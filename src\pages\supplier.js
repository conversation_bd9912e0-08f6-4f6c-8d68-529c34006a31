'use strict';

import React from 'react';
import ReactDOM from 'react-dom';
import TextSearch from "src/shared/components/TextSearch";
import Pagination from "src/shared/components/Pagination";

// Utility function to escape HTML characters
const escapeHtml = (text) => {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, (m) => map[m]);
};

const TABLE_ID = 'supplier';
const colsDefs = {
    'org_a': ['OrgA', '', true],
    'name': ['Supplier', '', true],
    'supp_supplier_id': ['WHC_Supplier', '', true],
    'action_btn': [''],
    'offers': ['Offers'],
    'created_on': ['Last Success', 'dt'],
    'updated_on': ['Last Contact', 'dt'],
    'created_by_disp_name': ['Last User'],
    'status': ['Status'],
    //'comment': ['Comment'],
    'top3_comments': ['Comment'],
    'top5_no_contacts': ['No Contacts'],
};
const colsWidth = {
    'org_a': 40,
    'name': 150,
    'supp_supplier_id': 120,
    'action_btn': 50,
    'offers': 300,
    'created_on': 70,
    'updated_on': 70,
    'created_by_disp_name': 40,
    'status': 70,
    'top3_comments': 200,
    'top5_no_contacts': 200,
};

const sortColsDefs = ['org_a', 'name', 'created_on', 'updated_on', 'status', 'created_by_disp_name'];

const TableRow = (props) => {
    const [loading, setLoading] = React.useState(false);
    const [item, setItem] = React.useState(props.item);
    const [isEditing, setIsEditing] = React.useState(false);
    const [domReady, setDomReady] = React.useState(false);

    React.useEffect(() => {
        setItem(props.item);
    }, [props.item]);

    React.useEffect(() => {
        dispose_tooltip($('.supplier-info'));
        init_tooltip($('.supplier-info'));
    }, [item.info_comment]);

    /**
     * Edit/Save action
     *
     * @param e
     */
    const handleEditButton = (e) => {
        e.preventDefault();
        props.onClickCreateTask(e, item);
    };

    const handleCellChange = (colName, value) => {
        setItem({...item, [colName]: value});
    };

    const handleCreateComment = (id) => {
        let options_html = '';
        const statusIds = Object.keys(statuses);
        let sc_customer_order_shown = false;
        let sc_customer_order_required = false;
        let comment_required = false;

        statusIds.forEach((statusId) => {
            const s = statuses[statusId];
            const default_checked = s.sc_default_position == 'Default' ? ' checked="checked"' : '';
            options_html += `
                <div class="form-check d-block">
                    <label class="form-check-label">
                        <input type="radio" name="status" id="status${statusId}" value="${statusId}" class="form-check-input status" 
                        ${default_checked}                           
                        /> ${s.name}
                    </label>                    
                </div>
            `;
            if (s.sc_default_position == 'Default' && s.sc_customer_order == '1') {
                sc_customer_order_shown = true;
            }
            if (s.sc_default_position == 'Default' && s.sc_contact == '1') {
                comment_required = true;
            }
            if (sc_customer_order_shown && s.sc_customer_order_required) {
                sc_customer_order_required = true;
            }
        });

        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm ${comment_required ? 'required' : ''}">Comment</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Offer</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon">
                            <input class="form-control form-control-sm offer_id" type="text" />
                        </div>                                                  
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-3 col-form-label-sm required">Status</label>
                    <div class="col-9">
                        ${options_html}                                                             
                    </div>
                </div> 
                <div class="form-group row customer_order-wrap ${sc_customer_order_shown ? '' : ' d-none'}">                
                    <label class="col-3 col-form-label-sm ${sc_customer_order_required ? ' required' : ''}">Customer Order</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon">
                            <input class="form-control form-control-sm customer_order" type="text" />
                        </div>
                    </div>
                </div>
            `;

        const dlgObj = bs4pop.dialog({
            title: `Create a Comment of '${item.name}'`,
            content: cont,
            className2: 'modal-dialog-scrollable',
            backdrop: 'static',
            width: 500,
            onShowEnd: () => {
                const $input = dlgObj.$el.find('.comment');
                $input.focus();

                // auto completion for an offer
                const $offer_id = dlgObj.$el.find('.offer_id');
                init_autocomplete($offer_id, {
                    class: 'OfferAction',
                    action: 'get_ac_offers',
                    supplier_id: id
                }, false, null, () => {
                }, (suggestion) => {
                });

                // auto completion for an order
                const $customer_order = dlgObj.$el.find('.customer_order');
                init_autocomplete($customer_order, {
                    class: 'OrderAction',
                    action: 'get_ac_orders',
                    supplier_id: id
                }, false, null, () => {
                }, (suggestion) => {
                });

                // change events for status checkboxes.
                dlgObj.$el.find('input[name="status"]').change((e) => {
                    const statusId = e.target.value;
                    const s = statuses[statusId] || null;

                    if (s != null) {
                        if (s.sc_customer_order == 1) {
                            dlgObj.$el.find('.customer_order-wrap').removeClass('d-none');
                        } else {
                            dlgObj.$el.find('.customer_order-wrap').addClass('d-none');
                        }
                        const comment_label = dlgObj.$el.find('textarea.comment').closest('.row').find('label');
                        if (s.sc_contact != 1) {
                            comment_label.removeClass('required');
                        } else {
                            comment_label.addClass('required');
                        }
                        if (s.sc_customer_order_required == 1) {
                            dlgObj.$el.find('.customer_order-wrap label').addClass('required');
                        } else {
                            dlgObj.$el.find('.customer_order-wrap label').removeClass('required');
                        }
                    }
                });

            },
            btns: [{
                label: 'New Offer...',
                className: 'btn-outline-info btn-sm mr-5',
                onClick: (evt) => {
                    /**
                     * Open a New offer dialog
                     */
                    const contNewOffer = `
                        <div class="form-group row">                
                            <label class="col-3 col-form-label-sm required">ID</label>
                            <div class="col-9">
                                <input class="form-control form-control-sm offer_sid" name="offer_sid" />                             
                            </div>
                        </div>
                        <div class="form-group row">                
                            <label class="col-3 col-form-label-sm">Name</label>
                            <div class="col-9">
                                <input class="form-control form-control-sm offer" name="offer" />                             
                            </div>
                        </div>
                        <div class="form-group row">                
                            <label class="col-3 col-form-label-sm">Supplier</label>
                            <div class="col-9">
                                <div class="input-wrap-with-icon">
                                    <input class="form-control form-control-sm supplier_id" type="text" value="${item.name}" selected-val="${item.id}" />
                                </div>                       
                            </div>
                        </div>
                    `;
                    const dlgNewOffer = bs4pop.dialog({
                        title: `Create an Offer`,
                        content: contNewOffer,
                        backdrop: 'static',
                        width: 400,
                        onShowEnd: () => {
                            const $input = dlgNewOffer.$el.find('.offer_sid');
                            $input.focus();
                            const $supplier_id = dlgNewOffer.$el.find('.supplier_id');
                            init_autocomplete($supplier_id, {
                                class: 'SupplierAction',
                                action: 'get_ac_suppliers',
                                exactName: '',
                            }, true, null, () => {
                            }, (suggestion) => {
                            });
                        },
                        btns: [{
                            label: 'Create',
                            className: 'btn-info btn-sm',
                            onClick: (evt) => {
                                const data = {
                                    supplier_id: dlgNewOffer.$el.find('.supplier_id').attr('selected-val'),
                                    offer_sid: dlgNewOffer.$el.find('.offer_sid').val(),
                                    offer: dlgNewOffer.$el.find('.offer').val(),
                                }
                                if (data.offer_sid.length < 1) {
                                    App.info('Please fill Offer ID.')
                                    return false;
                                }
                                createOffer(data, dlgNewOffer);
                                return false;       // we don't close dialog.
                            }
                        }, {
                            label: 'Close',
                            className: 'btn-secondary btn-sm',
                            onClick: e => {
                                e.hide();
                            }
                        }]
                    });
                    return false;
                }
            }, {
                label: 'Create',
                className: 'btn-info btn-sm',
                onClick: (evt) => {
                    // Getting customer order
                    let customer_order = '';
                    const statusId = dlgObj.$el.find('input[name="status"]:checked').val();
                    const s = statuses[statusId] || null;
                    if (s != null) {
                        if (s.sc_customer_order == 1)
                            customer_order = dlgObj.$el.find('.customer_order').attr('selected-val') || '';
                    }
                    const data = {
                        supplier_id: id,
                        comment: dlgObj.$el.find('.comment').val(),
                        status: dlgObj.$el.find('input[name="status"]:checked').val(),
                        offer_id: dlgObj.$el.find('.offer_id').attr('selected-val'),
                        customer_order: customer_order,
                    }
                    if ((s != null && s.sc_contact == 1) && data.comment.length < 1) {
                        App.info('Please fill comment.')
                        return false;
                    }
                    if (data.status.length < 1) {
                        App.info('Please select status.')
                        return false;
                    }
                    if (s != null && s.sc_customer_order == 1 && s.sc_customer_order_required == 1) {
                        if (customer_order.length < 1) {
                            App.info('Please fill the Customer Order.');
                            dlgObj.$el.find('.customer_order').focus();
                            return false;
                        }
                    }

                    createComment(data, dlgObj);
                    return false;
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    }

    const createComment = (comment, dlgObj) => {
        App.ajax_post_ok(get_ajax_url(), {
            class: 'SupplierAction',
            action: 'save_comment',
            data: comment,
        }, function (res) {
            if (res.error == false) {
                setItem(res.data.row);
                dlgObj.hide();
            }
        }, dlgObj.$el);
    };

    const updateInfo = () => {
        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Comment</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm comment" rows="8">${item.info_comment || ''}</textarea>                             
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">ASP</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm info_asp" rows="6">${item.info_asp || ''}</textarea>                             
                    </div>
                </div>
        `;

        const dlgUpdateInfo = bs4pop.dialog({
            title: `Update Supplier Info`,
            content: cont,
            backdrop: 'static',
            width: 600,
            onShowEnd: () => {
                const $input = dlgUpdateInfo.$el.find('.comment');
                $input.focus();
            },
            btns: [{
                label: 'Update',
                className: 'btn-info btn-sm',
                onClick: (evt) => {
                    App.ajax_post_ok(get_ajax_url('SupplierAction', 'update_info'), {
                        supplier_id: item.id,
                        comment: dlgUpdateInfo.$el.find('.comment').val(),
                        asp: dlgUpdateInfo.$el.find('.info_asp').val(),
                    }, function (res) {
                        if (res.error == false) {
                            setItem({...item, info_comment: res.data.row.comment, info_asp: res.data.row.asp});
                            dlgUpdateInfo.hide();
                            dispose_and_init_tooltip($('#tr' + item.id).find('.tt'));
                        }
                    }, dlgUpdateInfo.$el);
                    return false;       // we don't close dialog.
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    };

    const createOffer = (offer, dlgObj) => {
        App.ajax_post_ok(get_ajax_url(), {
            class: 'OfferAction',
            action: 'save',
            data: offer,
        }, function (res) {
            if (res.error == false) {
                dlgObj.hide();
            }
        }, dlgObj.$el);
    };

    // We color the row by latest updated_date
    let trCls = '';
    if (item != null && item.updated_on != null) {
        const m = moment(item.updated_on).add(-tzLocalOffset - tzServerOffset);
        if (m.isValid()) {
            const days = -m.diff(moment(), 'days');
            if (days > 20) {
                trCls = 'light-pink';
            } else if (days > 10) {
                trCls = 'light-green';
            }
        }
    }


    // =================================================================================================
    // Table Cell
    // =================================================================================================
    const viewOffers = () => {
        // Open a modal dialog
        setDlgTitle($g_dlg, item.name + "'s Offers");
        setDlgBody($g_dlg, no_result);
        $g_dlg.modal({
            'backdrop': 'static',
            'show': true
        });

        App.ajax_post_ok(get_ajax_url('SupplierAction', 'view_offers'), {
            supplier_id: item.id,
        }, function (res) {
            if (!res.error) {
                setDlgBody($g_dlg, res.data.html);
            }
        }, $g_dlg);
    };

    const viewComments = (type) => {
        // Open a modal dialog
        setDlgTitle($g_dlg, item.name + "'s Comments");
        setDlgBody($g_dlg, no_result);
        $g_dlg.modal({
            'backdrop': 'static',
            'show': true
        });

        App.ajax_post_ok(get_ajax_url('SupplierAction', 'view_comments'), {
            supplier_id: item.id,
            type: type || ''
        }, function (res) {
            if (!res.error) {
                setDlgBody($g_dlg, res.data.html);
            }
        }, $g_dlg);
    };

    const getValue = (isEditing, colName) => {
        const type = RHelper.getColType(colName, colsDefs);
        const value = item[colName];

        let val = value;
        if (colName == 'updated_on') {
            if (typeof isEditing != 'undefined' && !isEditing)
                val = value != null ? CvUtil.dtAgo(value) : '';
        } else if (colName == 'status') {
            val = (value in statuses) ? statuses[value].name : '';
        } else if (colName == 'created_on') {
            val = CvUtil.dtNiceDMY(value);
        } else if (colName == 'supp_supplier_id') {
            val = item.supp_supplier?.name || '';
        }
        return val;
    };
    const handleChange = (e) => {
        let {name, value} = e.target;
        handleCellChange(name, value);
    };

    const getTDHtml = (colName) => {
        const value = getValue(isEditing, colName);
        const colType = RHelper.getColType(colName, colsDefs);
        const isEditableCol = RHelper.isEditable(colName, colsDefs);
        const statusIds = Object.keys(statuses);
        const isNumericCol = colType == 'd' || colType == 'i';
        const cls = "form-control form-control-sm input-edit fw-80" + (isNumericCol ? ' text-right' : '');

        let value_html = '';
        let td_div_cls = colName;
        if (isEditing && isEditableCol) {
            value_html = colName != 'status' ? (
                    <input className={cls} type="text" disabled={loading} value={getValue(isEditing, colName, item[colName])} onChange={handleChange}/>
                ) :
                (
                    <select className={cls} value={item[colName] || ''} onChange={handleChange}>
                        <option value=""></option>
                        {
                            statusIds.map((statusId, ind) => (<option key={statusId} value={statusId}>{statuses != null ? (statuses[statusId + ''] || '') : ''}</option>))
                        }
                    </select>
                );
        } else {
            value_html = getValue(false, colName);

            // Getting value's HTML content.
            switch (colName) {
                case 'org_a':
                case 'supp_supplier_id':
                    td_div_cls += ' text-left';
                    break;
                case 'created_on':
                    td_div_cls += ' text-center fs-z8';
                    break;
                case 'updated_on':
                    value_html = <span className="badge badge-secondary">{value_html}</span>;
                    td_div_cls += ' text-center';
                    break;
                case 'status':
                    value_html = <span className="badge badge-info">{value_html}</span>;
                    td_div_cls += ' text-center';
                    break;
                case 'top5_no_contacts':
                    value_html = <React.Fragment>
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-4 pr-0">{CvUtil.dtNiceDMYShort(c.created_on, true)} ({c.created_by_name})</div>
                            <div className="col-8">
                                {c.comment}
                                <span className="c-lightgrey ml-2">{c.order_id}</span>
                            </div>
                        </div>)}
                        <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            <i className="oi oi-info m-0" onClick={e => {
                                viewComments('no_contacts')
                            }} title="Please click to view all 'Nicht erreicht' comments."></i>
                        </div>
                    </React.Fragment>;
                    td_div_cls += ' position-relative fs-z6';
                    break;
                case 'top3_comments':
                    value_html = <React.Fragment>
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-3 pr-0">{CvUtil.dtNiceDM(c.created_on)}  ({c.created_by_name})</div>
                            <div className="col-9">
                                {c.comment}
                                <span className="c-lightgrey ml-2">{c.customer_order}</span>
                            </div>
                        </div>)}
                        <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            <i className={"oi oi-info m-0" + (value && value.length > 0 ? " blue-f" : "")} onClick={e => {
                                viewComments('contacts')
                            }} title="Please click to view all comments."></i>
                        </div>
                    </React.Fragment>;
                    td_div_cls += ' position-relative fs-z6';
                    break;
                case 'offers':
                    value_html = <React.Fragment>
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className={"col-4 tt" + (c.status == '1' ? '' : ' c-lightgrey')} title={c.offer_comments}>
                                <a href={base_url + '/offer.php?offer_sid=' + c.offer_sid} target="_blank">{c.offer_sid}</a>
                            </div>
                            <div className={"col-6 tt-ajax" + (c.status == '1' ? '' : ' c-lightgrey')} id={c.offer_sid} data-offer_sid={c.offer_sid}>{c.offer}</div>
                            <div className={"col-2 fs-z6 tt-ajax" + (c.status == '1' ? '' : ' c-lightgrey')} id={c.offer_sid} data-offer_sid={c.offer_sid}>{c.updated_on? `[${CvUtil.dtNiceDM(c.updated_on)}]` : ''}</div>
                        </div>)}
                        <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            <i className="oi oi-info m-0" onClick={viewOffers} title="Please click to view all offers"></i>
                        </div>
                    </React.Fragment>;
                    td_div_cls += ' position-relative fs-z8';
                    break;
                case 'comment':
                    value_html = <span style={{'cursor': 'pointer'}} onClick={e => viewComments('')}>{value_html}</span>;
                    td_div_cls += ' ';
                    break;
                case 'created_by_disp_name':
                    td_div_cls += ' fs-z6 text-center';
                    break;
                case 'name':
                    const info_comment = _.get(item, 'info_comment', '');
                    value_html = <React.Fragment>
                        <span className="supplier-info tt" title={item.info_asp || ''}> {value_html}</span>
                        <i className={"oi oi-info m-0 float-right" + (info_comment && info_comment.length > 0 ? " blue-f" : "")} onClick={e => updateInfo()} title="Please click to update supplier information."></i>
                    </React.Fragment>;
                    td_div_cls += ' ';
                    break;
            }
        }

        return (
            <div className={td_div_cls + (isNumericCol ? " text-right" : "")} style={{width: RHelper.getColWidth(colName, colsWidth) || 'auto'}}>
                {value_html}
            </div>
        );
    };

    return (
        <tr id={"tr" + item.id} className={trCls}>
            {props.cols.map((col, ind) => (
                (col !== 'action_btn') ?
                    <td key={col}>
                        {getTDHtml(col)}
                    </td> :
                    <td key={ind}>
                        <div className="text-center" style={{width: 30}}>
                            <i className="oi oi-comment-square action tt" title="Create a comment..." onClick={e => handleCreateComment(item.id)}></i>
                            {/*<i className="oi oi-delete action tt" title="Delete a supplier..." onClick={(e) => props.handleRowDelete(item)}></i>*/}
                        </div>
                    </td>))}
            {App.has_perm('Supplier Edit') && <td>
                <i className="oi oi-pencil oi-edit act-update-supplier tt m-0" title="Update supplier..." onClick={e => props.onClickCreateSupplier(e, item)}></i>
            </td>}
        </tr>
    );
};

const TableSearchRow = ({loading, searchData}) => {
    const [row, setRow] = React.useState({});

    const onChange = (e) => {
        const {name, value} = e.target;
        setRow({...row, [name]: value});
        searchData({...row, [name]: value});
    };

    const cols = Object.keys(colsDefs);
    const statusIds = Object.keys(statuses);
    let inputCls = "form-control form-control-sm";

    return (
        <tr id="row-search" className={"row-search" + (loading ? ' loading' : '')}>
            {cols.map((col, ind) => (
                col !== 'action_btn' ?
                    <td key={col}>
                        {(col == 'top3_comments' || col == 'name' || col == 'status') &&
                        <div style={{width: _.get(colsWidth, col, 'auto')}}>
                            {col != 'status' ?
                                <TextSearch className={inputCls}
                                       name={col}
                                       value={col in row ? row[col] : ''}
                                       onChange={onChange}
                                /> :
                                <select className={inputCls}
                                        name={col}
                                        /*readOnly={loading}*/
                                        value={col in row ? row[col] : ''}
                                        onChange={onChange}
                                >
                                    <option value=""></option>
                                    {statusIds.map((statusId, ind) => (
                                        <option key={statusId} value={statusId}>{statuses != null ? (statuses[statusId + ''].name || '') : ''}</option>
                                    ))}
                                </select>
                            }
                        </div>
                        }
                    </td> :
                    <td key={col}></td>
            ))}
            {App.has_perm('Supplier Edit') && <td></td>}
        </tr>
    );
};

function SearchForm(props) {
    React.useEffect(() => {
    }, [props]);

    const orgAList = props.orgAListAll;
    return (
        <div className="card border-0 bg-transparent form-card">
            <div className="card-body p-0">
                <div className="form-row">
                    <div className="col-auto">
                        <label className="mt-1">OrgA:</label>
                        {orgAList.map((a, ind) => (
                            <div key={'orgAList' + a} className="form-check d-inline-block ml-3">
                                <input type="checkbox" name="orgAList[]" id={'orgAList' + a} className="form-check-input"
                                       value={a}
                                       readOnly={props.loading}
                                       checked={_.indexOf(props.form.orgAList, a) >= 0}
                                       onChange={e => props.onSearchFormChange('orgAList', e.target.value, e.target.checked)}
                                />
                                <label htmlFor={'orgAList' + a} className="form-check-label">{a || ' - '}</label>
                            </div>
                        ))}
                    </div>
                    <div className="col-auto ml-3">
                        <label>No contact since</label>
                        <TextSearch type="text" className="form-control form-control-sm text-right fw-50 d-inline-block mx-2"
                               value={_.get(props, 'form.no_contact_since', '')}
                               onChange={e => props.onSearchFormChange('no_contact_since', e.target.value)}
                        />
                        <label>days</label>
                    </div>
                    <div className="col-auto ml-3">
                        <label>Contact in last </label>
                        <TextSearch type="text" className="form-control form-control-sm text-right fw-50 d-inline-block mx-2"
                               value={_.get(props, 'form.contact_last_hour', '')}
                               onChange={e => props.onSearchFormChange('contact_last_hour', e.target.value)}
                        />
                        <label>hours</label>
                    </div>
                    <div className="col-auto ml-3 mt-1">
                        <div className="form-check d-inline-block">
                            <input type="checkbox" name="allOldOffers" id={'allOldOffers'} className="form-check-input"
                                   value={1}
                                   readOnly={props.loading}
                                   checked={props.form.allOldOffers == '1'}
                                   onChange={e => props.onSearchFormChange('allOldOffers', e.target.value, e.target.checked)}
                            />
                            <label htmlFor={'allOldOffers'} className="form-check-label">All Old Offers</label>
                        </div>
                    </div>
                    <div className="col-auto ml-3 mt-1">
                        <div className="form-check d-inline-block">
                            <input type="checkbox" name="null_supp_supplier_id" id={'null_supp_supplier_id'} className="form-check-input"
                                   value={1}
                                   readOnly={props.loading}
                                   checked={props.form.null_supp_supplier_id == '1'}
                                   onChange={e => props.onSearchFormChange('null_supp_supplier_id', e.target.value, e.target.checked)}
                            />
                            <label htmlFor={'null_supp_supplier_id'} className="form-check-label">No WHC_Supplier ID?</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

/**
 * React component.
 *
 * @param props
 * @returns {*}
 * @constructor
 */
function Supplier(props) {
    // State hooks
    const [initFloatThead, setInitFloatThead] = React.useState(true);
    const [loading, setLoading] = React.useState(true);
    const [isLoaded, setIsLoaded] = React.useState(false);

    // Settings
    const [orgAList, setOrgAList] = React.useState(props.settings.orgAList);

    // Search Form
    const [form, setForm] = React.useState({...props.defaultSearchForm, orgAList: orgAList? [orgAList[0]] : []});

    // Order by
    const [order_field, setOrderField] = React.useState(props.defaultOrder.field);
    const [order_dir, setOrderDir] = React.useState(props.defaultOrder.dir);

    // result data
    const [items, setItems] = React.useState([]);
    const [sql, setSql] = React.useState('');

    // pagination
    const pagerDefault = {
        page: 0,
        pageCount: 0
    };
    const [pager, setPager] = React.useState(pagerDefault);


    // Note: the empty deps array [] means
    // this useEffect will run once
    // similar to componentDidMount()
    React.useEffect(() => {
        getDataList({...form, with: 'form'});
    }, []);
    React.useEffect(() => {
        if(isLoaded) {
            if (loading) {
                wait_icon($('#list-loading'));
            } else {
                hide_wait_icon($('#list-loading'));
            }
        }
    }, [loading]);

    React.useEffect(() => {
        $('#' + TABLE_ID).floatThead({autoReflow: true});
    }, [initFloatThead]);

    React.useEffect(() => {
        if (isLoaded) {
            onRefresh(null);
        }
    }, [order_dir, order_field]);

    React.useEffect(() => {
        if (isLoaded) {
            onRefresh(null);
        }
    }, [form]);

    React.useEffect(() => {
        if (isLoaded) {
            onRefresh(null);
        }
    }, [pager.page]);

    /**
     * Change events on Search form
     *
     * @param name
     * @param value
     */
    const onSearchFormChange = (name, value, checked) => {
        if (name == 'orgAList') {
            if (checked) {
                setForm({...form, [name]: [value]});
            }
            /*let orgAList = [...form.orgAList].filter(x => x != value);
            if (checked) orgAList.push(value);
            setForm({...form, [name]: orgAList});*/
        } else if (name == 'allOldOffers' || name == 'null_supp_supplier_id') {
            setForm({...form, [name]: checked ? 1: 0});
        } else {
            setForm({...form, [name]: value});
        }
    };

    const searchData = (data) => {
        setLoading(true);
        data = Object.assign({}, form, data);
        getDataList(data);
    };

    const getDataList = (params) => {
        let post_data = {pager};
        Object.assign(post_data, params);

        // ordering
        post_data['order_field'] = order_field;
        post_data['order_dir'] = order_dir;
        post_data['inc_supp_supplier'] = 'supp_supplier';

        App.ajax_post(get_ajax_url('SupplierAction', 'get_list_with_form_data'), post_data, function (res) {
            setIsLoaded(true);
            setLoading(false);
            setResponseData(res.data);
            setInitFloatThead(false);
        }, function (res) {
            setIsLoaded(true);
        });
    };

    const setResponseData = (data) => {
        setItems(data['rows']);
        setPager(_.get(data, 'pager', pagerDefault));
        setSql(data['sql']);
        init_tooltip();
    };

    const handleRowCreate = (item, $dlg, cb) => {
        setLoading(true);
        App.ajax_post(get_ajax_url(), {
            class: 'Supplier',
            action: 'save',
            data: item,
        }, function (res) {
            setLoading(false);
            if (!res.error) {
                if (item.id == '') {
                    let x = [res.data.row, ...items];
                    setItems(x);
                } else {
                    setItems(items.map(el => (el.id === item.id ? Object.assign({}, el, res.data.row) : el)));
                }
                $dlg.hide();
                if (cb) cb();
            }
        }, function (res) {
            setLoading(false);
        });
    };
    const handleRowDelete = (item) => {
        bs4pop.confirm(`Are you sure you want to delete '${item.name}'?`, function (sure) {
            if (sure) {
                App.ajax_post(get_ajax_url(), {
                    class: 'SupplierAction',
                    action: 'delete',
                    id: item.id,
                }, function (res) {
                    if (res.error == false) {
                        setItems(items.filter(function (obj) {
                            return obj.id != item.id;
                        }));
                    }
                }, function (res) {
                });
            }
        }, {title: 'Delete a Supplier'});
    };
    const onRefresh = (e) => {
        let params = {};
        $('.row-search input, .row-search select').each((ind, ele) => {
            params[ele.name] = ele.value;
        });
        searchData(params);
    };

    const onClickCreateSupplier = (e, item) => {
        const isNew = _.isUndefined(item);
        if (isNew) {
            item = {};
        }
        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">WHC_Supplier</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon">
                            <input class="form-control form-control-sm supp_supplier_id" type="text" name="supp_supplier_id" value="${escapeHtml(item.supp_supplier?.name || '')}" selected-val="${item.supp_supplier?.id || ''}" />
                        </div>                                                  
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Supplier</label>
                    <div class="col-9">
                        <input class="form-control form-control-sm" name="name" type="text" value="${item.name || ''}" />                                     
                    </div>
                </div> 
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">OrgA</label>
                    <div class="col-9">
                        <input class="form-control form-control-sm fw-30" name="org_a" type="text" maxlength="1" value="${item.org_a || ''}" />                                     
                    </div>
                </div>
            `;


        const dlgObj = bs4pop.dialog({
            title: isNew ? 'Create a Supplier' : 'Update a Supplier',
            content: cont,
            backdrop: 'static',
            width: 600,
            onShowEnd: () => {
                const $input = dlgObj.$el.find('input:first');
                $input.focus();

                // auto completion for an offer
                const $supp_supplier_id = dlgObj.$el.find('.supp_supplier_id');
                init_autocomplete($supp_supplier_id, {
                    class: 'SuppSupplierAction',
                    action: 'get_ac_suppliers'
                }, !!item.supp_supplier?.id, null, () => {
                }, (suggestion) => {
                    if (isNew) {
                        dlgObj.$el.find('[name="name"]').val(suggestion.name || '')
                        dlgObj.$el.find('[name="org_a"]').val(suggestion.org_a || '')
                    }
                });
            },
            btns: [{
                label: isNew ? 'Create' : 'Update',
                onClick: (evt) => {
                    const $el = dlgObj.$el;
                    const postData = {
                        id: item.id || '',
                        name: $el.find('[name="name"]').val(),
                        org_a: $el.find('[name="org_a"]').val(),
                        supp_supplier_id: $el.find('[name="supp_supplier_id"]').attr('selected-val') || '',
                    };

                    if (!postData.supp_supplier_id) {
                        App.error('Please select WHC_Supplier');
                        return false;
                    }
                    if (!postData.name) {
                        App.error('Please fill name');
                        return false;
                    }
                    
                    handleRowCreate(postData, dlgObj, onRefresh);
                    return false;
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    };

    const changeOrderField = (col) => {
        if (!RHelper.isSortable(col, sortColsDefs)) return;
        if (col == order_field) {
            setOrderDir(prev => prev == 'desc' ? 'asc' : 'desc');
        } else {
            setOrderField(col);
            setOrderDir('asc');
        }
    }

    const handlePageClick = (data) => {
        const merged = {...pager, page: data.selected};
        setPager(merged);
    };

    const cols = Object.keys(colsDefs);

    return (
        <React.Fragment>
            <SearchForm
                form={form}
                setForm={setForm}
                loading={loading}
                setLoading={setLoading}
                searchData={searchData}
                orgAListAll={orgAList}
                handleCreate={handleRowCreate}
                onSearchFormChange={onSearchFormChange}
            />

            <h4>{items ? `Results (${items.length} records)` : "No Results"}
                <button className='btn-sql-view btn btn-light btn-sm'>Show/Hide SQL</button>
            </h4>
            <div className='sql-log-wrap'>
                <pre>{sql}</pre>
            </div>

            <div className="table-wrap position-relative">
                <div className="table-btn-wrap position-absolute" style={{top: -40, left: 450}}>
                    <button className="btn btn-sm btn-info mr-4" onClick={onRefresh} disabled={props.loading}>Refresh</button>
                    <button className="btn btn-sm btn-success" onClick={onClickCreateSupplier} disabled={props.loading}>Create Supplier</button>
                    <span className="ml-5">{loading? "Loading ..." : ""}</span>
                </div>
                <table className="data-table editable border-0" id={TABLE_ID} style={{minWidth: 500}}>
                    <thead>
                    <tr>
                        {cols.map((col, ind) =>
                            ('action_btn' !== col) ?
                                <th key={col}
                                    className={RHelper.isSortable(col, sortColsDefs) ? ' icon-order-wrap' : ''}
                                    onClick={e => changeOrderField(col)}
                                >
                                    <div style={{width: _.get(colsWidth, col, 'auto'), wordWrap: 'break-word'}}>
                                        {RHelper.getColName(col, colsDefs)}
                                        {RHelper.isSortable(col, sortColsDefs) && order_field == col && <a className={"icon-order " + (order_dir)} href="#"></a>}
                                    </div>
                                </th> :
                                <th key={col}></th>
                        )}
                        {App.has_perm('Supplier Edit') && <th></th>}
                    </tr>
                    </thead>
                    <tbody>
                    <TableSearchRow
                        loading={loading}
                        searchData={searchData}
                    />

                    {items != null && items.map((item, ind) => (
                        <TableRow key={item.id}
                                  isTotal={false}
                                  item={item}
                                  cols={cols}
                                  loading={loading}
                                  handleRowDelete={handleRowDelete}
                                  onClickCreateSupplier={onClickCreateSupplier}
                        />
                    ))}
                    </tbody>
                </table>
                <Pagination
                    pager={pager}
                    onPageChange={handlePageClick}
                />
            </div>
        </React.Fragment>
    );
}

// Setting up Props.
let props = typeof SupplierProps !== "undefined" ? SupplierProps : (typeof App !== 'undefined' ? App.get_params(window.location) : {});
ReactDOM.render(
    React.createElement(Supplier, props),
    document.getElementById('root')
);
