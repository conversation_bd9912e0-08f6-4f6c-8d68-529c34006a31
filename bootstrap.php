<?php

header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

use app\SysMsg\SysMsg;

require_once 'vendor/autoload.php';

use Dotenv\Dotenv;

if (file_exists(__DIR__ . DIRECTORY_SEPARATOR . ".env")) {
    $dotenv = Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

require_once 'config.php';

require_once APP_PATH . 'constants.php';
require_once APP_PATH . 'auth.php';

// ----------------------------------------------------------------------- //
// Loading libraries
// ----------------------------------------------------------------------- //
require_once LIB_PATH . 'SysMsg.php';
require_once LIB_PATH . 'Auth.php';
require_once LIB_PATH . 'SimDB.php';
require_once LIB_PATH . 'SimDBSupp.php';
require_once LIB_PATH . 'Logger.php';
require_once LIB_PATH . 'Pagination.php';
require_once LIB_PATH . 'Loader.php';

// ----------------------------------------------------------------------- //
// Basic Models, helpers, actions
// ----------------------------------------------------------------------- //
require_once $APP_PATH . 'models'. DS . 'BaseModel.php';
require_once $APP_PATH . 'models'. DS . 'BaseModelSupp.php';
require_once $APP_PATH . 'models'. DS . 'SysConfigModel.php';
require_once $APP_PATH . 'models'. DS . 'UserModel.php';

require_once $APP_PATH . 'helpers' . DS . 'global_helper.php';
require_once $APP_PATH . 'api'. DS . 'BaseAction.php';

// ----------------------------------------------------------------------- //
// Initializing the library instances.
// ----------------------------------------------------------------------- //
$db =& SimDB::get_instance();
$db->LOGS_PATH = $LOG_SUB_PATH;
$db->initialize($DB_HOST, $DB_USER, $DB_PASSWORD, $DB_NAME, $DB_PORT);

// Logger
$logger = new Logger(APP_PATH . $LOG_SUB_PATH . DS . "app_" . date("Y_m_d") . ".log", $LOG_LEVEL);

// SysMsg
$msg =& SysMsg::get_instance();

// Track necessary SQL queries.
$sqls = array();

delete_backup_files();
