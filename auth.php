<?php
defined('APP_PATH') OR exit('No direct script access allowed');

require_once 'vendor/autoload.php';

session_name('WHCOrg-Dashboard');
session_start();

$user = isset($_SESSION['user']) ? $_SESSION['user'] : NULL;

$b_url = $_SERVER['SCRIPT_NAME'];
$script = substr($b_url, strrpos($b_url, '/') + 1);

/**
 * If user is not logged in and tries to view other pages, we will redirect to login page "index.php"
 */
if (!$user && ($script != 'index.php' && $script != 'index_b.php')) {
    header("Location: $BASE_URL/index.php");
    exit;
}

require_once 'lib/SysMsg.php';
require_once $APP_PATH . 'models'. DS . 'BaseModel.php';
require_once $APP_PATH . 'models'. DS . 'UserModel.php';

/**
 * Logout Action
 */
if (isset($_GET['action']) && $_GET['action'] == "logout") {
    require_once 'lib/SimDB.php';
    require_once 'lib/Auth.php';

    if (!empty($_SESSION)) {
        foreach ($_SESSION as $key => $value) {
            unset($_SESSION[$key]);
        }
    }

    $db = &SimDB::get_instance();
    $db->initialize($DB_HOST, $DB_USER, $DB_PASSWORD, $DB_NAME, $DB_PORT);

    Auth::log_access_info('logout');

    $return_url = $_REQUEST['return_url'] ?? 'index.php';
    header("Location: $return_url");

    exit;
}

/**
 * Login action
 * Accept the POST request only.
 */
if (isset($_POST['action']) && $_POST['action'] == "login") {
    require_once 'constants.php';
    require_once 'lib/SimDB.php';
    require_once APP_PATH . 'models' . DS . 'BaseModel.php';
    require_once 'lib/Auth.php';

    $db = &SimDB::get_instance();
    $db->initialize($DB_HOST, $DB_USER, $DB_PASSWORD, $DB_NAME, $DB_PORT);
    $db->LOGS_PATH = $LOG_SUB_PATH;
    $row = $db->query_row(sprintf("SELECT * FROM users WHERE  username='%s' AND password='%s'", $db->get_escape_string($_POST['username']), $db->get_escape_string($_POST['password'])));

    if ($row) {
        $_SESSION['user'] = $row;
        Auth::log_access_info('login');

        $return_url = $_REQUEST['return_url'] ?? 'index.php';
        header("Location: $return_url");
        exit;
    }

    Auth::log_access_info('login');
}

