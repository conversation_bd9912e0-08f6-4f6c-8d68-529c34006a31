<?php
require_once '_admin_includes.php';

require_once APP_PATH . 'models' . DS . 'UserModel.php';
$user_model =& UserModel::get_instance();

$uid = $_GET['id'] ?? 0;

// -------------------------------------------------------------------- //
// Validation Checking
// -------------------------------------------------------------------- //

$u = array();

// -------------------------------------------------------------------- //
// POST request handling
// -------------------------------------------------------------------- //

if (isset($_POST['btn_submit'])) {
    $u['username'] = trim($_POST['username']);
    $u['password'] = $_POST['password'];
    $u['display_name'] = $_POST['display_name'];

    if ($u['username']) {
        if ($u['password']) {
            if ($user_model->insert($u))
                $msg->success("Added new user '{$u['username']}' successfully.", $ADMIN_URL . "/users.php");
            else
                $msg->error('Failed to create a new user.');
        } else {
            $msg->error("Password field is required.");
        }
    } else {
        $msg->error("Username field is required.");
    }
}

// -------------------------------------------------------------------- //
// Start to render the page
// -------------------------------------------------------------------- //

$title = "Add user";
require_once APP_PATH . 'layout/header.php';

?>
    <form id="admin-form" class="mt-4" action="<?php echo $_SERVER['PHP_SELF'] ?>" method="post">
        <div class="card w-75">
            <div class="card-header">
                <h4 class="float-left mb-0">User information</h4>
                <div class="card-header-pills float-right">
                    <button type="submit" name="btn_submit" class="btn btn-info btn-sm">Submit</button>
                    <a href="<?php echo $ADMIN_URL . "/users.php" ?>" class="btn btn-outline-secondary btn-sm">Cancel</a>
                </div>
            </div>
            <div class="card-body">
                <div class="form-group row">
                    <label for="username" class="col-form-label col-sm-2">Username</label>
                    <div class="col-sm-9">
                        <input type="text" required id="username" name="username" class="form-control w-auto" value="<?php echo $u['username'] ?? '' ?>"/>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="username" class="col-form-label col-sm-2">Display Name</label>
                    <div class="col-sm-9">
                        <input type="text" required id="display_name" name="display_name" class="form-control w-auto" value="<?php echo $u['display_name'] ?? '' ?>" maxlength="20"/>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="password" class="col-form-label col-sm-2">Password</label>
                    <div class="col-sm-9">
                        <input type="password" id="password" name="password" class="form-control w-auto" required value=""/>
                    </div>
                </div>
            </div>
        </div>
    </form>
<?php

require_once APP_PATH . 'layout/footer.php';
