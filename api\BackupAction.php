<?php

use app\SysMsg\SysMsg;
use Ifsnop\Mysqldump as IMysqldump;


class BackupAction extends BaseAction
{
    private $compressManager = null;

    function __construct()
    {
        parent::__construct();
    }

    public function get_dump_object($dump_settings)
    {
        $DB_HOST = getenv('DB_HOST');
        $DB_USER = getenv('DB_USER');
        $DB_PASSWORD = getenv('DB_PASSWORD');
        $DB_NAME = getenv('DB_NAME');
        $DB_PORT = getenv('DB_PORT');

        $dump = new IMysqldump\Mysqldump("mysql:host={$DB_HOST}:{$DB_PORT};dbname={$DB_NAME}", $DB_USER, $DB_PASSWORD, $dump_settings);
        return $dump;
    }

    public function backup()
    {
        $this->post_restrict();
        $this->msg = '';

        try {
            $dump_settings = [
                'compress' => IMysqldump\Mysqldump::GZIP,
                'add-drop-table' => true,
                'add-locks' => false,
                'routines' => true,
                'extended-insert' => true,  // batch insert mode.
            ];
            $dump = $this->get_dump_object($dump_settings);
            $file_name = "whcorg_db_" . date("Ymd") . "_" . time() . ".gz";
            $dump->start(APP_PATH . 'data' . DS . 'backup' . DS . $file_name);
            $this->data['download_url'] = base_url() . "/download.php?type=backup&name=" . $file_name . "&return_url=" . "import.php";
        } catch (Exception $e) {
            $this->error = true;
            $this->msg = 'mysqldump-php error: ' . $e->getMessage();
        }

        $this->return();
    }

    public function backup_transactions_2()
    {
        $this->post_restrict();
        $this->msg = '';
        try {
            $dump_settings = [
                'compress' => IMysqldump\Mysqldump::NONE,
                'no-create-info' => true,
                'add-drop-table' => false,
                'add-locks' => false,
                'routines' => false,
                'complete-insert' => true,
                'extended-insert' => true,  // batch insert mode.
                'skip-triggers' => true,
                'skip-tz-utc' => true,
                'skip-comments' => true,
                'skip-dump-date' => true,
                'skip-definer' => true,
                'include-tables' => [
                    BaseModel::TBL_SYS_SYNC_TRANSACTION
                ],
            ];
            $dump = $this->get_dump_object($dump_settings);

            $file_name = "db_sync_" . date("Ymd") . "_" . time() . ".sql";
            $dump->start(APP_PATH . 'data' . DS . 'backup' . DS . $file_name);
            $file_name = $this->encrypt_file($file_name);
            $this->data['download_url'] = base_url() . "/download.php?type=backup&name=" . $file_name . "&return_url=" . "import.php";
        } catch (Exception $e) {
            $this->error = true;
            $this->msg = 'mysqldump-php error: ' . $e->getMessage();
        }
        $this->return();
    }

    /**
     * Backup transaction table into .json file.
     *
     */
    public function backup_transactions()
    {
        $this->post_restrict();
        $this->msg = '';
        $MAX_BUFFER = 150;

        $sc_model =& SysConfigModel::get_instance();
        $base_path = APP_PATH . 'data' . DS . 'backup';
        $export_path = $_POST['export_path'] ?? '';
        $use_export_path = $_POST['use_export_path'] ?? false;
        if ($use_export_path) {
            if (!$export_path) $this->return(true, 'Please fill export path.');
            /*if (strpos($export_path, '/') === 0) {
                $base_path = $export_path;
            } else {
                $base_path = APP_PATH . DS . $export_path;
            }*/
            $base_path = $export_path;
            if (!file_exists($base_path)) {
                $this->return(true, 'Server path is invalid. You specified ' . $export_path);
            }
            if (!is_writable($base_path)) {
                $this->return(true, 'Server path is not writable. You specified ' . $export_path);
            }
            $sc_model->update_where(['value' => $export_path], $this->db->where_equal('code', SysConfigModel::CODE_EXPORT_PATH, ''));
        }

        try {
            $file_name = "WOS_" . BaseModel::get_location_id() . "_" . date("Ymd") . "_" . time() . ".bak";
            $file_path = $base_path . DS . $file_name;

            // bzip2 lib.
            require_once APP_PATH . '/vendor/ifsnop/mysqldump-php/src/Ifsnop/Mysqldump/Mysqldump.php';
            $this->compressManager = IMysqldump\CompressManagerFactory::create(IMysqldump\Mysqldump::BZIP2);

            // Create output file
            $result = $this->compressManager->open($file_path);
            if ($result) {
                $this->compressManager->write('[');

                $where = '';
                $sql = "
                    SELECT * FROM `" . BaseModel::TBL_SYS_SYNC_TRANSACTION . "` WHERE 1 $where
                    ORDER BY created_on
                    ";
                $this->db->query($sql);
                $last_link =& $this->db->get_last_link();
                $buffer = '';
                $prefix = '';
                while ($r = $this->db->fetch_array($last_link)) {
                    $buffer .= $prefix . json_encode($r);
                    $prefix = CRLF . ',';
                    $this->compressManager->write($buffer);
                    $buffer = '';
                }
                $this->db->free_result();

                $this->compressManager->write(']');
                $this->compressManager->close();

                $file_name = $this->encrypt_file($file_name);
                $this->data['download_url'] = base_url() . "/download.php?type=backup&name=" . $file_name . "&return_url=" . "import.php";
                $this->data['file_path'] = $file_path;
            }
        } catch (Exception $e) {
            $this->error = true;
            $this->msg = 'Error: ' . $e->getMessage();
        }

        $this->return();
    }

    /**
     * Backup transaction table into .json file.
     *
     */
    public function backup_transactions_json()
    {
        $this->post_restrict();
        $this->msg = '';
        $MAX_BUFFER = 150;

        try {
            $file_name = "WOS_" . BaseModel::get_location_id() . "_" . date("Ymd") . "_" . time() . ".bak";
            $file_path = APP_PATH . 'data' . DS . 'backup' . DS . $file_name;
            $fp = @fopen($file_path, 'w');
            if ($fp) {
                fwrite($fp, '[');

                $where = '';
                $sql = "
                    SELECT * FROM `" . BaseModel::TBL_SYS_SYNC_TRANSACTION . "` WHERE 1 $where
                    ";
                $this->db->query($sql);
                $last_link =& $this->db->get_last_link();
                $buffer = '';
                $prefix = '';
                while ($r = $this->db->fetch_array($last_link)) {
                    $buffer .= $prefix . json_encode($r);
                    $prefix = CRLF . ',';
                    fwrite($fp, $buffer);
                    $buffer = '';
                }
                $this->db->free_result();

                fwrite($fp, ']');
                fclose($fp);

                $file_name = $this->encrypt_file($file_name);
                $this->data['download_url'] = base_url() . "/download.php?type=backup&name=" . $file_name . "&return_url=" . "import.php";
            }
        } catch (Exception $e) {
            $this->error = true;
            $this->msg = 'Error: ' . $e->getMessage();
        }

        $this->return();
    }


    /**
     * Import .bak file.
     */
    public function import_transactions()
    {
        $this->post_restrict();


        $this->msg = '';
        $file_name = $_POST['file_name'] ?? '';

        // to do
        //$file_name = 'whcorgs_20200912_1599884462.bak';
        if (empty($file_name)) $this->return(true, 'Invalid imported file.');
        if (!function_exists("bzopen")) {
            $this->return(true, "Compression is enabled, but bzip2 lib is not installed or configured properly");
        }

        try {
            $base_path = APP_PATH . 'data' . DS . 'backup';
            if ($_POST['use_import_path'] ?? 0) {
                $sc_model =& SysConfigModel::get_instance();
                $import_path = $sc_model->get_code(null, SysConfigModel::CODE_IMPORT_PATH);
                /*if (strpos($import_path, '/') === 0) {
                    $base_path = $import_path;
                } else {
                    $base_path = APP_PATH . DS . $import_path;
                }*/
                $base_path = $import_path;
            }

            $file_name = $this->decrypt_file($file_name);
            $file_path = $base_path . DS . $file_name;
            $fp_path = tempnam(sys_get_temp_dir(), 'wh_' . time());

            $fp_new = @fopen($fp_path, 'w');
            $bz = bzopen($file_path, "r");
            if (!$fp_new && !$bz) {
                $this->return(true, "Failed to import sync data. File can not be created");
            }

            while (!feof($bz)) {
                fwrite($fp_new, bzread($bz, 4096));
            }
            bzclose($bz);
            fclose($fp_new);

            $jsonStream = \JsonMachine\JsonMachine::fromFile($fp_path);

            $MAX_BATCH_COUNT = 10;
            $rows = [];

            // Important: We disable trigger.
            $this->db->disable_trigger();

            $total_count = 0;
            $count = 0;
            $ids = [];
            foreach ($jsonStream as $ind => $row) {
                $ids[] = $row['id'];
                $rows[$row['id']] = $row;

                $count++;
                if ($count >= $MAX_BATCH_COUNT) {
                    list($status, $cnt) = $this->process_data($ids, $rows);
                    if (!$status) {
                        $this->error = true;
                        break;
                    }
                    $total_count += $cnt;
                    $ids = [];
                    $rows = [];
                }
            }
            if ($count) {
                list($status, $cnt) = $this->process_data($ids, $rows);
                if (!$status) {
                    $this->error = true;
                }
                $total_count += $cnt;
            }

            // Important: We re-enable trigger.
            $this->db->enable_trigger();
            SysMsg::get_instance()->info($total_count . ' actions performed.');

            // After processing. We need to remove duplicated entries.
            $this->db_check();

        } catch (Exception $e) {
            $this->error = true;
            $this->msg = 'Error: ' . $e->getMessage();
        }
        $this->return();
    }

    public  function db_check()
    {
        // Order_comments table.
    }

    /**
     * Import .bak file.
     */
    public function import_transactions_json()
    {
        $this->post_restrict();
        $this->msg = '';
        $file_name = $_POST['file_name'] ?? '';

        // to do
        //$file_name = 'whcorgs_20200912_1599884462.bak';

        if (empty($file_name)) $this->return(true, 'Invalid imported file.');
        $file_path = APP_PATH . 'data' . DS . 'backup' . DS . $file_name;

        try {
            $fp = @fopen($file_path, 'r');
            if (!$fp) $this->return(true, 'File does not exist. Please upload a file and try again.');
            fclose($fp);

            $file_name = $this->decrypt_file($file_name);
            $file_path = APP_PATH . 'data' . DS . 'backup' . DS . $file_name;

            $jsonStream = \JsonMachine\JsonMachine::fromFile($file_path);

            $MAX_BATCH_COUNT = 10;
            $rows = [];

            // Important: We disable trigger.
            $this->db->disable_trigger();

            $total_count = 0;
            $count = 0;
            $ids = [];
            foreach ($jsonStream as $ind => $row) {
                $ids[] = $row['id'];
                $rows[$row['id']] = $row;

                $count++;
                if ($count >= $MAX_BATCH_COUNT) {
                    list($status, $cnt) = $this->process_data($ids, $rows);
                    if (!$status) {
                        $this->error = true;
                        break;
                    }
                    $total_count += $cnt;
                    $ids = [];
                    $rows = [];
                }
            }
            if ($count) {
                list($status, $cnt) = $this->process_data($ids, $rows);
                if (!$status) {
                    $this->error = true;
                }
                $total_count += $cnt;
            }

            // Important: We re-enable trigger.
            $this->db->enable_trigger();

            SysMsg::get_instance()->info($total_count . ' actions performed.');
        } catch (Exception $e) {
            $this->error = true;
            $this->msg = 'Error: ' . $e->getMessage();
        }
        $this->return();
    }

    /**
     * Process Transaction rows.
     *
     * @param $ids
     * @param $rows
     * @return array
     */
    private function process_data($ids, $rows)
    {
        $TARGET_TABLE = BaseModel::TBL_SYS_SYNC_TRANSACTION;
        // to do Please comment this line.
        //$TARGET_TABLE = BaseModel::TBL_SYS_SYNC_TRANSACTION_TEST;

        $dummy_model =& UserModel::get_instance();
        $success = true;
        $count = 0;
        // processing
        $existed_ids = $this->db->query_col(sprintf("SELECT id FROM `%s` WHERE 1 %s",
            $TARGET_TABLE,
            $this->db->where_in('id', $ids)));

        // Get new_ids to be synced.
        $new_ids = array_diff($ids, $existed_ids);
        $new_rows = [];
        if (!empty($new_ids)) {
            foreach ($new_ids as $id) {
                $row = $rows[$id];

                // t odo
                $sql = $row['sql'];
                if ($row['trans_type'] == 'INSERT') {
                    $sql = str_replace_first('INSERT ', 'REPLACE ', $row['sql']);
                }
                $result = $this->db->query($sql);
                if (!$result) {
                    $success = false;
                } else {
                    $count++;
                    $new_rows[] = $row;
                }
            }
            $res = $dummy_model->insert_batch($new_rows, $TARGET_TABLE, 'id');
            if (!$res) $success = false;
        }

        return [$success, $count];
    }

    public function encrypt_file($file_name)
    {
        $new_file_name = $file_name;

        return $new_file_name;
    }

    public function decrypt_file($file_name)
    {
        $new_file_name = $file_name;

        return $new_file_name;
    }


    public function search_files()
    {
        $this->post_restrict();
        $this->msg = '';

        $path = $_POST['path'] ?? '';
        /*if (strpos($path, '/') === 0) {
            $base_path = $path;
        } else {
            $base_path = APP_PATH . DS . $path;
        }*/
        $base_path = $path;

        $files = [];
        if (is_dir($base_path)) {
            if ($dh = opendir($base_path)) {
                while (($file = readdir($dh)) !== false) {
                    if (is_file($base_path . DS . $file) && strpos($file, ".bak") !== false)
                        $files[] = $file;
                }
                closedir($dh);
            }
            $sc_model =& SysConfigModel::get_instance();
            $sc_model->update_where(['value' => $path], $this->db->where_equal('code', SysConfigModel::CODE_IMPORT_PATH, ''));
        } else {
            $this->return(true, 'Invalid folder path.');
        }
        arsort($files);
        $this->data = array_values($files);


        /*$base_path = APP_PATH . 'data' . DS . 'backup';
        $export_path = $_POST['export_path'] ?? '';
        $use_export_path = $_POST['use_export_path'] ?? false;
        if ($use_export_path) {
            if (!$export_path) $this->return(true, 'Please fill export path.');
            if (strpos($export_path, '/') === 0) {
                $base_path = $export_path;
            } else {
                $base_path = APP_PATH . DS . $export_path;
            }
            if (!file_exists($base_path)) {
                $this->return(true, 'Server path is invalid. You specified '. $export_path);
            }
            if (!is_writable($base_path)) {
                $this->return(true, 'Server path is not writable. You specified '. $export_path);
            }
            $sc_model->update_where(['value' => $export_path], $this->db->where_equal('code',SysConfigModel::CODE_EXPORT_PATH, ''));
        }*/

        $this->return();
    }
}