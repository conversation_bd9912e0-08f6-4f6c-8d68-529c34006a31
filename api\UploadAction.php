<?php

/**
 * Class UploadAction
 */
class UploadAction extends BaseAction
{
    function __construct()
    {
        parent::__construct();
    }

    public function upload_sync_file()
    {
        $storeFolder = APP_PATH . 'data' . DS . 'backup';
        if (!empty($_FILES)) {
            $tempFile = $_FILES['file']['tmp_name'];
            $targetPath = $storeFolder . DS;

            $fileName = $_FILES['file']['name'];
            $fileName = get_new_file_path($targetPath . $fileName, false);

            $targetFile = $targetPath . $fileName;
            if ($error = move_uploaded_file($tempFile, $targetFile)) {
                $this->data['file_name'] = $fileName;
                $this->data['url'] = sprintf('%s/data/backup/%s', base_url(), $fileName);
            } else {
                $this->error = true;
                $this->data = $error;
            }
        } else {
            $this->error = true;
            $this->msg = 'No file!';
        }
        $this->return();
    }

    /**
     * Removed uploaded a picture on server.
     */
    public function remove_sync_file()
    {
        $this->msg = '';
        $storeFolder = APP_PATH . 'data' . DS . 'backup';
        $fileName = $_POST['fileName'] ?? '';
        if ($fileName) {
            $fullPath = $storeFolder . DS . $fileName;
            if (file_exists($fullPath)) {
                if (unlink($fullPath)) {
                }
            } else {
                $this->ajax_return(true, 'No file exists.');
            }
        } else {
            $this->ajax_return(true, 'No file selected.');
        }
        $this->ajax_return();
    }
}