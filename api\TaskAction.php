<?php

use app\SysMsg\SysMsg;

/**
 * Class TaskAction
 */
class TaskAction extends BaseAction
{
    /**
     * @var TaskModel
     */
    private $task_model = null;
    /**
     * @var SysConfigModel
     */
    private $config_model = null;

    function __construct()
    {
        parent::__construct();

        /**
         * TaskModel
         */
        $this->task_model =& Loader::get_instance()->load_model('TaskModel');

        // We don't need to include, because we include this model as default.
        $this->config_model =& SysConfigModel::get_instance();
    }

    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $title = $row['title'] ?? '';

        if (empty($title)) {
            $this->return(true, "Please fill Title.");
        }

        // Force NULL setting
        for ($i = 1; $i <= 4; $i++) {
            if (isset($row["cat$i"]) && !$row["cat$i"]) {
                $row["cat$i"] = NULL;
            }
        }

        $cat1 = $row['cat1'] ?? null;
        $cat2 = $row['cat2'] ?? null;
        $cat3 = $row['cat3'] ?? null;
        $cat4 = $row['cat4'] ?? null;

        if ($cat1) {
            $cat1_row = $this->config_model->get($cat1, '*');
            if ($cat1_row) {
                if ($cat3) {
                    // If cat3 is specified but we can't find it in Category table, then we need to create it.
                    $where_tmp = sprintf('`type`=%s AND (id=%s OR name=%s)',
                        $this->db->safe_value(SysConfigModel::CONFIG_TYPE_TASK_CATEGORY),
                        $this->db->safe_value($cat3),
                        $this->db->safe_value($cat3));
                    $cat3_row = $this->config_model->get_by_where($where_tmp, 'id, name');
                    if (!$cat3_row) {
                        $new_id = $this->config_model->create_new_category($cat3, $cat1);
                        // we failed to insert, then we remove 'cat3'
                        if (!$cat3) {
                            unset($row['cat3']);
                        } else {
                            $row['cat3'] = $new_id;
                            SysMsg::get_instance()->info(sprintf("Created a new Cat2: '%s / %s'.", $cat1_row['name'], $cat3));
                        }
                    } else {
                        $row['cat3'] = $cat3_row['id'];
                    }
                }
                if ($cat4) {
                    // If cat4 is specified but we can't find it in Category table, then we need to create it.
                    $where_tmp = sprintf('`type`=%s AND (id=%s OR name=%s)',
                        $this->db->safe_value(SysConfigModel::CONFIG_TYPE_TASK_CATEGORY),
                        $this->db->safe_value($cat4),
                        $this->db->safe_value($cat4)
                    );
                    $cat4_row = $this->config_model->get_by_where($where_tmp, 'id, name');
                    if (!$cat4_row) {
                        $new_id = $this->config_model->create_new_category($cat4, $cat1);
                        // we failed to insert, then we remove 'cat4'
                        if (!$new_id) {
                            unset($row['cat4']);
                        } else {
                            $row['cat4'] = $new_id;
                            SysMsg::get_instance()->info(sprintf("Created a new Cat2: '%s / %s'.", $cat1_row['name'], $cat4));
                        }
                    } else {
                        $row['cat4'] = $cat4_row['id'];
                    }
                }
            } else {
                $this->return(true, "Invalid Cat1, please try again after reloading a page.");
            }
        }

        $this->config_model->verify_data($row);

        if (isset($row['due_date']) && !($row['due_date'] ?? '')) {
            $row['due_date'] = NULL;
        }

        $id = $row['id'] ?? null;
        if (empty($id)) {
            $success = false;
            unset($row['id']);

            $id = $this->task_model->insert($row, true);
            if ($id) {
                $success = true;
            }
        } else {
            $success = $this->task_model->update($row, $id);
        }

        // Save styling information.
        if ($success) {
            $task_user_data = $_POST['task_user_data'] ?? [];
            if (!isset($task_user_data['font-weight'])) {
                $task_user_data['font-weight'] = null;
            }

            $task_id = $id;
            $user_id = Auth::current_user_id();
            $success &= $this->task_model->set_task_user_data($task_id, $user_id, $task_user_data);
        }

        $this->data['row'] = $this->task_model->get_row($id, [TaskModel::WITH_ASSIGNED_USERS]);
        $config_model =& SysConfigModel::get_instance();
        $categories = $config_model->get_task_categories([]);
        $this->data['categories'] = $categories;
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    /**
     * Delete a task and its assigned users data.
     */
    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->task_model->get($id, 1);
        if (!empty($row)) {
            $this->task_model->delete($id, BaseModel::TBL_TASK_USERS, 'task_id');
            $this->task_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $with = $_POST['with'] ?? '';
        $this->msg = '';

        $rows = $this->task_model->get_list($_POST, [
            TaskModel::WITH_ASSIGNED_USERS,
        ]);
        $this->data = [
            'rows' => $rows,
            'categories' => SysConfigModel::get_instance()->get_task_categories([]),
            'sql' => $this->db->show_query_history(false),
        ];

        if ($with == 'form') {
            $this->data['sql'] = $this->db->show_query_history(false);
        }
        $this->return();
    }

    /**
     * Get the saved tasks list of user.
     * Default: current user selected.
     */
    public function get_user_saved_list()
    {
        $this->post_restrict();
        $this->msg = '';
        $params['user_id'] = $_POST['user_id'] ?? Auth::current_user_id();
        $params['status'] = $_POST['status'] ?? '';
        $this->data['rows'] = $this->task_model->get_user_saved_list($params);
        $this->data['sql'] = $this->db->show_query_history(false);
        $this->return();
    }

    /**
     * Put a task in user saved list.
     */
    public function save_task_in_user_saved_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $row = [
            'user_id' => Auth::current_user_id(),
            'task_id' => $_POST['task_id'] ?? NULL
        ];
        if (!$row['task_id']) $this->return(true, 'Invalid request. Please reload a page and try again.');
        $this->error = !$this->db->replace(BaseModel::TBL_TASK_USER_MAP_TMP, $row);
        $this->data['rows'] = $this->task_model->get_user_saved_list(['user_id' => $row['user_id'], 'status' => 1]);
        $this->return();
    }

    /**
     * Empty user's saved list.
     */
    public function empty_user_saved_list()
    {
        $this->post_restrict();
        $this->msg = '';
        $user_id = Auth::current_user_id();
        $this->error = !$this->task_model->delete_by_where(
            $this->db->where_equal('user_id', $user_id, ''),
            BaseModel::TBL_TASK_USER_MAP_TMP
        );
        $this->data['rows'] = $this->task_model->get_user_saved_list(['user_id' => $user_id, 'status' => 1]);
        $this->return();
    }

    /**
     * Delete a user's saved task
     */
    public function delete_saved_task()
    {
        $this->post_restrict();
        $this->msg = '';
        $user_id = Auth::current_user_id();
        $task_id = $_POST['task_id'] ?? '';
        if (!$task_id) {
            $this->return(true, 'Invalid request. Please reload a page and then try again.');
        }
        $where = $this->db->where_by_array(compact('user_id', 'task_id'));
        $this->error = !$this->task_model->delete_by_where($where, BaseModel::TBL_TASK_USER_MAP_TMP);
        $this->data['rows'] = $this->task_model->get_user_saved_list(['user_id' => $user_id, 'status' => 1]);
        $this->return();
    }


    /**
     * View assigned users by task ID.
     * Called on ajax.
     */
    public function view_assigned_users()
    {
        $this->post_restrict();
        /*$this->msg = '';

        $task_id = $_POST['task_id'] ?? '';
        if (empty($task_id)) {
            $this->return(true, "Invalid request!");
        }

        $s = $this->task_model->get($task_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Task does not exist!");
        }

        $params = ['task_id' => $task_id];
        $offer_model =& Loader::get_instance()->load_model('OfferModel');
        $rows = $offer_model->get_list($params);

        Loader::get_instance()->load_helper('offer_helper');
        $this->data['html'] = table_supplier_offers($rows);*/

        $this->return();
    }

    public function assign_user()
    {
        $this->post_restrict();
        $user_id = $_POST['user_id'] ?? '';
        $status_id = $_POST['status_id'] ?? '';
        $task_id = $_POST['task_id'] ?? '';
        $created_on = get_today(DATE_FORMAT_YMD_HIS);

        $this->error = !$this->db->replace(BaseModel::TBL_TASK_USERS,
            compact('user_id', 'status_id', 'task_id', 'created_on'));
        $this->msg = $this->error ? 'Failed to save.' : 'Assigned successfully.';

        $this->data['row'] = $this->task_model->get_row($task_id, [
            TaskModel::WITH_ASSIGNED_USERS,
        ]);

        $this->return();
    }

    /**
     * Delete an assignment
     */
    public function delete_assignment()
    {
        $this->post_restrict();

        $user_id = $_POST['user_id'] ?? '';
        $task_id = $_POST['task_id'] ?? '';

        $where = '1';
        $where .= $this->db->where_equal('user_id', $user_id);
        $where .= $this->db->where_equal('task_id', $task_id);
        $this->error = !$this->task_model->delete_by_where($where, BaseModel::TBL_TASK_USERS);

        $this->msg = $this->error ? 'Failed to remove.' : 'Removed an assignment successfully.';

        $this->data['row'] = $this->task_model->get_row($task_id, [
            TaskModel::WITH_ASSIGNED_USERS,
        ]);

        $this->return();
    }

    public function create_assignment_in_map()
    {
        $this->post_restrict();

        //$user_id = $_POST['user_id'] ?? '';
        $user_id = Auth::current_user_id();
        $task_id = $_POST['task_id'] ?? '';
        $task_category_id = $_POST['task_category_id'] ?? '';
        $position = $_POST['position'] ?? '';

        $this->error = !$this->db->insert(BaseModel::TBL_TASK_USER_CATEGORY_MAP, compact('user_id', 'task_id', 'task_category_id', 'position'));

        /*$params = [
            'user_id' => Auth::current_user_id(),
            'category_id' => $task_category_id,
        ];
        $this->data['tuc_list'] = $this->task_model->get_task_user_category_map_with_task($params);*/

        $this->msg = $this->error ? 'Failed to assign task into category.' : 'Assigned successfully.';
        $this->return();
    }


    public function move_assignment_in_map()
    {
        $this->post_restrict();

        $user_id = Auth::current_user_id();
        $task_id = $_POST['task_id'] ?? '';
        $task_category_id = $_POST['task_category_id'] ?? '';
        $position = $_POST['position'] ?? '';

        $where = '1';
        $where .= $this->db->where_equal('user_id', $user_id);
        $where .= $this->db->where_equal('task_id', $task_id);
        $where .= $this->db->where_equal('task_category_id', $task_category_id);
        $this->error = !$this->db->update(BaseModel::TBL_TASK_USER_CATEGORY_MAP, ['position' => $position], $where);

        $this->msg = $this->error ? 'Failed to assign task into category.' : 'Assigned successfully.';
        $this->return();
    }

    public function delete_assignment_in_map()
    {
        $this->post_restrict();

        $user_id = Auth::current_user_id();
        $task_id = $_POST['task_id'] ?? '';
        $task_category_id = $_POST['task_category_id'] ?? '';

        $where = '1';
        $where .= $this->db->where_equal('user_id', $user_id);
        $where .= $this->db->where_equal('task_id', $task_id);
        $where .= $this->db->where_equal('task_category_id', $task_category_id);
        $this->error = !$this->task_model->delete_by_where($where, BaseModel::TBL_TASK_USER_CATEGORY_MAP);

        $this->msg = $this->error ? 'Failed to remove.' : 'Removed an assignment successfully.';

        $this->return();
    }


    /**
     *
     * Get tasks list for auto completion list.
     */
    public function get_ac_tasks()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';

        $where .= $this->db->where_like('title', $_POST['keyword'] ?? '', true);
        $where .= $this->db->where_equal('title', $_POST['exactTitle']);
        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                id as data,
                IFNULL(`title`, ' - ') as value 
            FROM tasks
            WHERE TRUE $where
            $limit_str
        ";
        $this->data = $this->db->query_select($sql);
        $this->return();
    }

    public function get_assigned_tasks()
    {
        $this->login_restrict();
        $this->msg = '';

        $params = [
            'user_id' => Auth::current_user_id(),
            'task_category_id' => $_REQUEST['category_id'] ?? '',
            'status' => 1,
        ];

        $rows = $this->task_model->get_task_user_category_map_with_task($params);
        $this->data['tuc_list'] = $rows ? $rows : [];
        $this->data['grid_data'] = $this->task_model->get_grid_row_data($params['task_category_id'], $params['user_id']);

        $this->return();
    }

    public function set_grid_row()
    {
        $this->login_restrict();
        $this->msg = '';

        $task_category_id = $_POST['task_category_id'] ?? '';
        if (!$task_category_id) $this->return(true, self::MSG_INVALID_REQUEST_AND_RELOAD_PAGE);

        $user_id = Auth::current_user_id();
        $row_count = $_POST['row_count'] ?? null;
        $grid_detail = ['row_count' => $row_count];

        $this->error = !$this->task_model->set_grid_row_data($task_category_id, $user_id, $grid_detail);
        $this->data['grid_data'] = $this->task_model->get_grid_row_data($task_category_id, $user_id);
        $this->return();
    }

    /**
     * Set the Grid Height
     */
    public function set_grid_row_height()
    {
        $this->login_restrict();
        $this->msg = '';

        $task_category_id = $_POST['task_category_id'] ?? '';
        if (!$task_category_id) $this->return(true, self::MSG_INVALID_REQUEST_AND_RELOAD_PAGE);

        $row_ind = intval($_POST['row_ind'] ?? 0);
        $height = intval($_POST['height'] ?? 1);

        if ($height < 1) $height = 1;
        $user_id = Auth::current_user_id();
        $grid_detail = ['heights' => [$row_ind => $height]];

        $this->error = !$this->task_model->set_grid_row_data($task_category_id, $user_id, $grid_detail);
        $this->data['grid_data'] = $this->task_model->get_grid_row_data($task_category_id, $user_id);
        $this->return();
    }

    public function set_task_user_data()
    {
        $this->login_restrict();
        $this->msg = '';

        $task_id = $_POST['task_id'] ?? '';
        if (!$task_id) $this->return(true, self::MSG_INVALID_REQUEST_AND_RELOAD_PAGE);
        $user_id = Auth::current_user_id();
        $new_data = $_POST['task_user_data'] ?? null;
        if (empty($new_data)) $this->return(true, self::MSG_INVALID_REQUEST_AND_RELOAD_PAGE);

        $this->error = !$this->task_model->set_task_user_data($task_id, $user_id, $new_data);
        $this->data['task_user_data'] = $this->task_model->get_task_user_data($task_id, $user_id);
        $this->return();
    }

    public function toggle_background()
    {
        $this->login_restrict();
        $this->msg = '';

        $task_id = $_POST['task_id'] ?? '';
        $new_background = $_POST['new_background'] ?? null;

        if (!$task_id) $this->return(true, self::MSG_INVALID_REQUEST_AND_RELOAD_PAGE);
        $user_id = Auth::current_user_id();
        $new_data = [
            'new_background' => !$new_background ? NULL : $new_background,
        ];
        $this->error = !$this->task_model->set_task_user_data($task_id, $user_id, $new_data);
        $this->data['row'] = $this->task_model->get_row($task_id, [TaskModel::WITH_ASSIGNED_USERS]);
        $this->data['category_tree'] = SysConfigModel::get_instance()->get_task_categories_jstree([]);

        $this->return();
    }

}