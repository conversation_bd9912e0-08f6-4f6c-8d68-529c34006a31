<?php

/**
 * Class SupplierCommentAction
 */
class SupplierCommentAction extends BaseAction
{
    /**
     * @var SupplierModel
     */
    private $supplier_comment_model = null;

    function __construct()
    {
        parent::__construct();

        /**
         * SupplierModel
         */
        $this->supplier_comment_model =& Loader::get_instance()->load_model('SupplierCommentModel');
    }

    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $rows = $this->supplier_comment_model->get_list($_POST);

        Loader::get_instance()->load_helper('supplier_helper');
        $this->data['html'] = table_supplier_comments($rows);

        $this->return();
    }
}