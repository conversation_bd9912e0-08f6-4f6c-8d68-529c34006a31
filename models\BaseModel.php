<?php

use app\SysMsg\SysMsg;

abstract class BaseModel
{
    /**
     * Table key prefix.
     */
    CONST TBL_SYS_CONFIG = 'sys_config';
    CONST TBL_PERMISSION = 'permissions';
    CONST TBL_SUPPLIER = 'suppliers';
    CONST TBL_SUPPLIER_COMMENTS = 'supplier_comments';
    CONST TBL_SUPPLIER_INFO = 'supplier_info';
    CONST TBL_ORDER_INFO = 'order_info';
    CONST TBL_OFFER_INFO = 'offer_info';
    CONST TBL_USERS = 'users';
    CONST TBL_SYS_USER_PERMISSIONS = 'sys_user_permissions';
    CONST TBL_OFFERS = 'offers';
    CONST TBL_OFFER_COMMENTS = 'offer_comments';
    CONST TBL_CATEGORIES = 'categories';
    CONST TBL_CUSTOMERS = 'customers';
    CONST TBL_CUSTOMERS_CORE_SYSTEM = 'customers_core_system';
    CONST TBL_CUSTOMER_CATEGORIES = 'customer_categories';
    CONST TBL_CUSTOMER_INFO = 'customer_info';
    CONST TBL_CUSTOMER_COMMENTS = 'customer_comments';

    CONST TBL_ORDERS = 'orders';
    CONST TBL_ORDER_COMMENTS = 'order_comments';
    CONST TBL_ORDER_DATES = 'order_dates';
    CONST TBL_ORDER_INVOICES = 'order_invoices';
    CONST TBL_ORDER_INVOICE_HS_CODES = 'order_invoice_hs_codes';

    CONST TBL_TASKS = 'tasks';
    CONST TBL_TASK_USERS = 'task_users';
    CONST TBL_TASK_USER_STATUSES = 'task_user_statuses';

    CONST TBL_TASK_USER_CATEGORY_MAP = 'task_user_category_map';
    CONST TBL_TASK_USER_MAP_TMP = 'task_user_map_temp';
    CONST TBL_TASK_USER_CATEGORY_GRID = 'task_user_category_grid';
    CONST TBL_TASK_USER_DETAIL = 'task_user_detail';

    CONST TBL_INVOICES = 'invoices';


    // Tables not to be synced.
    CONST TBL_SYS_ACCESS_LOG = 'sys_access_logs';
    CONST TBL_SYS_SYNC_LOCATION = 'sys_sync_location';
    CONST TBL_SYS_SYNC_TRANSACTION = 'sys_sync_transactions';

    // test table.
    CONST TBL_SYS_SYNC_TRANSACTION_TEST = 'sys_sync_transactions_test';
    CONST EXCLUDED_IN_SYNC = [
        self::TBL_SYS_ACCESS_LOG,
        self::TBL_SYS_SYNC_LOCATION,
        self::TBL_SYS_SYNC_TRANSACTION,
        self::TBL_SYS_SYNC_TRANSACTION_TEST,
    ];

    /**
     * @var SimDB|null
     */
    protected $db = NULL;
    /**
     * @var Logger|null
     */
    protected $logger = NULL;

    /**
     * @var bool Indicate whether we will get full lists or count of lists.
     */
    private $calc_count = NULL;

    /**
     * Get/Set method for calc_count
     *
     * @param null $calc_count
     * @return bool|null
     */
    public function calc_count($calc_count = NULL)
    {
        if ($calc_count === NULL) return $this->calc_count;
        else return $this->calc_count = $calc_count;
    }

    /**
     * @var Pagination null
     */
    private $pager = NULL;

    public function set_pager($pager)
    {
        $this->pager = $pager;
    }

    public function get_pager()
    {
        return $this->pager;
    }

    /**
     * Get SQL limit clause from pagination object.
     *
     * @return string
     */
    public function limit_by_pager()
    {
        if ($this->pager) {
            $limit_start = $this->pager->getLimitStart();
            $limit = $this->pager->getRPP();
            return $this->limit($limit_start, $limit);
        }
        return "";
    }

    public function limit($limit_start = null, $limit = LIMIT_COUNT)
    {
        return $this->db->limit($limit_start, $limit);
    }

    /**
     * DB Table specific variables.
     * @var string
     */
    protected $pk = 'id';
    protected $table = null;
    protected $columns = null;
    protected $tables = NULL;

    /**
     * Get a table name
     * @return null
     */
    public function get_table()
    {
        return $this->table;
    }

    public function get_columns()
    {
        return $this->columns;
    }

    protected static $_db_location = null;

    /**
     * @var
     */
    private static $_instances = NULL;

    /**
     * Get the singleton object
     *
     * @return BaseModel|SysConfigModel|ImportModel
     *
     */
    public static function &get_instance()
    {
        global $logger;

        if (self::$_instances == NULL) {
            self::$_instances = [];
        }
        $cls_name = get_called_class();
        if (!(self::$_instances[$cls_name] ?? null)) {
            self::$_instances[$cls_name] = new $cls_name();
            self::$_instances[$cls_name]->initialize();
            self::$_instances[$cls_name]->db =& SimDB::get_instance();
            self::$_instances[$cls_name]->logger =& $logger;
        }
        return self::$_instances[$cls_name];
    }

    public static function get_location($refresh = false)
    {
        if (self::$_db_location === null || $refresh)
            self::$_db_location = SimDB::get_instance()->query_row('SELECT * FROM ' . BaseModel::TBL_SYS_SYNC_LOCATION . ';');

        return self::$_db_location;
    }

    /**
     * @param bool $refresh
     * @return FALSE|null
     */
    public static function get_location_id($refresh = false)
    {
        self::get_location($refresh);
        return self::$_db_location ? self::$_db_location['location_id'] : '';
    }

    public static function get_latest_executed_audit_date($refresh = false)
    {
        self::get_location($refresh);
        return self::$_db_location ? self::$_db_location['last_sync_date'] : '';
    }

    abstract protected function initialize();

    /**
     * BaseModel constructor.
     */
    private function __construct()
    {
        $this->initialize();
    }

    /**
     * Get the tables list as an array.
     * Tables list is kept like a singleton.
     *
     * But if $refresh is specified, fresh tables list will be returned.
     *
     * @param bool $refresh
     * @return array
     */
    public function get_tables($refresh = TRUE)
    {
        if ($this->tables == NULL || $refresh) {
            $this->tables = $this->db->get_tables();
        }

        return $this->tables;
    }

    /**
     * @param $val
     * @return string|null
     * @deprecated use safe_value
     *
     */
    public function get_field_value($val)
    {
        return $this->db->get_field_value($val);
    }

    public function get_escape_string($value, $include_single_quote = false)
    {
        $newValue = $this->db->get_escape_string($value);
        if ($include_single_quote) {
            return "'" . $newValue . "'";
        } else {
            return $newValue;
        }
    }

    /**
     * Get a quoted value
     *
     * @param mixed $val
     * @return string|null
     */
    public function safe_value($val)
    {
        return $this->db->get_field_value($val);
    }

    /**
     * Helper function to get a SQL clauses to be used in IN.
     *
     * @param $fields_array
     * @return string
     */
    public function get_where_in($fields_array)
    {
        if (!empty($fields_array)) {
            $new = [];
            foreach ($fields_array as $value) $new[] = $this->get_field_value($value);
            return implode(",", $new);
        } else {
            return '';
        }
    }

    /**
     * Generate a new Primary Key.
     *
     * @param null $table_name
     * @return FALSE|null
     */
    public function gen_pk($table_name = null)
    {
        if ($table_name == null) $table_name = $this->table;
        $id = $this->db->gen_pk($table_name);
        if (!$id) {
            SysMsg::get_instance()->error('Primary Key generation failed in ' . $table_name . '.');
        }
        return $id;
    }

    /**
     * @param $id   PK value
     * @param string $select
     * @param null $table
     * @param null $pk_field
     * @return array|bool|null
     */
    public function get($id, $select = '*', $table = null, $pk_field = null)
    {
        if ($table == null) $table = $this->table;
        if ($pk_field == null) $pk_field = $this->pk;
        return $this->db->query_row(sprintf('SELECT %s FROM `%s` WHERE `%s`=%s', $select, $table, $pk_field, $this->safe_value($id)));
    }

    /**
     * @param $field
     * @param $value
     * @param string $select
     * @param null $table
     * @return array|bool|null
     */
    public function get_by_field($field, $value, $select = '*', $table = null)
    {
        if ($table == null) $table = $this->table;
        return $this->db->get_by_field($field, $value, $select, $table);
    }

    /**
     * @param string $where
     * @param string $select
     * @param null $table
     * @return array|bool|null
     */
    public function get_by_where($where, $select = '*', $table = null)
    {
        if ($table == null) $table = $this->table;
        return $this->db->get_by_where($where, $select, $table);
    }

    public function get_list_by_where($where, $select = '*', $table = null)
    {
        if ($table == null) $table = $this->table;
        return $this->db->query_select(sprintf('SELECT %s FROM `%s` WHERE %s', $select, $table, $where));
    }

    /**
     * @param PK $id
     * @param null $table
     * @param null $field
     * @return bool|mysqli_result|string
     */
    public function delete($id, $table = null, $field = null)
    {
        if ($table == null) $table = $this->table;
        if ($field == null) $field = $this->pk;
        return $this->db->query(sprintf('DELETE FROM `%s` WHERE `%s` = %s', $table, $field, $this->safe_value($id)));
    }

    /**
     * Delete by where condition.
     *
     * @param $where
     * @param null $table
     * @return bool|mysqli_result|string
     */
    public function delete_by_where($where, $table = null)
    {
        if ($table == null) $table = $this->table;
        return $this->db->query(sprintf('DELETE FROM `%s` WHERE %s', $table, $where));
    }

    /**
     * @param array $data
     * @param $id   PK value
     * @param bool $updated_on
     * @param bool $updated_by
     * @return bool|mysqli_result|string
     */
    public function update($data, $id, $updated_on = false, $updated_by = false)
    {
        $where = sprintf("`%s`=%s", $this->pk, $this->safe_value($id));
        if ($updated_on)
            $data['updated_on'] = time2db_datetime(time());
        if ($updated_by)
            $data['updated_by'] = Auth::current_user_id();

        return $this->db->update($this->table, $data, $where);
    }

    /**
     * Update table in current model.
     *
     * @param array $data
     * @param string $where
     * @param bool $updated_on
     * @param bool $updated_by
     * @return bool|mysqli_result|string
     */
    public function update_where($data, $where, $updated_on = false, $updated_by = false)
    {
        if ($updated_on)
            $data['updated_on'] = time2db_datetime(time());
        if ($updated_by)
            $data['updated_by'] = Auth::current_user_id();
        return $this->db->update($this->table, $data, $where);
    }

    /**
     * Insert a new record.
     *
     * @param array $data
     * @param bool $created_on
     * @param bool $created_by
     * @param bool $updated_on
     * @param bool $updated_by
     * @return bool|int
     */
    public function insert($data, $created_on = false, $created_by = false, $updated_on = false, $updated_by = false)
    {
        if (!isset($data[$this->pk]) || !$data[$this->pk])
            $data[$this->pk] = $this->gen_pk();

        if ($created_on)
            $data['created_on'] = time2db_datetime(time());

        if ($created_by)
            $data['created_by'] = Auth::current_user_id();

        if ($updated_on)
            $data['updated_on'] = $data['created_on'];

        if ($updated_by)
            $data['updated_by'] = $data['created_by'];

        if ($this->db->insert($this->table, $data))
            return $data[$this->pk];
        else return false;
    }

    /**
     * Insert / Update by data.
     *
     * @param $data
     * @param bool $created_on
     * @param bool $created_by
     * @return int
     */
    public function replace($data, $created_on = false, $created_by = false)
    {
        if (!isset($data[$this->pk])) {
            $data[$this->pk] = $this->gen_pk();
        }

        if ($created_on)
            $data['created_on'] = time2db_datetime(time());

        if ($created_by)
            $data['created_by'] = Auth::current_user_id();

        return $this->db->replace($this->table, $data);
    }

    /**
     * @param array $data
     * @param null $table_name
     * @param null $pk_field
     * @return bool|int
     */
    public function insert_batch($data, $table_name = null, $pk_field = null)
    {
        if (count($data) == 0) {
            return -1;
        }
        // Setting $table_name & PK field.
        if ($table_name == null) $table_name = $this->table;
        if ($pk_field == null) $pk_field = $this->pk;

        $pk = null;
        foreach ($data as &$row) {
            if (isset($row[$pk_field])) {
                $pk = $row[$pk_field];
            } else {
                $pk = $this->gen_pk($table_name);
                $row[$pk_field] = $pk;
            }
        }

        $result = $this->db->insert_batch($table_name, $data);
        if ($result) return $pk;
        else false;
    }

    public function sql_user_name($col, $as = NULL)
    {
        return "(SELECT display_name FROM users WHERE id=$col)" . ($as ? " AS $as" : '');
    }

    /**
     * Remove unnecessary data.
     *
     * @param $data
     * @param $cols
     * @return void
     */
    public function verify_data(&$data, $cols = null)
    {
        if ($cols == null) $cols = $this->columns;
        if ($cols == null) return;

        $keys = array_keys($data);
        if ($keys) {
            $diff = array_diff($keys, $cols);
            if ($diff) {
                foreach ($diff as $k) unset($data[$k]);
            }
        }
    }
}