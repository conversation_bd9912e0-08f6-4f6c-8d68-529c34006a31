(()=>{"use strict";var e,t={6255:(e,t,n)=>{var a=n(6540),r=n(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=o(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var c="supplier",s="internal",i="customer",u=l(l(l({},c,"Supplier"),s,"Internal"),i,"Customer"),m=([{id:"",name:""}].concat(_.map(Object.keys(u),(function(e,t){return{id:e,name:u[e]}}))),n(2280)),d=n(2827);function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function p(e){return function(e){if(Array.isArray(e))return w(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||y(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?b(Object(n),!0).forEach((function(t){g(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function g(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=f(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,o,l,c=[],s=!0,i=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(a=o.call(n)).done)&&(c.push(a.value),c.length!==t);s=!0);}catch(e){i=!0,r=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(i)throw r}}return c}}(e,t)||y(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){if(e){if("string"==typeof e)return w(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?w(e,t):void 0}}function w(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var k=a.createContext(null),E="table-offer",N={org_a:["OrgA","",!1],org_b:["OrgB","",!1],supplier_id:["Supplier","",!0],offer_sid:["ID","",!0],offer:["Offer","",!0],we:["WE","i",!0],status:["Status","select",!0],warehouse:["Warehouse","select",!0],brand:["Brand","select",!0],spread_out:["Spread Out","select",!0],action_btn2:[""],top3_comments_supplier:["Supp. Comment"],top3_comments_internal:["Int. Comment"],top3_comments_customer:["Cust. Comment"],action_btn:[""]},C={org_a:40,org_b:40,supplier_id:120,offer_sid:60,offer:200,we:50,status:100,customer_comments:200,supplier_comments:200,top3_comments:200,top3_comments_supplier:200,top3_comments_internal:200,top3_comments_customer:200,warehouse:55,brand:55,spread_out:55,created_on:90,updated_on:90,action_btn:40,action_btn2:30},O=["supplier_id","offer_sid","offer","status","warehouse","brand","spread_out","created_on","updated_on","we"],S=function(e){return"supplier_id"===e?"supplier_name":e},j=[{id:1,name:"Yes"},{id:0,name:"No"}],A=[{id:1,name:"Active"},{id:2,name:"In Progress"},{id:0,name:"Closed"}],x={0:"-",1:"Yes",2:"No",3:"Publish"},P=function(e){return $("tr#tr-".concat(e," td input.supplier_id"))},q=function(e){var t=h(a.useState(!1),2),n=t[0],r=t[1],o=h(a.useState(e.item),2),l=o[0],u=o[1],m=h(a.useState(!1),2),d=m[0],f=m[1],p=h(a.useState(!1),2),b=p[0],y=p[1];a.useEffect((function(){y(!0),_.isEmpty(l.supplier_id)&&j()}),[]),a.useEffect((function(){b&&d&&j()}),[d]),a.useEffect((function(){u(e.item)}),[e.item]),a.useEffect((function(){b&&_.isEmpty(l.supplier_id)&&j()}),[l.supplier_id]);var w=a.useContext(k),E=w.sc_statuses,O=(w.cc_statuses,w.offerCommentStatuses),S=function(e){var t=P(l.id);if(t.length>0&&t.closest("tr").find("input.offer_sid ").length<1){r(!1),wait_icon(t.closest("td"));var n=q();App.ajax_post(get_ajax_url("OfferAction","save"),{isNew:0,data:{id:n.id,offer_sid:n.offer_sid,offer:n.offer,supplier_id:n.supplier_id}},(function(e){r(!1),hide_wait_icon(t.closest("td")),e.error||u(e.data.row)}),(function(e){r(!1),hide_wait_icon(t.closest("td"))}))}},j=function(){var e=l.supplier_id||"";init_autocomplete(P(l.id),{class:"SupplierAction",action:"get_ac_suppliers",exactName:""},null!=e&&e.length>1,null,null,S)},q=function(e,t){var n=v({},l);_.isUndefined(e)||(n[e]=t);var a=P(l.id);return a.length>0&&(n.supplierName=a.val(),n.supplier_id=n.supplierName?a.attr("selected-val"):""),n},D=function(e){if(e.preventDefault(),d){r(!0);var t=P(l.id),n=t.attr("selected-val"),a=t.val();u(v(v({},l),{},{supplier_id:n,supplierName:a})),App.ajax_post(get_ajax_url("OfferAction","save"),{isNew:0,data:{id:l.id,offer_sid:l.offer_sid,offer:l.offer,status:l.status,warehouse:l.warehouse,brand:l.brand,spread_out:l.spread_out,we:l.we,supplier_id:n}},(function(e){r(!1),e.error||(f(!d),u(e.data.row))}),(function(e){r(!1)}))}else f(!d)},L=function(e,t){var n=P(l.id),a=v(v({},l),{},g(g(g({},e,t),"supplier_id",n.attr("selected-val")),"supplierName",n.val()));u(a)},R=function(){l.id&&l.supplier_id&&(setDlgTitle($g_dlg,"Comments - ".concat(l.offer_sid," - ").concat(l.offer," / ").concat(l.supplierName)),setDlgBody($g_dlg,no_result),$g_dlg.modal({backdrop:"static",show:!0}),App.ajax_post_ok(get_ajax_url("SupplierComment","get_list"),{offer_id:l.id,supplier_id:l.supplier_id},(function(e){e.error||setDlgBody($g_dlg,e.data.html)}),$g_dlg))},T=function(e){setDlgTitle($g_dlg,l.offer+"'s Comments"),setDlgBody($g_dlg,no_result),$g_dlg.modal({backdrop:"static",show:!0});var t="";if(!_.isUndefined(e))switch(e){case"top3_comments_supplier":t=c;break;case"top3_comments_internal":t=s;break;case"top3_comments_customer":t=i}App.ajax_post_ok(get_ajax_url("OfferAction","view_offer_comments"),{offer_id:l.id,off_status_type:t},(function(e){e.error||setDlgBody($g_dlg,e.data.html)}),$g_dlg)},I=function(){var e="",t=Object.keys(E),n=!1,a=!1,r=!1;t.forEach((function(t){var o=E[t],l="Default"==o.sc_default_position?' checked="checked"':"";e+='\n                <div class="form-check d-block">\n                    <label class="form-check-label">\n                        <input type="radio" name="status" id="status'.concat(t,'" value="').concat(t,'" class="form-check-input status" \n                        ').concat(l,"                           \n                        /> ").concat(o.name,"\n                    </label>                    \n                </div>\n            "),"Default"==o.sc_default_position&&"1"==o.sc_customer_order&&(n=!0),"Default"==o.sc_default_position&&"1"==o.sc_contact&&(r=!0),n&&o.sc_customer_order_required&&(a=!0)}));var o='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm '.concat(r?"required":"",'">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             \n                    </div>\n                </div>\n                <div class="form-group row">\n                    <label class="col-3 col-form-label-sm required">Status</label>\n                    <div class="col-9">\n                        ').concat(e,'                                                             \n                    </div>\n                </div>\n                <div class="form-group row customer_order-wrap ').concat(n?"":" d-none",'">                \n                    <label class="col-3 col-form-label-sm ').concat(a?" required":"",'">Customer Order</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm customer_order" type="text" />\n                        </div>\n                    </div>\n                </div>\n            '),c=bs4pop.dialog({title:"Create a Comment of '".concat(l.supplierName,"'"),content:o,className2:"modal-dialog-scrollable",backdrop:"static",width:500,onShowEnd:function(){c.$el.find(".comment").focus();var e=c.$el.find(".customer_order");init_autocomplete(e,{class:"OrderAction",action:"get_ac_orders",supplier_id:l.supplier_id},!1,null,(function(){}),(function(e){})),c.$el.find('input[name="status"]').change((function(e){var t=e.target.value,n=E[t]||null;if(null!=n){1==n.sc_customer_order?c.$el.find(".customer_order-wrap").removeClass("d-none"):c.$el.find(".customer_order-wrap").addClass("d-none");var a=c.$el.find("textarea.comment").closest(".row").find("label");1!=n.sc_contact?a.removeClass("required"):a.addClass("required"),1==n.sc_customer_order_required?c.$el.find(".customer_order-wrap label").addClass("required"):c.$el.find(".customer_order-wrap label").removeClass("required")}}))},btns:[{label:"Create",className:"btn-info btn-sm",onClick:function(e){var t="",n=c.$el.find('input[name="status"]:checked').val(),a=E[n]||null;null!=a&&1==a.sc_customer_order&&(t=c.$el.find(".customer_order").attr("selected-val"));var r={supplier_id:l.supplier_id,comment:c.$el.find(".comment").val(),status:c.$el.find('input[name="status"]:checked').val(),offer_id:l.id,customer_order:t};return null!=a&&1==a.sc_contact&&r.comment.length<1?(App.info("Please fill comment."),!1):r.status.length<1?(App.info("Please select status."),!1):null!=a&&1==a.sc_customer_order&&1==a.sc_customer_order_required&&t.length<1?(App.info("Please fill the Customer Order."),c.$el.find(".customer_order").focus(),!1):(App.ajax_post_ok(get_ajax_url("SupplierAction","save_comment"),{data:r,ref_page:"offers"},(function(e){0==e.error&&(u(e.data.row),c.hide())}),c.$el),!1)}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},U=function(){var e=l.customer_comments||[],t=base_url+"/customer.php";if(e&&e.length){var n=e.filter((function(e){return 15==(e.customer_id||"").length}));n.length>0&&(t+="?customer_id=".concat(n[0].customer_id,"&offer_id=").concat(n[0].offer_id))}window.open(t,"_blank")},F=function(e,t){App.ajax_post_ok(get_ajax_url(),{class:"OfferAction",action:"save_comment",data:e},(function(e){0==e.error&&(u(e.data.row),t.hide())}),t.$el)},H=function(){var e='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="6">'.concat(l.info_comment||"","</textarea>                             \n                    </div>\n                </div>\n        "),t=bs4pop.dialog({title:"Update Offer Info",content:e,backdrop:"static",width:400,onShowEnd:function(){t.$el.find(".comment").focus()},btns:[{label:"Update",className:"btn-info btn-sm",onClick:function(e){return App.ajax_post_ok(get_ajax_url("Offer","update_info"),{offer_id:l.id,comment:t.$el.find(".comment").val()},(function(e){if(0==e.error){u(v(v({},l),{},{info_comment:e.data.row.comment})),t.hide();var n=$("#tr-"+l.id).find(".tt");dispose_tooltip(n),init_tooltip(n)}}),t.$el),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},B=function(e){var t,r,o,c,s=l[e]||"",i=RHelper.getColType(e,N),u=RHelper.isEditable(e,N),m=RHelper.isNumeric(e,N),f="",p="";switch(e){case"org_a":case"org_b":case"status":case"we":case"warehouse":case"brand":case"spread_out":case"supplier_id":case"created_on":case"updated_on":case"action_btn":case"action_btn2":p+=" text-center"}if(u&&(d||"supplier_id"==e&&_.isEmpty(l.supplier_id))){var b="form-control form-control-sm "+e;switch(m&&(b+=" text-right"),e){case"status":f=a.createElement("select",{className:b,value:s||"",onChange:function(t){return L(e,t.target.value)},disabled:n},a.createElement("option",{value:1},"Active"),a.createElement("option",{value:2},"In Progress"),a.createElement("option",{value:0},"Closed"));break;case"warehouse":f=a.createElement("select",{className:b,defaultValue:s||"",onChange:function(t){return L(e,t.target.value)},disabled:n},a.createElement("option",{value:1},"Yes"),a.createElement("option",{value:0},"No"));break;case"brand":case"spread_out":f=a.createElement("select",{className:b,defaultValue:s||"",onChange:function(t){return L(e,t.target.value)},disabled:n},Object.keys(x).map((function(e){return a.createElement("option",{key:e,value:e},x[e])})));break;case"supplier_id":f=a.createElement("div",{className:"input-wrap-with-icon"},a.createElement("input",{className:b,type:"text",disabled:n,value:l.supplierName||"","selected-val":s||"",onChange:function(t){return L(e,t.target.value)}}));break;default:f=a.createElement("input",{className:b,type:"text",disabled:n,value:s||"",onChange:function(t){return L(e,t.target.value)}})}}else{var v=s||"";null!=v&&"dt"==i&&(v=CvUtil.dtNiceDMY(v));var g=_.get(l,"supplier_comments",[]);switch(e){case"status":var h="";h=1==s?"badge-success":2==s?"badge-primary":"badge-secondary",f=a.createElement("span",{className:"badge ".concat(h)},null!==(t=null===(r=A.find((function(e){return e.id==s})))||void 0===r?void 0:r.name)&&void 0!==t?t:"");break;case"supplier_id":f=a.createElement(a.Fragment,null,a.createElement("span",{className:"supplier-info"}," ",l.supplierName||""),g&&g.length>0&&a.createElement("i",{className:"oi oi-info m-0 float-right"+(g.length>0?" blue-f":""),onClick:R,title:"Please click to view supplier comments."}));break;case"warehouse":f=1==v?"Yes":"";break;case"brand":f=null!==(o=x[v])&&void 0!==o?o:"";break;case"spread_out":f=null!==(c=x[v])&&void 0!==c?c:"";break;case"org_b":f=0==s?"":s;break;case"customer_comments":f=a.createElement("div",{className:"position-relative pr-3"},s.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-3"},CvUtil.dtNiceDMY(e.created_on)),a.createElement("div",{className:"col-9"},e.comment))})),a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},a.createElement("i",{className:"oi oi-comment-square tt",onClick:U,title:"Please click to create a comment."})));break;case"supplier_comments":f=a.createElement("div",{className:"position-relative pr-3"},s.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-3"},CvUtil.dtNiceDMY(e.created_on)),a.createElement("div",{className:"col-9"},e.comment))})),a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},s&&s.length>0&&a.createElement("i",{className:"oi oi-info m-0 mr-1"+(s&&s.length>0?" blue-f":""),onClick:R,title:"Please click to view supplier comments."}),l.id&&l.supplier_id&&a.createElement("i",{className:"oi oi-comment-square tt",onClick:I,title:"Please click to create a comment."})));break;case"top3_comments":f=a.createElement("div",{className:"position-relative pr-3 fs-z6"},s.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-3 pr-0"},CvUtil.dtNiceDM(e.created_on)," (",e.created_by_name,")"),a.createElement("div",{className:"col-9"},e.comment,a.createElement("span",{className:"c-lightgrey ml-2"},e.customer_name)))})),a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},a.createElement("i",{className:"oi oi-info m-0"+(s&&s.length>0?" blue-f":""),onClick:function(e){T()},title:"Please click to view all comments."})));break;case"top3_comments_supplier":case"top3_comments_internal":case"top3_comments_customer":f=a.createElement("div",{className:"position-relative pr-3 fs-z6"},s.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-3 pr-0"},CvUtil.dtNiceDM(e.created_on)," (",e.created_by_name,")"),a.createElement("div",{className:"col-9"},a.createElement("span",{className:"c-lightgrey mr-2"},e.customer_name),e.comment))})),s&&s.length>0&&a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},a.createElement("i",{className:"oi oi-info m-0"+(s&&s.length>0?" blue-f":""),onClick:function(t){T(e)},title:"Please click to view all comments."})));break;case"action_btn":f=a.createElement(a.Fragment,null,a.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:D,disabled:n},d?"Save":"Edit"));break;case"action_btn2":f=a.createElement("i",{className:"oi oi-comment-square action tt",title:"Create a comment...",onClick:function(e){return function(e){var t="",n=Object.keys(O),a=!1,r=!1,o=!1;n.forEach((function(e){var n=O[e],l="Default"==n.sc_default_position?' checked="checked"':"";t+='\n                <div class="form-check d-block">\n                    <label class="form-check-label">\n                        <input type="radio" name="status" id="status'.concat(e,'" value="').concat(e,'" class="form-check-input status" \n                        ').concat(l,"                           \n                        /> ").concat(n.name," ").concat(1==n.link_to_supplier?'<span class="fs-z6 c-lightgrey">(Linked to the supplier)</span>':"","\n                    </label>                    \n                </div>\n            "),"Default"==n.sc_default_position&&"1"==n.sc_customer_order&&(a=!0),"Default"==n.sc_default_position&&"1"==n.sc_contact&&(o=!0),a&&n.sc_customer_order_required&&(r=!0)}));var c='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm '.concat(o?"required":"",'">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Customer</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm customer_id" type="text" />\n                        </div>                                                  \n                    </div>\n                </div>\n                <div class="form-group row">\n                    <label class="col-3 col-form-label-sm required">Status</label>\n                    <div class="col-9">\n                        ').concat(t,'                                                             \n                    </div>\n                </div>\n                <div class="form-group row customer_order-wrap ').concat(a?"":" d-none",'">                \n                    <label class="col-3 col-form-label-sm ').concat(r?" required":"",'">Customer Order</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm customer_order" type="text" />\n                        </div>\n                    </div>\n                </div>\n            '),s=bs4pop.dialog({title:"Create a Comment of '".concat(l.offer,"'"),content:c,className2:"modal-dialog-scrollable",backdrop:"static",width:500,onShowEnd:function(){s.$el.find(".comment").focus();var t=s.$el.find(".customer_id");init_autocomplete(t,{class:"CustomerAction",action:"get_ac_customers"},!1,null,(function(){}),(function(e){}));var n=s.$el.find(".customer_order");init_autocomplete(n,{class:"OrderAction",action:"get_ac_orders",offer_id:e},!1,null,(function(){}),(function(e){})),s.$el.find('input[name="status"]').change((function(e){var t=e.target.value,n=O[t]||null;if(null!=n){1==n.sc_customer_order?s.$el.find(".customer_order-wrap").removeClass("d-none"):s.$el.find(".customer_order-wrap").addClass("d-none");var a=s.$el.find("textarea.comment").closest(".row").find("label");1!=n.sc_contact?a.removeClass("required"):a.addClass("required"),1==n.sc_customer_order_required?s.$el.find(".customer_order-wrap label").addClass("required"):s.$el.find(".customer_order-wrap label").removeClass("required")}}))},btns:[{label:"Create",className:"btn-info btn-sm",onClick:function(t){var n="",a=s.$el.find('input[name="status"]:checked').val(),r=O[a]||null;null!=r&&1==r.sc_customer_order&&(n=s.$el.find(".customer_order").attr("selected-val")||"");var o={offer_id:e,comment:s.$el.find(".comment").val(),sc_id:s.$el.find('input[name="status"]:checked').val(),customer_id:s.$el.find(".customer_id").attr("selected-val"),customer_order:n};return o.sc_id.length<1?(App.info("Please select status."),!1):null!=r&&1==r.sc_customer_order&&1==r.sc_customer_order_required&&n.length<1?(App.info("Please fill the Customer Order."),s.$el.find(".customer_order").focus(),!1):(F(o,s),!1)}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})}(l.id)}});break;case"offer":f=a.createElement(a.Fragment,null,a.createElement("span",{className:"tt",title:l.info_comment}," ",l.offer||""),a.createElement("i",{className:"oi oi-info m-0 float-right"+(g.length>0?" blue-f":""),onClick:H,title:"Please click to update offer information."}));break;default:f=v}}return a.createElement("td",{key:e,className:p},a.createElement("div",{style:{width:_.get(C,e,"")}},f))},M="";return 1==l.warehouse&&(M+=" light-yellow"),a.createElement("tr",{id:"tr-"+l.id,className:M},e.cols.map((function(e){return B(e)})))};function D(e){var t=e.loading,n=e.form,r=e.setForm,o=e.orgAListAll,l=function(e){var t=e.target,a=t.name,o=t.value,l=t.checked;if("orgAList[]"==a){var c=p(n.orgAList).filter((function(e){return e!=o}));l&&c.push(o),r(v(v({},n),{},{orgAList:c}))}else r(v(v({},n),{},g({},a,"noSupplier"==a?e.target.checked?1:0:o)))};return a.createElement("div",{className:"card border-0 bg-transparent"},a.createElement("div",{className:"card-body p-0 pt-1"},a.createElement("div",{className:"form-row"},a.createElement("div",{className:"col-auto mr-4"},a.createElement("label",{className:"mt-1"},"OrgA:"),o.map((function(e,r){return a.createElement("div",{key:"orgAList"+e,className:"form-check d-inline-block ml-3"},a.createElement("input",{type:"checkbox",name:"orgAList[]",id:"orgAList"+e,className:"form-check-input",value:e,readOnly:t,checked:_.indexOf(n.orgAList,e)>=0,onChange:l}),a.createElement("label",{htmlFor:"orgAList"+e,className:"form-check-label"},e||" - "))}))),a.createElement("div",{className:"col-auto"},a.createElement("div",{className:"form-check d-inline-block mt-1"},a.createElement("input",{type:"checkbox",id:"noSupplier",name:"noSupplier",value:1,className:"form-check-input",defaultChecked:n.noSupplier,onChange:l}),a.createElement("label",{htmlFor:"noSupplier",className:"form-check-label"},"No Supplier?"))))))}var L=function(e){var t=e.loading,n=e.form,r=e.setForm,o=function(e){var t=e.target,a=t.name,o=t.value;r(v(v({},n),{},g({},a,o)))},l=Object.keys(N),c="form-control form-control-sm";return a.createElement("tr",{id:"row-search",className:"row-search"+(t?" loading":"")},l.map((function(e,t){return"action_btn"!==e&&"action_btn2"!==e?a.createElement("td",{key:e},"top5_no_contacts"!=e&&"offers"!=e&&"org_a"!=e&&a.createElement("div",{style:{width:_.get(C,e,"auto")}},"status"!=e&&"warehouse"!=e&&"brand"!=e&&"spread_out"!=e?a.createElement(m.A,{className:c,name:S(e),value:n[S(e)]||"",onChange:o}):"spread_out"==e||"brand"==e?a.createElement("select",{className:c,name:S(e),defaultValue:n[n[S(e)]]||"",onChange:o},a.createElement("option",{value:""}),Object.keys(x).map((function(e,t){return a.createElement("option",{key:e,value:e},x[e])}))):a.createElement("select",{className:c,name:S(e),defaultValue:n[n[S(e)]]||"",onChange:o},a.createElement("option",{value:""}),j.map((function(e,t){return a.createElement("option",{key:e.id,value:e.id},e.name)}))))):a.createElement("td",{key:e})})))};function R(e){var t=e.defaultSearchForm,n=e.defaultOrder,r=e.settings,o=h(a.useState(!0),2),l=o[0],c=o[1],s=h(a.useState(!0),2),i=s[0],u=s[1],m=h(a.useState(!1),2),f=m[0],b=m[1],g=h(a.useState(n.field),2),y=g[0],w=g[1],S=h(a.useState(n.dir),2),P=S[0],R=S[1],T=h(a.useState(r),2),I=T[0],U=T[1],F=h(a.useState(r.orgAList),2),H=F[0],B=(F[1],{id:"",offer_sid:"",offer:"",supplier_id:"",status:1,warehouse:1,brand:0,spread_out:0}),M=h(a.useState(B),2),W=(M[0],M[1]),Y=h(a.useState([]),2),V=Y[0],z=Y[1],Q=h(a.useState(""),2),G=Q[0],J=Q[1],K=h(a.useState(v(v({},t),{},{orgAList:H})),2),X=K[0],Z=K[1],ee={page:0,pageCount:0},te=h(a.useState(ee),2),ne=te[0],ae=te[1];a.useEffect((function(){le(v(v({},X),{},{with:"form"}))}),[]),a.useEffect((function(){$("#"+E).floatThead({autoReflow:!0})}),[l]),a.useEffect((function(){f&&re(null)}),[y,P]),a.useEffect((function(){f&&re(null)}),[X]),a.useEffect((function(){f&&re(null)}),[ne.page]);var re=function(e){var t=v({},X);oe(t)},oe=function(e){e=Object.assign({},X,e),le(e)},le=function(e){u(!0);var t={pager:ne};"order_field"in(t=Object.assign({},t,e))||(t.order_field=y),"order_dir"in t||(t.order_dir=P),App.ajax_post(get_ajax_url("OfferAction","get_list"),t,(function(e){b(!0),u(!1),e.error||ce(e.data),c(!1)}),(function(e){b(!0)}))},ce=function(e){z(p(e.rows)),ae(_.get(e,"pager",ee)),J(e.sql),init_tooltip()},se=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.offer_sid,"'?"),(function(t){t&&App.ajax_post(get_ajax_url("OfferAction","delete"),{id:e.id},(function(t){0==t.error&&z(V.filter((function(t){return t.id!=e.id})))}))}),{title:"Delete entry"})},ie=function(e,t){var n=!t.id,a=ue(t),r=bs4pop.dialog({id:"dlg-create-offer",title:n?"Create an Offer":"Update an Offer",content:a,backdrop:"static",className2:"modal-dialog-scrollable",width:500,onShowEnd:function(e){r.$el.find("input:first").focus(),init_autocomplete($('#dlg-create-offer input[name="supplier_id"]'),{class:"Supplier",action:"get_ac_suppliers"},""!=_.get(t,"supplier_id",""))},btns:[{label:n?"Create":"Update",onClick:function(e){var n=r.$el;return function(e,t){u(!0);var n=v({},e);delete n.supplierName,App.ajax_post(get_ajax_url("Offer","save"),{data:n},(function(e){u(!1),e.error||(z([e.data.row].concat(p(V))),W(v({},B)),_.isUndefined(t)||t.hide())}),(function(e){u(!1)}))}({id:t.id||"",supplier_id:n.find('[name="supplier_id"]').attr("selected-val")||"",offer_sid:n.find('[name="offer_sid"]').val(),offer:n.find('[name="offer"]').val(),status:n.find('[name="status"]').val(),warehouse:n.find('[name="warehouse"]').val(),brand:n.find('[name="brand"]').val(),spread_out:n.find('[name="spread_out"]').val(),we:n.find('[name="we"]').val()},r),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},ue=function(e){return'\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Supplier</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon fw-150">\n                            <input class="form-control form-control-sm" name="supplier_id" type="text" value="'.concat(e.supplierName||"",'" selected-val="').concat(e.supplier_id||"",'" />\n                        </div>                                                             \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Offer ID</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm" name="offer_sid" type="text" value="').concat(_.get(e,"offer_id",""),'" />                                     \n                    </div>\n                </div> \n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Offer</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm" name="offer">').concat(e.offer||"",'</textarea>\n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">WE</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm text-right fw-150" name="we" type="text" value="').concat(_.get(e,"we",""),'" />                                     \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Status</label>\n                    <div class="col-9">\n                        ').concat(A.map((function(t,n){return'\n                             <div class="form-check d-inline-block ml-3 mt-1">\n                                <input type="radio" name="status" id="'.concat("status"+t.id,'" class="form-check-input"\n                                       value="').concat(t.id,'"\n                                       ').concat(e.status==t.id?' checked="checked"':"",'\n                                />\n                                <label for="').concat("status"+t.id,'" class="form-check-label">').concat(t.name,"</label>\n                            </div>    \n                        ")})).join(""),'                                                 \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Warehouse?</label>\n                    <div class="col-9">\n                        ').concat(j.map((function(t,n){return'\n                             <div class="form-check d-inline-block ml-3 mt-1">\n                                <input type="radio" name="warehouse" id="'.concat("warehouse"+t.id,'" class="form-check-input"\n                                       value="').concat(t.id,'"\n                                       ').concat(e.warehouse==t.id?' checked="checked"':"",'\n                                />\n                                <label for="').concat("warehouse"+t.id,'" class="form-check-label">').concat(t.name,"</label>\n                            </div>    \n                        ")})).join(""),'\n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Brand?</label>\n                    <div class="col-9">\n                        ').concat(Object.keys(x).map((function(t,n){return'\n                             <div class="form-check d-inline-block ml-3 mt-1">\n                                <input type="radio" name="brand" id="'.concat("brand"+t,'" class="form-check-input"\n                                       value="').concat(t,'"\n                                       ').concat(e.brand==t?' checked="checked"':"",'\n                                />\n                                <label for="').concat("brand"+t,'" class="form-check-label">').concat(x[t],"</label>\n                            </div>    \n                        ")})).join(""),'\n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Spread Out?</label>\n                    <div class="col-9">\n                        ').concat(Object.keys(x).map((function(t,n){return'\n                             <div class="form-check d-inline-block ml-3 mt-1">\n                                <input type="radio" name="spread_out" id="'.concat("spread_out"+t,'" class="form-check-input"\n                                       value="').concat(t,'"\n                                       ').concat(e.spread_out==t?' checked="checked"':"",'\n                                />\n                                <label for="').concat("spread_out"+t,'" class="form-check-label">').concat(x[t],"</label>\n                            </div>    \n                        ")})).join(""),"\n                    </div>\n                </div>\n            ")},me=Object.keys(N);return a.createElement(k.Provider,{value:v(v({},I),{},{setStateSettings:U})},a.createElement(D,{form:X,setForm:Z,loading:i,orgAListAll:H}),a.createElement("h4",null,V?"Results (".concat(V.length," records)"):"No Results",a.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),a.createElement("div",{className:"sql-log-wrap"},a.createElement("pre",null,G)),a.createElement("div",{className:"table-wrap position-relative"},a.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},a.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:re,disabled:i},"Refresh"),a.createElement("button",{className:"btn btn-sm btn-success",onClick:function(e){return ie(0,B)},disabled:i},"Create an Offer"),a.createElement("span",{className:"ml-5"},i?"Loading ...":"")),a.createElement("table",{id:E,className:"data-table editable border-0",style:{minWidth:500}},a.createElement("thead",null,a.createElement("tr",null,me.map((function(e,t){return"action_btn"!==e&&"action_btn2"!==e?a.createElement("th",{key:e,className:(RHelper.isSortable(e,O)?" icon-order-wrap":"")+RHelper.getAlign(e,N),onClick:function(t){return function(e){RHelper.isSortable(e,O)&&(e==y?R((function(e){return"desc"==e?"asc":"desc"})):(w(e),R("asc")))}(e)}},a.createElement("div",{style:{width:_.get(C,e,"")}},RHelper.getColName(e,N),RHelper.isSortable(e,O)&&y==e&&a.createElement("a",{className:"icon-order "+P,href:"#"}))):a.createElement("th",{key:e})})))),a.createElement("tbody",null,a.createElement(L,{form:X,setForm:Z,loading:i}),V.map((function(e,t){return a.createElement(q,{key:e.id,isTotal:!1,item:e,cols:me,loading:i,handleRowDelete:se})})))),a.createElement(d.A,{pager:ne,onPageChange:function(e){var t=v(v({},ne),{},{page:e.selected});ae(t)}})))}var T="undefined"!=typeof ROfferProps?ROfferProps:"undefined"!=typeof App?App.get_params(window.location):{};r.render(a.createElement(R,T),document.getElementById("root"))}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,a),o.loaded=!0,o.exports}a.m=t,e=[],a.O=(t,n,r,o)=>{if(!n){var l=1/0;for(u=0;u<e.length;u++){for(var[n,r,o]=e[u],c=!0,s=0;s<n.length;s++)(!1&o||l>=o)&&Object.keys(a.O).every((e=>a.O[e](n[s])))?n.splice(s--,1):(c=!1,o<l&&(l=o));if(c){e.splice(u--,1);var i=r();void 0!==i&&(t=i)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,r,o]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),a.j=594,(()=>{var e={594:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var r,o,[l,c,s]=n,i=0;if(l.some((t=>0!==e[t]))){for(r in c)a.o(c,r)&&(a.m[r]=c[r]);if(s)var u=s(a)}for(t&&t(n);i<l.length;i++)o=l[i],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),a.nc=void 0;var r=a.O(void 0,[96],(()=>a(6255)));r=a.O(r)})();