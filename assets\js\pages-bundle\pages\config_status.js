(()=>{"use strict";var e,t={6336:(e,t,n)=>{var r=n(6540),a=n(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function c(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,c,l=[],i=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);i=!0);}catch(e){s=!0,a=e}finally{try{if(!i&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(s)throw a}}return l}}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var m={code:["Code"],name:["Name"],order:["Order","i",!0],sc_customer_order:["Display OrderNo?","select",!0],sc_customer_order_required:["Require OrderNo?","select",!0],sc_default_position:["Position","",!0],sc_contact:["Contacts","select",!0]},b=function(e){var t=u(r.useState(e.cellData),2),n=t[0],a=t[1],o=e.isEditing,c=n.colName,l=n.value;r.useEffect((function(){a(e.cellData)}),[e]);var i=function(t){var n=t.target.value;a({colName:c,value:n}),e.onCellChange(c,n)},s=function(){RHelper.getColType(c,m);var e=l;switch(c){case"sc_contact":case"sc_customer_order":case"sc_customer_order_required":e=1==e?"Yes":"No";break;default:e=e||""}return e},f=RHelper.getColType(n.colName,m),d="form-control form-control-sm"+("d"==f||"i"==f?" text-right":"")+" "+c,b="";if(o){var p="";switch(c){case"code":case"order":case"":p+=" fw-60";break;case"sc_customer_order":case"sc_default_position":case"sc_customer_order_required":p+=" fw-100"}switch(c){case"sc_contact":case"sc_customer_order":case"sc_customer_order_required":var v="1"==l?1:0;b=r.createElement("select",{className:d+p,value:v,onChange:i},r.createElement("option",{defaultValue:v,value:1},"Yes"),r.createElement("option",{defaultValue:v,value:0},"No"));break;default:b=r.createElement("input",{className:d+p,type:"text",disabled:e.loading,value:s(),onChange:i,onInput:i})}}else{var y=c;"sc_contact"!=c&&"sc_customer_order"!=c&&"sc_customer_order_required"!=c||(y+=" fw-80 text-center"),b=r.createElement("div",{className:y+("d"==f||"i"==f?" text-right":"")},s())}return r.createElement("td",null,b)},p=function(e){var t=u(r.useState(!1),2),n=t[0],a=t[1],o=u(r.useState(e.item),2),c=o[0],l=o[1],f=u(r.useState(!1),2),d=f[0],p=f[1];r.useEffect((function(){l(e.item)}),[e.item]);var v=function(e,t){var n=i(i({},c),{},s({},e,t));l(n)},y=e.rowIndex;return r.createElement("tr",{id:"tr-"+c.id},r.createElement("td",null,isNaN(y)?"":y+1),e.cols.map((function(e,t){return r.createElement(b,{key:t,cellData:{colName:e,value:e in c?c[e]:""},isEditing:d&&"created_on"!=e,loading:n,onCellChange:v})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){if(e.preventDefault(),d){a(!0);var t={id:c.id};Object.keys(m).forEach((function(e){t[e]=c[e]})),App.ajax_post(get_ajax_url("ConfigAction","save"),{isNew:0,data:t},(function(e){p(!d),a(!1),l(e.data)}),(function(e){a(!1)}))}else p(!d)},disabled:n},d?"Save":"Edit"),r.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(c)},disabled:n},"Delete")))},v=function(e){var t=u(r.useState(!1),2),n=t[0],a=t[1],o=u(r.useState(e.item),2),c=o[0],l=o[1];r.useEffect((function(){}),[c]),r.useEffect((function(){l(e.item),a(e.loading)}),[e]);var f=function(e,t){l(i(i({},c),{},s({},e,t)))};return r.createElement("tr",{id:"tr-"+c.id},r.createElement("td",null),e.cols.map((function(e,t){return r.createElement(b,{key:t,cellData:{colName:e,value:e in c?c[e]:""},isEditing:"created_on"!=e,loading:n,onCellChange:f})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=i({},c);e.handleRowCreate(t),l(t)}},"Create")))};function y(e){var t=e.loading,n=e.form,a=e.searchData,o=u(r.useState(n.code),2),c=o[0],l=o[1],i=u(r.useState(n.name),2),s=i[0],f=i[1],d=function(e){a({code:c,name:s})};return r.createElement("div",{className:"card border-0 bg-transparent"},r.createElement("div",{className:"card-body p-0 pt-1"},r.createElement("div",{className:"form-row"},r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"code"},"Code:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"code",id:"code",value:c,onChange:function(e){return l(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&d()}})),r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"name"},"Name:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"name",id:"name",value:s,onChange:function(e){return f(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&d()}})),r.createElement("div",{className:"col-auto"},r.createElement("button",{className:"btn btn-sm btn-info",onClick:d,disabled:t},"Search")))))}function g(e){var t=e.initialFormStates,n={id:0,code:"",name:"",order:"",value:""},a=u(r.useState(!0),2),o=a[0],l=a[1],s=u(r.useState(!0),2),f=s[0],d=s[1],b=u(r.useState(!1),2),g=(b[0],b[1]),h=u(r.useState(n),2),E=h[0],_=h[1],w=u(r.useState({}),2),N=w[0],O=w[1],S=u(r.useState([]),2),j=S[0],C=S[1],k=u(r.useState(""),2),x=k[0],A=k[1],D=u(r.useState(t),2),P=D[0],R=D[1];r.useEffect((function(){I(i(i({},P),{},{with:"form"}))}),[]),r.useEffect((function(){q()}),[j]),r.useEffect((function(){$("#table-config-status").floatThead({autoReflow:!0})}),[o]);var q=function(){var e={};j.map((function(t,n){e[t.id]=n})),O(e)},I=function(e){var t={class:"ConfigAction",action:"get_list"};t=Object.assign({},t,e),App.ajax_post(get_ajax_url(),t,(function(e){g(!0),d(!1),T(e.data),l(!1)}),(function(e){g(!0)}))},T=function(e){C(c(e.rows)),A(e.sql),init_tooltip()},F=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.code," / ").concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"ConfigAction",action:"delete",id:e.id},(function(t){0==t.error&&C(j.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete entry"})},H=Object.keys(m);return r.createElement(r.Fragment,null,r.createElement(y,{form:P,setForm:R,loading:f,setLoading:d,searchData:function(e){d(!0),I(e)}}),r.createElement("h4",null,j?"Results (".concat(j.length," records)"):"No Results",r.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),r.createElement("div",{className:"sql-log-wrap"},r.createElement("pre",null,x)),r.createElement("div",{className:"table-wrap"},r.createElement("table",{className:"data-table editable border-0",id:"table-config-status",style:{minWidth:500}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"No"),H.map((function(e,t){return r.createElement("th",{key:e},RHelper.getColName(e,m),"order"==e&&r.createElement("i",{className:"oi oi-info",title:"Display order.","data-placement":"right"}),"sc_default_position"==e&&r.createElement("i",{className:"oi oi-info",title:"Please set 'Default' if this status is a default status.","data-placement":"right"}))})),r.createElement("th",null))),r.createElement("tbody",null,r.createElement(v,{key:"new-row",item:E,cols:H,loading:f,handleRowCreate:function(e){d(!0),App.ajax_post(get_ajax_url(),{class:"ConfigAction",action:"save",data:i({},e)},(function(e){d(!1),e.error||(C([e.data].concat(c(j))),_(i({},n)))}),(function(e){d(!1)}))}}),j.map((function(e,t){return r.createElement(p,{key:e.id,isTotal:!1,item:e,cols:H,rowIndex:N[e.id],loading:f,handleRowDelete:F})}))))))}var h="undefined"!=typeof ConfigStatusProps?ConfigStatusProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(r.createElement(g,h),document.getElementById("root"))}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var c=1/0;for(u=0;u<e.length;u++){for(var[n,a,o]=e[u],l=!0,i=0;i<n.length;i++)(!1&o||c>=o)&&Object.keys(r.O).every((e=>r.O[e](n[i])))?n.splice(i--,1):(l=!1,o<c&&(c=o));if(l){e.splice(u--,1);var s=a();void 0!==s&&(t=s)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,a,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=703,(()=>{var e={703:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[c,l,i]=n,s=0;if(c.some((t=>0!==e[t]))){for(a in l)r.o(l,a)&&(r.m[a]=l[a]);if(i)var u=i(r)}for(t&&t(n);s<c.length;s++)o=c[s],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r.nc=void 0;var a=r.O(void 0,[96],(()=>r(6336)));a=r.O(a)})();