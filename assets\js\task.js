const TC_ACTION_URL = get_ajax_url() + '?class=ConfigTaskCategory';
const DEFAULT_ROW_COUNT = 4;
const DEFAULT_COL_COUNT = 4;
const DEFAULT_ROW_HEIGHT = 80;
const SAVED_TASKS = 1;
const YELLOW = '#FFFF00';
const DEFAULT_BACKGROUND = '#FFFFFF';

var catTree = null
var assignmentWrapEle = null;
var tasksWrapEle = null;
var tasksEle = null;
var savedTasksEle = null;
var last_removed_id = null;
var triggerOnTree = true;

function Task(settings) {
    this.settings = settings || {
        tasks: [],
        saved_tasks: [],
        grid_data: {},
    };
    this.init = () => {
        if (!jQuery.isReady) {
            App.error('Invalid use of JS lib.');
            return;
        }
        assignmentWrapEle = $('#assignments-wrap');
        tasksWrapEle = $('#tasksWrapEle');
        tasksEle = $('#tasks');
        savedTasksEle = $('#saved-tasks');

        this.reload_tasks(null);
        this.reload_saved_tasks(null);
        this._init_category_tree();
        this._init_assignments_dnd();

        $('#act-create-task').click((e) => {
            this.on_click_update_task(e);
        });

        $('#act-create-task-sc').click(e => {
            this.on_click_create_task_sc(e);
        });
        $('#new-comment').keydown(e => {
            if (is_tab_pressed(e)) {
                this.on_click_create_task_sc(e);
            }
        });

        $('#act-reload-tasks').click((e) => {
            this.reload_tasks(e);
        });

        $('#act-reload-categories').click((e) => {
            this.reload_categories(e);
        });

        $('#act-new-grid-row').click(this._expand_grid_row);
        $body.on('click', '#act-delete-grid-row', this._shrink_grid_row);

        assignmentWrapEle.on('click', '.grid-row i.oi-plus', e => this._expand_grid_row_height(e));
        assignmentWrapEle.on('click', '.grid-row i.oi-minus', e => this._shrink_grid_row_height(e));

        $('body').on('click', '#tasks .task-title, #saved-tasks .task-title', (e) => {
            e.stopPropagation();
            e.preventDefault();
            const task_id = $(e.currentTarget).parent().attr('data-id');
            const task = this.get_task_by_id(task_id);
            if (task != null) {
                this.on_click_update_task(e, task);
            }
        });

        /*var DELAY = 200, clicks = 0, timer = null;
        assignmentWrapEle.on('click', '.dropped-task .task-title', (e) => {
            clicks++;
            if(clicks === 1) {
                timer = setTimeout(()  => {
                    const task_id = $(e.currentTarget).parent().attr('data-id');
                    const task = this.get_task_by_id(task_id);
                    if (task != null) {
                        this.on_click_update_task(e, task);
                    }
                    clicks = 0;             //after action performed, reset counter
                }, DELAY);
            } else {
                clearTimeout(timer);    //prevent single-click action
                const task_id = $(e.currentTarget).closest('.dropped-task').attr('data-id');
                const task = this.get_task_by_id(task_id);

                let new_background = this._get_task_style_by_key(task, 'new_background');
                if (new_background != null && new_background.toUpperCase() == YELLOW) {
                    new_background = null;
                } else {
                    new_background = YELLOW;
                }
                App.ajax_post_ok(get_ajax_url('Task', 'toggle_background'), {
                    task_id: task_id,
                    new_background : new_background,
                }, (res) => {
                    if (!res.error) {
                        this.update_task_data(res.data.row);
                        this.settings.category_tree = res.data.category_tree;
                        this.refresh_tree(e);
                        triggerOnTree = false;
                    }
                }, assignmentWrapEle);
                clicks = 0;             //after action performed, reset counter
            }
        });*/

        assignmentWrapEle.on('click', '.dropped-task .task-title', (e) => {
            const task_id = $(e.currentTarget).parent().attr('data-id');
            const task = this.get_task_by_id(task_id);
            if (task != null) {
                this.on_click_update_task(e, task);
            }
        });

        assignmentWrapEle.on('contextmenu', '.dropped-task .task-title', (e) => {
            e.preventDefault();
            const task_id = $(e.currentTarget).closest('.dropped-task').attr('data-id');
            const task = this.get_task_by_id(task_id);

            let new_background = this._get_task_style_by_key(task, 'new_background');
            if (new_background != null && new_background.toUpperCase() == YELLOW) {
                new_background = null;
            } else {
                new_background = YELLOW;
            }
            App.ajax_post_ok(get_ajax_url('Task', 'toggle_background'), {
                task_id: task_id,
                new_background: new_background,
            }, (res) => {
                if (!res.error) {
                    this.update_task_data(res.data.row);
                    this.settings.category_tree = res.data.category_tree;
                    this.refresh_tree(e);
                    triggerOnTree = false;
                }
            }, assignmentWrapEle);
        });

        /*assignmentWrapEle.on('dblclick', '.dropped-task', (e) => {
            const task_id = $(e.currentTarget).attr('data-id');
            const task = this.get_task_by_id(task_id);

            let new_background = this._get_task_style_by_key(task, 'new_background');
            if (new_background != null && new_background.toUpperCase() == YELLOW) {
                new_background = null;
            } else {
                new_background = YELLOW;
            }
            App.ajax_post_ok(get_ajax_url('Task', 'toggle_background'), {
                task_id: task_id,
                new_background : new_background,
            }, (res) => {
                if (!res.error) {
                    this.update_task_data(res.data.row);
                    this.settings.category_tree = res.data.category_tree;
                    this.refresh_tree(e);
                }
            }, assignmentWrapEle);
        });*/

        // Saved Tasks --> Remove all
        $('#act-delete-saved-tasks').click(this.empty_user_saved_list);

        savedTasksEle.on('click', '.act-remove-saved-task', e => {
            this.delete_saved_task($(e.target).closest('.task').attr('data-id'));
        });

        // Set heights for 3 panels.
        this.set_panel_size();
        $(window).resize((e) => {
            this.set_panel_size();
        });
    };

    this.set_panel_size = () => {
        const offset = $('#content').offset();
        const top = offset['top'];
        let height;
        height = $(window).height() - top;
        height -= $('footer').height();
        $('#content .panel').height(height);
    };
    this._init_category_tree = () => {
        catTree = $('#categories-wrap');
        catTree.jstree({
            'core': {
                'data': this.settings.category_tree,
                'check_callback': (operation, node, parent, position, more) => {
                    // operation can be 'create_node', 'rename_node', 'delete_node', 'move_node', 'copy_node' or 'edit'
                    // in case of 'rename_node' node_position is filled with the new node name
                    //console.log(operation, node, parent, position, more);
                    if (operation === "copy_node" || operation === "move_node") {
                        if (parent.id === "#") {
                            return false; // prevent moving a child above or below the root
                        }
                    }
                    if (operation == 'rename_node' || operation == 'edit') {
                        var level = node.parents.length;
                        if (level <= 2) {
                            App.info('You cannot rename or edit categories up to 2nd level.')
                            return false;
                        }
                    } else if (operation == 'create_node') {
                        var level = parent.parents.length;
                        if (level <= 1) {
                            App.info('You cannot create the 2nd level category.')
                            return false;
                        }
                        if (level >= 4) {
                            App.info('You cannot create a child category anymore.')
                            return false;
                        }
                    } else if (operation == 'delete_node') {
                        var level = node.parents.length;
                        if (level <= 2) {
                            App.info('You cannot delete categories up to 2nd level category.')
                            return false;
                        }
                    } else if (operation == 'move_node') {
                        if (parent.parents.length <= 1 || node.parents.length <= 2) {
                            //App.info('You cannot move to 1st level category or move 2nd level category.')
                            return false;
                        }
                        if (parent.parents.length >= 4) {
                            return false;
                        }
                    }
                    return true; // allow everything else
                },
            },
            "plugins": [
                "contextmenu", "dnd", "unique", "wholerow", "types", "state"
                //"contextmenu", "dnd", "search", "state", "types", "wholerow"
            ],
            "contextmenu": {
                "items": function (data, ref) {
                    /*console.log(data, ref);*/
                    var level = data.parents.length;
                    if (level == 1) return null;
                    var items = {};
                    if (level >= 2 && level < 4) {
                        items["Create"] = {
                            "label": "Create",
                            "action": function (data) {
                                var ref = $.jstree.reference(data.reference);
                                sel = ref.get_selected();
                                if (!sel.length) {
                                    return false;
                                }
                                sel = sel[0];
                                sel = ref.create_node(sel, {"type": "file"});
                                if (sel) {
                                    ref.edit(sel);
                                }
                            }
                        }
                    }
                    if (level > 2) {
                        items["Rename"] = {
                            "label": "Rename",
                            "action": function (data) {
                                var inst = $.jstree.reference(data.reference);
                                obj = inst.get_node(data.reference);
                                inst.edit(obj);
                            }
                        };
                        items["Delete"] = {
                            "label": "Delete",
                            "action": function (data) {
                                var ref = $.jstree.reference(data.reference),
                                    sel = ref.get_selected();
                                if (!sel.length) {
                                    return false;
                                }
                                ref.delete_node(sel);
                            }
                        }
                    }
                    return items;
                }
            }
        }).on('delete_node.jstree', (e, data) => {
            last_removed_id = data.node.id;
            $.get(TC_ACTION_URL + '&action=delete_node', {'id': data.node.id}).done((d) => {
                if (d.error) {
                    App.show_messages(d, true);
                    data.instance.refresh();
                    //catTree.jstree("select_node", data.node.id);
                }
            }).fail(function () {
                data.instance.refresh();
            });
        }).on('create_node.jstree', function (e, data) {
            return true;
            /*$.get(TC_ACTION_URL + '&action=create_node', {'id': data.node.parent, 'position': data.position, 'text': data.node.text}).done((d) => {
                if (!d.error) {
                    data.instance.set_id(data.node, d.data.row.id);
                } else data.instance.refresh();
            }).fail(function () {
                data.instance.refresh();
            });*/
        }).on('rename_node.jstree', function (e, data) {
            $.get(TC_ACTION_URL + '&action=rename_node', {
                'id': data.node.id,
                'text': data.text,
                'parent_id': data.node.parent
            }).done(res => {
                if (res.error) {
                    data.instance.refresh();
                    App.show_messages(res, true);
                } else {
                    data.instance.set_id(data.node, res.data.row.id);
                }
            }).fail(function () {
                data.instance.refresh();
            });
        }).on('move_node.jstree', function (e, data) {
            $.get(TC_ACTION_URL + '&action=move_node', {
                'id': data.node.id,
                'parent_id': data.parent,
                'position': data.position
            }).done(res => {
                if (res.error) {
                    data.instance.refresh();
                    App.show_messages(res, true);
                }
            }).fail(function () {
                data.instance.refresh();
            });
        }).on('copy_node.jstree', function (e, data) {
            return false;
            /*$.get(TC_ACTION_URL + '&action=copy_node', {'id': data.original.id, 'parent': data.parent, 'position': data.position})
                .always(function () {
                    data.instance.refresh();
                });*/
        }).on('changed.jstree', function (e, data) {
            if (data && data.selected && data.selected.length < 1) {
                if (last_removed_id) {
                    catTree.jstree('select_node', last_removed_id);
                }
            }
        });

        // Select event on jsTree
        // ----------------------------------------------------------------------------------------------
        catTree.bind("select_node.jstree", this.on_select_category);
    };

    this.on_select_category = (e, data) => {
        const old_status = triggerOnTree;
        triggerOnTree = true;
        if (!old_status) return;

        const node = data.node;
        const has_children = data.node.children.length > 0;
        if (!has_children) {
            App.ajax_post_ok(get_ajax_url('Task', 'get_assigned_tasks'), {
                category_id: node.id
            }, res => {
                if (!res.error) {
                    assignmentWrapEle.parent().removeClass('no-grid');
                    assignmentWrapEle.html('');
                    this.settings.tuc_list = res.data['tuc_list'];
                    this.settings.grid_data = res.data['grid_data'];
                    assignmentWrapEle.append(this._html_grid_panel(res.data['tuc_list'], res.data['grid_data']));
                    this._init_assignments_dnd();
                    this._update_remove_row_btn();
                    this._update_grid_rows_styles();
                }
            }, assignmentWrapEle.closest('.card-body'));
        } else {
            assignmentWrapEle.parent().addClass('no-grid');
            assignmentWrapEle.html('<div id="assignment-hint">Please select a leaf category to assign tasks.</div>');
        }
        this._update_remove_row_btn();
    };

    this._init_assignments_dnd = () => {
        this._init_droppable();
        this._init_draggable();
    };
    this._init_draggable = (mode) => {
        const targetEle = _.isUndefined(mode) ? tasksEle : savedTasksEle;
        $("div.task", targetEle).draggable({
            //cancel: "div.task-title", // clicking an icon won't initiate dragging
            revert: "invalid", // when not dropped, the item will revert back to its initial position
            containment: "document",
            helper: "clone",
            cursor: "move",
            start: function (event, ui) {
                $(ui.helper).css('width', $(event.target).outerWidth());
                $(ui.helper).css('height', $(event.target).outerHeight());
            },
        });
        this._init_grid_cell_draggable()
    };

    this._init_grid_cell_draggable = () => {
        $(".grid-col .dropped-task", assignmentWrapEle.find('.grid-row')).draggable({
            revert: "invalid", // when not dropped, the item will revert back to its initial position
            containment: "document",
            helper: "clone",
            cursor: "move",
            start: function (event, ui) {
                $(ui.helper).css('width', $(event.target).outerWidth());
                $(ui.helper).css('height', $(event.target).outerHeight());
            },
        });
    };

    this._init_droppable = () => {
        $('.grid-row > div').droppable({
            accept: "#tasks > .task, #saved-tasks > .task, .grid-col .dropped-task",
            classes: {
                "ui-droppable-active": "ui-state-highlight",
                "ui-droppable-hover": "ui-state-hover"
            },
            drop: (e, ui) => {
                this._assign_task_in_grid(e, ui.draggable);
            }
        });

        // Assignments --> Tasks & Saved Tasks
        $('#card-tasks > .card-body, #card-saved-tasks > .card-body').droppable({
            accept: "#assignments-wrap .dropped-task",
            classes: {
                "ui-droppable-active": "ui-state-highlight hide-content",
                "ui-droppable-hover": "ui-state-hover"
            },
            drop: (e, ui) => {
                this.dnd_assignments_to_tasks(e, ui.draggable);
            }
        });

        // Tasks --> Saved Tasks
        $('#card-saved-tasks > .card-body').droppable({
            accept: "#tasks > .task, .grid-col .dropped-task",
            classes: {
                "ui-droppable-active": "ui-state-highlight hide-content",
                "ui-droppable-hover": "ui-state-hover"
            },
            drop: (e, ui) => {
                this.dnd_tasks_to_saved_tasks(e, ui.draggable);
            }
        });
    };

    this.get_task_by_id = (id) => {
        const tasks = _.filter(this.settings.tasks, x => x.id === id);
        let task = null;
        if (tasks.length > 0) {
            return tasks[0];
        } else {
            task = _.get(_.filter(this.settings.tuc_list, x => x.id === id), '[0]', null);
        }
        if (task == null) {
            task = _.get(_.filter(this.settings.saved_tasks, x => x.id === id), '[0]', null);
        }
        return task;
    };

    this._assign_task_in_grid = (e, $item) => {
        const $target = $(e.target);
        const task_id = $item.attr('data-id') || '';
        const task = this.get_task_by_id(task_id);

        if (task == null) {
            App.info('Invalid task');
            return;
        }
        const node = this.get_selected_category();
        if (node == null) {
            App.info("Please select a category or reload a page and try again.");
            return;
        }
        const from_tasks_panel = $item.hasClass('task');
        if (from_tasks_panel) {
            // already assigned?
            if (assignmentWrapEle.find('div.dropped-task[data-id="' + task_id + '"]').length > 0) {
                App.info('It was already assigned.');
                return;
            }
            let data = {
                task_id: task_id,
                task_category_id: node.id,
                position: $target.attr('data-position'),
            };
            App.ajax_post_ok(get_ajax_url('Task', 'create_assignment_in_map'), data, res => {
                if (!res.error) {
                    $target.append(this._html_dropped_task(task));
                    this.settings.tuc_list.push({...task, ...data});
                    this.settings.tasks = this.settings.tasks.filter(x => x.id != task_id);
                    this.render_tasks_all();
                }
            }, assignmentWrapEle.closest('.card-body'));
        } else {
            let data = {
                task_id: task_id,
                task_category_id: node.id,
                position: $target.attr('data-position'),
            };
            if (task.position == data.position) return;

            const $p = $(`.grid-col .dropped-task[data-id="${task_id}"]`);
            App.ajax_post_ok(get_ajax_url('Task', 'move_assignment_in_map'), data, res => {
                if (!res.error) {
                    $p.fadeOut(() => $p.remove());
                    $target.append(this._html_dropped_task(task));
                    const ind = _.findIndex(this.settings.tuc_list, {id: task_id});
                    if (ind > -1) this.settings.tuc_list[ind].position = data.position;
                    this._init_grid_cell_draggable();
                }
            }, assignmentWrapEle.closest('.card-body'));
        }
    };

    this.dnd_assignments_to_tasks = (e, $item) => {
        const targetEle = $(e.target);
        const task_id = $item.attr('data-id');
        const task = this.get_task_by_id(task_id);

        // If Tasks panel
        if (targetEle.find('#tasks').length > 0) {
            this.remove_assigned_task(task, 'tasks');
        } else {
            if (_.isUndefined(_.find(this.settings.saved_tasks, {id: task_id}))) {
                this.remove_assigned_task(task, 'saved-tasks');
            }
        }
    };

    this.dnd_tasks_to_saved_tasks = (e, $item) => {
        const targetEle = $(e.target);
        const task_id = $item.attr('data-id');
        const task = this.get_task_by_id(task_id);

        if (_.isUndefined(_.find(this.settings.saved_tasks, {id: task_id}))) {
            this.add_task_in_saved_tasks_panel(task, $('#card-tasks > .card-body'));
        }
    };

    this._html_dropped_task = (task) => {
        const style_str = this._get_task_styles_str(task);
        var tpl = `
            <div class="dropped-task" class="position-relative" data-id="${task.id}">
                <div class="task-title"><span style="${style_str}">${task.title}</span></div>            
                <div class="task-comment fs-z6"><span>${task.comment}</span></div>
            </div>
        `;
        return tpl;
    };

    this._get_grid_row_count = (assigned_tasks) => {
        let max_pos = 0;
        if (assigned_tasks && assigned_tasks.length > 0) {
            assigned_tasks.forEach(x => {
                if (max_pos < parseInt(x.position)) max_pos = parseInt(x.position);
            });
        }
        return Math.max(DEFAULT_ROW_COUNT, Math.ceil(max_pos / DEFAULT_COL_COUNT));
    };

    this._html_grid_panel = (assigned_tasks, grid_data) => {
        let rows_count = this._get_grid_row_count(assigned_tasks);
        rows_count = Math.max(rows_count, _.parseInt(_.get(grid_data, 'row_count', DEFAULT_ROW_COUNT)));
        var html = '';
        for (let i = 0; i < rows_count; i++) {
            html += this._html_grid_row(i);
        }

        const $html = $(html);
        if (assigned_tasks) {
            assigned_tasks.forEach(x => {
                const {id, position} = x;
                const task = this.get_task_by_id(id);
                if (task) {
                    $html.find('.grid-col-' + position).append(this._html_dropped_task(task));
                }
            });
        }
        return $html;
    };


    this._expand_grid_row = () => {
        const row_count = assignmentWrapEle.children('.grid-row').length;
        App.ajax_post_ok(get_ajax_url('Task', 'set_grid_row'), {
            row_count: row_count + 1,
            task_category_id: this.get_selected_category_id(),
        }, res => {
            if (!res.error) {
                const html = this._html_grid_row(row_count);
                assignmentWrapEle.append(html);
                this._init_droppable();
                this._update_remove_row_btn();
            }
        });
    };

    this._shrink_grid_row = () => {
        const row_cnt = assignmentWrapEle.children('.grid-row').length;
        if (row_cnt > DEFAULT_ROW_COUNT) {
            const grid_row = assignmentWrapEle.children('.grid-row').eq(row_cnt - 1);
            if (grid_row.find('div.dropped-task').length > 0) {
                App.info('You can not remove the last row. Please move an assignment into other row or delete.')
                return;
            }
            const row_count = assignmentWrapEle.children('.grid-row').length;
            App.ajax_post_ok(get_ajax_url('Task', 'set_grid_row'), {
                row_count: row_count - 1,
                task_category_id: this.get_selected_category_id(),
            }, res => {
                if (!res.error) {
                    grid_row.remove();
                    this._update_remove_row_btn();
                    this.settings.grid_data = res.data.grid_data;
                }
            });
        }
    };

    this._get_grid_row_height_setting = (row_ind) => {
        return _.parseInt(_.get(this.settings.grid_data, 'heights.' + row_ind, 1));
    };

    this._expand_grid_row_height = (e) => {
        const row_ele = $(e.target).closest('.grid-row');
        const ind = row_ele.index();
        let height = this._get_grid_row_height_setting(ind);

        App.ajax_post_ok(get_ajax_url('Task', 'set_grid_row_height'), {
            row_ind: ind,
            height: height + 1,
            task_category_id: this.get_selected_category_id(),
        }, res => {
            if (!res.error) {
                this.settings.grid_data = res.data.grid_data;
                this._update_grid_row_styles(ind, this._get_grid_row_height_setting(ind));
            }
        });
    };

    this._shrink_grid_row_height = (e) => {
        const row_ele = $(e.target).closest('.grid-row');
        const ind = row_ele.index();
        let height = this._get_grid_row_height_setting(ind);
        if (height == 1) return;

        App.ajax_post_ok(get_ajax_url('Task', 'set_grid_row_height'), {
            row_ind: ind,
            height: Math.max(height - 1, 1),
            task_category_id: this.get_selected_category_id(),
        }, res => {
            if (!res.error) {
                this.settings.grid_data = res.data.grid_data;
                this._update_grid_row_styles(ind, this._get_grid_row_height_setting(ind));
            }
        });
    };
    this._update_grid_row_styles = (row_ind, height) => {
        const row_ele = assignmentWrapEle.children().eq(row_ind);
        if (row_ele.length > 0) {
            if (_.isUndefined(height)) height = this._get_grid_row_height_setting(row_ind);
            row_ele.css('min-height', height * DEFAULT_ROW_HEIGHT);
        }
    };

    this._update_grid_rows_styles = () => {
        const count = assignmentWrapEle.children().length;
        for (let i = 0; i < count; i++) {
            this._update_grid_row_styles(i);
        }
    };

    this._update_remove_row_btn = () => {
        // Remove row option.
        const row_count = assignmentWrapEle.children('.grid-row').length;
        if (row_count > DEFAULT_ROW_COUNT) {
            const new_row_btn = $('#act-new-grid-row');
            const remove_row_btn = new_row_btn.next();
            if (remove_row_btn.length < 1) {
                new_row_btn.after(`<button class="btn btn-sm btn-sm-td btn-outline-danger ml-2" id="act-delete-grid-row">Delete Row</button>`);
            }
        } else {
            $('#act-delete-grid-row').remove();
        }
    };

    /**
     * Create a task in ShortCut panel at the top of page.
     * @param e
     */
    this.on_click_create_task_sc = (e) => {
        e.preventDefault();
        let data = {
            id: '',
            title: $('#new-name').val(),
            comment: $('#new-comment').val()
        };
        if (data.title.length < 1) {
            App.info('Please fill Name.');
            $('#new-name').focus();
            return;
        }

        App.ajax_post_ok(get_ajax_url('Task', 'save'), {
            data: data,
        }, (res) => {
            if (!res.error) {
                this.settings.tasks = [res.data.row, ...this.settings.tasks];
                tasksEle.find('div.header').after(this._html_task(res.data.row));
                this._init_draggable();
                $('#new-task-sc-wrap input').val('');
            }
        }, $('#new-task-sc-wrap'));
    };

    /**
     * onClick event handler on create icon button.
     * Create / Update task info.
     *
     * @param e
     * @param item
     */
    this.on_click_update_task = (e, item) => {
        const isNew = typeof item === 'undefined';
        if (isNew) {
            item = {};
        }
        const cont = this._html_task_form(item);
        const get_task_user_data = ($el) => {
            return {
                font_size: $el.find('[name="font_size"]').val(),
                font_style: $el.find('[name="font_style"]').val(),
                text_decoration: $el.find('[name="text_decoration"]').val(),
                font_weight: $el.find('[name="font_weight"]:checked').val() || null,
                color: $el.find('[name="color"]').val(),
                background: $el.find('[name="background"]').val(),
            }
        };

        const dlgObj = bs4pop.dialog({
            id: 'dlg-create-task',
            title: '<i class="oi oi-task"></i> ' + (isNew ? 'Create a Task' : 'Update Task'),
            content: cont,
            backdrop: 'static',
            className2: 'modal-dialog-scrollable',
            width: 600,
            onShowEnd: (e) => {
                const $el = dlgObj.$el;
                const $input = $el.find('input:first');
                $input.focus();
                init_datepicker($el.find('.d-picker'));
                init_tooltip($el.find('i.oi-info'));

                $el.find('input[type="color"]').change(e => {
                    $el.find('.preview').css({
                        'background': $el.find('input[name="background"]').val(),
                        'color': $el.find('input[name="color"]').val(),
                    });
                });

                $el.find('.title, input, select').change(e => {
                    $el.find('.preview').html($el.find('.title').val());
                    const styles = get_task_user_data($el);
                    const style_str = this._get_task_styles_str(null, styles);
                    $el.find('.preview').attr('style', style_str);
                });
                $el.find('.title').change();
            },
            btns: [{
                label: isNew ? 'Create' : 'Update',
                onClick: (evt) => {
                    const $el = dlgObj.$el;
                    let data = {
                        id: item.id || '',
                        title: $el.find('.title').val(),
                        comment: $el.find('.comment').val(),
                        prio: $el.find('.prio').val(),
                        due_date: $el.find('.due_date').val(),
                        status: $el.find('[name="status"]:checked').val(),
                    };
                    App.ajax_post_ok(get_ajax_url('TaskAction', 'save'), {
                        data: data,
                        task_user_data: get_task_user_data($el),
                    }, (res) => {
                        if (!res.error) {
                            if (isNew) {
                                if (res.data.row.status == 1) {
                                    this.settings.tasks = [res.data.row, ...this.settings.tasks];
                                }
                                tasksEle.find('div.header').after(this._html_task(res.data.row));
                            } else {
                                this.update_task_data(res.data.row);
                                this._init_assignments_dnd();
                            }
                            this._init_draggable();
                            dlgObj.hide();
                        }
                    }, dlgObj.$el);
                    return false;
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    };

    this._html_task_form = (item) => {
        const options_html = HtmlUtil.radio_yes_no('status', item.status, '');
        let font_sizes = [];
        for (let i = 8; i < 15; i++) {
            font_sizes.push({
                id: i + 'px',
                name: i + 'px',
            });
        }
        let font_styles = [
            {id: 'font-style|normal', name: 'Normal'},
            {id: 'font-style|italic', name: 'Italic'},
        ];
        let text_decorations = [
            {id: 'text-decoration|none', name: ''},
            {id: 'text-decoration|underline', name: 'Underline'},
            {id: 'text-decoration|line-through', name: 'Strikethrough'},
        ];

        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Title</label>
                    <div class="col-9">
                        <input class="form-control form-control-sm title" type="text" value="${item.title || ''}" />                                     
                    </div>
                </div> 
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Comment</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm comment" rows="4">${item.comment || ''}</textarea>                                                  
                    </div>
                </div>
                <div class="form-group row mt-3">                
                    <label class="col-3 col-form-label-sm">Due Date</label>
                    <div class="col-auto">
                        <input class="form-control form-control-sm fw-100 d-picker due_date" type="text" value="${CvUtil.dtValidYMD(item.due_date || '')}" />                                     
                    </div>
                    <label class="col-auto col-form-label-sm">Prio</label>
                    <div class="col-auto p-0">
                        <input class="form-control form-control-sm fw-50 text-right prio" type="text" value="${item.prio || ''}" />                                     
                    </div>
                    <label class="col-auto col-form-label-sm">Status</label>
                    <div class="col-auto p-0 mt-1">
                        ${options_html}                                     
                    </div>
                </div> 
                   
                <hr />
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Font Style</label>
                    <div class="col-auto">
                        ${HtmlUtil.dropdown('font_style', font_styles, _.get(item, 'task_user_data.font_style', 'normal'))}                                                         
                    </div>
                    <div class="col-auto">
                        ${HtmlUtil.dropdown('font_size', font_sizes, _.get(item, 'task_user_data.font_size', '10px'))}                                                         
                    </div>
                    <div class="col-auto">
                        <div class="form-check d-inline-block mt-1">
                            <input type="checkbox" name="font_weight" id="font_weight" value="bold" class="form-check-input"
                                ${_.get(item, 'task_user_data.font_weight', '') == 'bold' ? ' checked="checked"' : ''}
                            />
                            <label for="font_weight" class="form-check-label">Bold?</label>
                        </div>
                    </div>        
                    <div class="col-auto">
                        ${HtmlUtil.dropdown('text_decoration', text_decorations, _.get(item, 'task_user_data.text_decoration', 'none'), ' class="form-control form-control-sm fw-100"')}                                                         
                    </div>            
                </div> 
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Color</label>
                    <div class="col-2">
                        <input type="color" name="color" value="${_.get(item, 'task_user_data.color', '#212529')}" />                                                         
                    </div>
                    <label class="col-3 col-form-label-sm">Background Color</label>
                    <div class="col-auto">
                        <input type="color" name="background" value="${_.get(item, 'task_user_data.background', DEFAULT_BACKGROUND)}" />      
                    </div>
                </div> 
                <div class="form-group row">
                    <label class="col-3 col-form-label-sm"></label>
                    <div class="col-auto">
                        <div  class="preview">${item.title}</div>
                    </div>
                </div>
            `;
        return cont;
    };

    this.update_task_data = (task) => {
        // Tasks panel
        let ind = _.findIndex(this.settings.tasks, {id: task.id});
        if (ind > -1) {
            if (task.status == 1) {
                this.settings.tasks[ind] = task;
                tasksEle.find('div[data-id="' + task.id + '"]').html($(this._html_task(task)).html());
            } else {
                _.remove(this.settings.tasks, (el) => el.id == task.id);
                tasksEle.find('div[data-id="' + task.id + '"]').remove();
            }
        }
        // Saved Tasks panel
        ind = _.findIndex(this.settings.saved_tasks, {id: task.id});
        if (ind > -1) {
            if (task.status == 1) {
                this.settings.saved_tasks[ind] = task;
                savedTasksEle.find('div[data-id="' + task.id + '"]').html($(this._html_task(task, 'saved')).html());
            } else {
                _.remove(this.settings.saved_tasks, (el) => el.id == task.id);
                savedTasksEle.find('div[data-id="' + task.id + '"]').remove();
            }
        }

        // Assigned Tasks List
        ind = _.findIndex(this.settings.tuc_list, {id: task.id});
        if (ind > -1) {
            if (task.status == 1) {
                this.settings.tuc_list[ind] = task;
                assignmentWrapEle.find('div.dropped-task[data-id="' + task.id + '"]').html($(this._html_dropped_task(task)).html());
            } else {
                _.remove(this.settings.tuc_list, (el) => el.id == task.id);
                assignmentWrapEle.find('div.dropped-task[data-id="' + task.id + '"]').remove();
            }
        }
    };

    /**
     * Render tasks lists in Task panel or Saved Tasks List panel.
     *
     * @param mode
     */
    this.render_tasks_all = (mode) => {
        const wrapEle = _.isUndefined(mode) ? tasksEle : savedTasksEle;
        const data = _.isUndefined(mode) ? this.settings.tasks : this.settings.saved_tasks;

        wrapEle.children('div.task').remove();
        if (mode != 1 && wrapEle.find('div.header').length < 1) {
            wrapEle.append(this._html_tasks_panel_header());
        }
        if (data) {
            data.forEach(x => {
                wrapEle.append(this._html_task(x, mode));
            });
        }
        this._init_draggable(mode);
    };

    this._html_tasks_panel_header = () => {
        var tpl = `
            <div class="form-row header">
                <div class="col-3">Title</div>
                <div class="col-5">Comment</div>
                <div class="col-2">Prio</div>
                <div class="col-2">Due</div>
            </div>
        `;
        return tpl;
    };

    this._get_task_style_by_key = (task, key) => {
        const styles = _.get(task, 'task_user_data', null);
        return _.get(styles, key, null);
    };

    this._get_task_style_str_by_key = (key, value, styles) => {
        let style_str = '';
        let map = null;
        switch (key) {
            case 'font_size':
                style_str += `font-size: ${value};`;
                break;
            case 'font_weight':
                style_str += `font-weight: ${value};`;
                break;
            case 'font_style':
                map = _.split(value, '|');
                if (_.isArray(map)) {
                    style_str += `${map[0]}: ${map[1]};`;
                }
                break;
            case 'text_decoration':
                map = _.split(value, '|');
                if (_.isArray(map)) {
                    style_str += `${map[0]}: ${map[1]};`;
                }
                break;
            case 'color':
                style_str += `${key}: ${value};`;
                break;
            case 'background':
            case 'new_background':
                let bg = value;
                const new_background = _.get(styles, 'new_background', null);
                if (new_background != null) {
                    bg = new_background;
                }
                if (bg != null) {
                    style_str += `background: ${bg};`;
                }
                break;
        }
        return style_str;
    };

    this._get_task_styles_str = (task, styles) => {
        let style_str = '';
        styles = _.isNull(task) ? styles : _.get(task, 'task_user_data', null);
        if (styles != null && _.isObject(styles)) {
            _.mapKeys(styles, (value, key) => {
                style_str += this._get_task_style_str_by_key(key, value, styles);
            });
            style_str = `${style_str}`;
        }
        return style_str;
    };

    /**
     * Get html of a task.
     *
     * @param task
     * @param mode  If undefined, it means Tasks Panel, otherwise Saved Tasks Panel
     * @returns {string}
     * @private
     */
    this._html_task = (task, mode) => {
        const task_ele_id = _.isUndefined(mode) ? task.id : 'saved' + task.id;
        let controls = '';
        if (!_.isUndefined(mode)) {
            controls = `<i class="oi oi-delete act-remove-saved-task"></i>`;
        }
        const style_str = this._get_task_styles_str(task);
        let tpl = `
            <div class="form-row task" id="${task_ele_id}" data-id="${task.id}">
                <div class="col-3 task-title"><span style="${style_str}">${task.title}</span></div>
                <div class="col-5 task-comment"><span>${task.comment}</span></div>
                <div class="col-2 task-prio">${task.prio}</div>
                <div class="col-2 task-due_date">${CvUtil.dtNiceDMY(task.due_date)}</div>
                ${controls}
            </div>
        `;
        return tpl;
    };

    this._html_grid_row = row_ind => {
        const start_pos = row_ind * DEFAULT_COL_COUNT;
        let html = '';
        html += `<div class="form-row grid-row">`;
        for (let j = 0; j < DEFAULT_COL_COUNT; j++) {
            const pos = start_pos + j;
            html += `<div class="col-3 grid-col grid-col-${pos}" data-position="${pos}"></div>`;
        }
        html += `
            <i class="oi oi-plus tt" title="Expand row height."></i><i class="oi oi-minus" title="Shrink row height."></i>
        `;
        html += `</div>`;
        return html;
    };

    this.get_selected_category_id = () => {
        const node = this.get_selected_category();
        return _.get(node, 'id', null);
    };

    this.get_selected_category = () => {
        const node = catTree.jstree('get_selected', true);
        if (node.length < 1) {
            return null;
        }
        return node[0];
    };

    this.remove_assigned_task = (task, target) => {
        const node = this.get_selected_category();
        if (node == null) {
            App.info('Please select the category and then try again.');
        }
        if (task) {
            if (target == 'tasks') {
                App.ajax_post_ok(get_ajax_url('Task', 'delete_assignment_in_map'), {
                    task_id: task.id,
                    task_category_id: node.id,
                }, (res) => {
                    if (res.error == false) {
                        const $p = assignmentWrapEle.find('div.dropped-task[data-id="' + task.id + '"]');
                        $p.fadeOut(function () {
                            $p.remove();
                        });
                        this.settings.tasks = [{
                            ...task,
                            task_category_id: null,
                            position: null
                        }, ...this.settings.tasks];
                        this.settings.tuc_list = this.settings.tuc_list.filter(x => x.id != task.id);
                        this.render_tasks_all();
                    }
                }, assignmentWrapEle.closest('.card-body'));
            } else {
                this.add_task_in_saved_tasks_panel(task);
            }
        } else {
            App.info('Invalid task. Please reload a page list and try again.');
        }
    };

    /**
     * Add a task in User saved tasks list (Saved Panel)
     *
     * @param task
     */
    this.add_task_in_saved_tasks_panel = (task, sourceEle) => {
        const blockEle = _.isUndefined(sourceEle) ? assignmentWrapEle.closest('.card-body') : sourceEle;
        App.ajax_post_ok(get_ajax_url('Task', 'save_task_in_user_saved_list'), {
            task_id: task.id
        }, (res) => {
            if (res.error == false) {
                this.settings.saved_tasks = res.data.rows;
                this.render_tasks_all(SAVED_TASKS);
            }
        }, blockEle);
    };

    /**
     * Add a task in User saved tasks list (Temp panel)
     *
     * @param task
     */
    this.empty_user_saved_list = () => {
        if (_.isEmpty(this.settings.saved_tasks)) {
            return;
        }
        App.ajax_post_ok(get_ajax_url('Task', 'empty_user_saved_list'), {}, (res) => {
            if (res.error == false) {
                this.settings.saved_tasks = [];
                this.render_tasks_all(SAVED_TASKS);
            }
        }, savedTasksEle);
    };

    /**
     * Delete a task in Saved Tasks Panel
     * @param task_id
     * @param task_category_id
     */
    this.delete_saved_task = (task_id) => {
        App.ajax_post_ok(get_ajax_url('Task', 'delete_saved_task'), {
            task_id: task_id
        }, (res) => {
            if (res.error == false) {
                //_.remove(this.settings.saved_tasks, (el) => el.id == task_id);
                this.settings.saved_tasks = res.data.rows;
                this.render_tasks_all(SAVED_TASKS);
            }
        }, savedTasksEle);
    };

    this.reload_tasks = e => {
        let post_data = {};
        // Setting sort params in case of not specified.
        if (!('order_field' in post_data))
            post_data['order_field'] = this.settings.order_field || '';

        if (!('order_dir' in post_data))
            post_data['order_dir'] = this.settings.order_dir || '';

        post_data['unassigned_only'] = 1;
        post_data['status'] = 1;

        // make an ajax call.
        App.ajax_post_ok(get_ajax_url('TaskAction', 'get_list'), post_data, (res) => {
            if (!res.error) {
                this.settings.tasks = res.data.rows;
                this.render_tasks_all();
                this._init_assignments_dnd();
            }
        }, $('#tasks-wrap').closest('.card'));
    };


    this.reload_categories = e => {
        // make an ajax call.
        App.ajax_post_ok(get_ajax_url('ConfigTaskCategory', 'get_categories'), {}, (res) => {
            if (!res.error) {
                this.settings.category_tree = res.data.category_tree;
                this.refresh_tree(e);
            }
        }, catTree);
    };

    this.refresh_tree = e => {
        catTree.jstree(true).settings.core.data = this.settings.category_tree;
        catTree.jstree(true).refresh();
    };

    this.reload_saved_tasks = e => {
        let post_data = {};
        // Setting sort params in case of not specified.
        if (!('order_field' in post_data))
            post_data['order_field'] = this.settings.order_field || '';

        if (!('order_dir' in post_data))
            post_data['order_dir'] = this.settings.order_dir || '';
        post_data['status'] = 1;

        // make an ajax call.
        App.ajax_post_ok(get_ajax_url('TaskAction', 'get_user_saved_list'), post_data, (res) => {
            if (!res.error) {
                this.settings.saved_tasks = res.data.rows;
                this.render_tasks_all(1);
                this._init_assignments_dnd();
            }
        }, $('#tasks-wrap').closest('.card'));
    };
}
