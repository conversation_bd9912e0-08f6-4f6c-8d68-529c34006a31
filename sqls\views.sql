CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_latest_comment_dates` AS
(
select `c`.`supplier_id` AS `supplier_id`, max(`c`.`updated_on`) AS `latest_updated_on`
from (`supplier_comments` `c` USE INDEX (`supplier_id_updated_on`) join `sys_config` `sc` on (`sc`.`code` = `c`.`code`))
where `sc`.`type` = 'status'
  and `sc`.`sc_contact` = 1
group by `c`.`supplier_id`);


CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_suppliers` AS
(
select `s`.`id`               AS `id`,
       `s`.`org_a`            AS `org_a`,
       `s`.`name`             AS `name`,
       `s`.`supp_supplier_id` AS `supp_supplier_id`,
       `c`.`comment`          AS `comment`,
       `c`.`updated_on`       AS `updated_on`,
       `c`.`updated_by`       AS `updated_by`,
       `c`.`created_on`       AS `created_on`,
       `c`.`code`             AS `status`,
       `conf`.`name`          AS `status_name`,
       `c`.`created_by`       AS `created_by`,
       `u`.`username`         AS `created_by_name`,
       `u`.`display_name`     AS `created_by_disp_name`
from ((((`suppliers` `s`
    left join `v_latest_comment_dates` `lcd`
         on (`lcd`.`supplier_id` = `s`.`id`))
    left join `supplier_comments` `c`
        on (`c`.`supplier_id` = `lcd`.`supplier_id`
            and `c`.`code` <> 90
            and `c`.`updated_on` = `lcd`.`latest_updated_on`))
    left join `sys_config` `conf`
       on (`conf`.`code` = `c`.`code`))
    left join `users` `u`
      on (`c`.`created_by` = `u`.`id`))
group by `s`.`id`);

