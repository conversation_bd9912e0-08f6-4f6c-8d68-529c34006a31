<?php

function table_supplier_comments($rows)
{
    $statuses = SysConfigModel::get_instance()->get_code(SysConfigModel::CONFIG_TYPE_STATUS);
    $users =  SimDB::get_instance()->query_select_dropdown('SELECT id, display_name FROM users', 'id', 'display_name');

    $head = '<tr>';
    $head .= "<th class='text-center'>No</th>";
    $head .= "<th>Comment</th>";
    $head .= "<th>Status</th>";
    $head .= "<th>Offer ID</th>";
    $head .= "<th>Offer</th>";
    $head .= "<th>Customer Order</th>";
    $head .= "<th>Created on</th>";
    $head .= "<th>Created by</th>";
    $head .= "<th>Updated on</th>";
    $head .= "<th>Updated by</th>";
    $head .= "</tr>";

    $ind = 1;
    $body = '';
    if (!empty($rows)) {
        foreach ($rows as $row) {
            $status = $statuses[$row['code']]['name'] ?? '';
            $comment = $row['comment'];

            $body .= '<tr>';
            $body .= "<td>" . $ind++ . "</td>";
            $body .= "<td><div class='fw-150'>" . $comment . "</div></td>";
            $body .= "<td class='text-center'><span class='badge badge-info'>" . $status . "</span></td>";
            $body .= "<td class='text-center'><span class='badge badge-success'>" . $row['offer_sid'] . "</span></td>";
            $body .= "<td>" . $row['offer'] . "</td>";
            $body .= "<td>" . $row['customer_order_desc'] . "</td>";
            $body .= "<td><div class='fw-80 text-center'>" . dt_nice_dmy($row['created_on']) . "</div></td>";
            $body .= "<td><div class='fw-60 text-center c-lightgrey'>" . ($users[$row['created_by'] ?? ''] ?? '') . "</div></td>";
            $body .= "<td><div class='fw-80 text-center'>" . dt_nice_dmy($row['updated_on']) . "</div></td>";
            $body .= "<td><div class='fw-60 text-center c-lightgrey'>" . ($users[$row['updated_by'] ?? ''] ?? '') . "</div></td>";
            $body .= '</tr>';
        }
    }

    $html_tpl =  '
        <table id="comments" class="data-table border-0 w-100">
            <thead>%s</thead>
            <tbody>%s</tbody>    
        </table>';

    return sprintf($html_tpl, $head, $body);
}