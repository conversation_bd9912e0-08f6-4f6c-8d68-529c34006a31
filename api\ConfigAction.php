<?php

/**
 * Class ConfigAction For comment_status
 */
class ConfigAction extends BaseAction
{
    protected $config_model = null;

    function __construct()
    {
        parent::__construct();
        $this->config_model =& Loader::get_instance()->load_model('SysConfigModel');
    }

    /**
     * Save Config row record.
     */
    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $isNew = ($row['id'] ?? '') ? false : true;

        if (!isset($row['type'])) {
            $row['type'] = SysConfigModel::CONFIG_TYPE_STATUS;
        }

        // validation
        /*if ($row['type'] == SysConfigModel::CONFIG_TYPE_STATUS && strlen($row['code'] ?? '') < 1) {
            $this->return(true, "Please fill code.");
        }*/
        if (strlen($row['name'] ?? '') < 1) {
            $this->return(true, "Please fill name.");
        }

        $id = $row['id'] ?? '';
        if ($isNew) {
            unset($row['id']);
        } else {
            $row_old = $this->config_model->get($id);
            if (!$row_old) {
                $this->return(true, 'Invalid request. Entry does not exist.');
            }
        }

        // Saving
        if ($isNew) {
            $success = false;
            $row['id'] = $this->config_model->gen_pk();

            if ($row['type'] != SysConfigModel::CONFIG_TYPE_STATUS) {
                $row['code'] = $row['id'];
            }
            if (!($row['code'] ?? null)) {
                $row['code'] = $row['id'];
            }
            $id = $this->config_model->insert($row);
            if ($id) {
                $row['id'] = $id;
                $success = true;
            }
        } else {
            $success = $this->config_model->update($row, $id);
        }

        if ($success) {
            $this->data = $this->config_model->get($id);
            $this->msg = $isNew ? 'Created successfully.' : 'Updated Successfully.';
        } else {
            $this->error = true;
            $this->msg = 'Failed to save status info.';
        }

        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->config_model->get($id);
        if (!empty($row)) {
            $this->config_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get a table of list
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        if (isset($_POST['type'])) {

        } else {
            if (!isset($_POST['types'])) {
                $_POST['type'] = SysConfigModel::CONFIG_TYPE_STATUS;
            }
        }

        $this->data['rows'] = $this->config_model->get_list($_POST, ' ORDER BY `order`');
        $this->data['sql'] = $this->db->show_query_history(false);

        $format = $_POST['format'] ?? 'json';
        if ($format == 'json') {
            $this->return();
        }
        $this->return();
        exit;
    }
}