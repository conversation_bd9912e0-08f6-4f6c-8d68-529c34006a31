<?php

/**
 * Class AccessLogAction
 */
class AccessLogAction extends BaseAction
{
    /**
     * @var AccessLogModel
     */
    private $access_log_model = null;

    function __construct()
    {
        parent::__construct();

        $this->access_log_model =& Loader::get_instance()->load_model('AccessLogModel');
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->access_log_model->get($id, 1);
        if (!empty($row)) {
            $this->access_log_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $rows = $this->access_log_model->get_list($_POST);
        $this->data = [
            'rows' => $rows,
            'sql' => $this->db->show_query_history(false)
        ];
        $this->return();
    }

    public function view_detail()
    {
        $this->post_restrict();
        $this->msg = '';

        $this->data['html'] = '';
        $id = $_POST['id'] ?? '';
        if (empty($id)) {
            $this->data['html']  = "<h6>Invalid request!</h6>";
        }

        $s = $this->access_log_model->get($id);
        if (empty($s)) {
            $this->data['html']  = "<h6>Invalid request! Log does not exist!</h6>";
        } else {
            Loader::get_instance()->load_helper('access_log_helper');
            $this->data['html'] = view_access_log_detail($s);
        }
        $this->return();
    }

}