<?php

/**
 * Class InvoiceAction
 */
class InvoiceAction extends BaseAction
{
    /**
     * @var InvoiceModel
     */
    private $invoice_model = null;

    function __construct()
    {
        parent::__construct();

        $this->invoice_model =& load_model('InvoiceModel');
    }

    public function save()
    {
        $this->post_restrict();

        $row = $_POST;
        $invoice_no = $row['invoice_no'] ?? '';

        if (empty($invoice_no)) {
            $this->return(true, "Please fill Invoice No.");
        }

        // permission checking
        if (!Auth::has_permission(PID_INVOICE_PAYMENT_EDIT)) {
            if (isset($row['status'])) unset($row['status']);
            if (isset($row['date_of_payment'])) unset($row['date_of_payment']);
        }

        // data normalization
        if (isset($row['date_of_payment']) && !$row['date_of_payment']) {
            $row['date_of_payment'] = NULL;
        }
        if (isset($row['date_of_invoice']) && !$row['date_of_invoice']) {
            $row['date_of_invoice'] = NULL;
        }

        $id = $row['id'] ?? null;
        if (empty($id)) {
            /*// validation
            $x = $this->invoice_model->get_by_field('id', $offer_sid, 1);
            if ($x) {
                $this->return(true, "This offer '" . $offer_sid . "' already exists!");
            }*/
            $success = false;
            unset($row['id']);
            $id = $this->invoice_model->insert($row, true, true, true, true);
            if ($id) {
                $success = true;
            }
        } else {
            // validation
            $x = $this->invoice_model->get($id);
            if (!$x) {
                $this->return(true, "This invoice does not exist.");
            }
            $success = $this->invoice_model->update($row, $id, true);
        }
        $this->data['row'] = $this->invoice_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();

        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->invoice_model->get($id, 1);
        if (!empty($row)) {
            $this->invoice_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }

        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();

        $this->msg = '';
        $rows = $this->invoice_model->get_list($_POST);
        $this->data = [
            'rows' => $rows,
            'sql' => $this->db->show_query_history(false)
        ];
        $this->data['sql'] = $this->db->show_query_history(false);

        $this->return();
    }

}