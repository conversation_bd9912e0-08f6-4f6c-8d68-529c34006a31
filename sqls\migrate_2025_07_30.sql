ALTER TABLE `suppliers`
    drop column org_b;

DELIMITER $$

ALTER ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `v_suppliers` AS (
SELECT
  `s`.`id`               AS `id`,
  `s`.`org_a`            AS `org_a`,
  `s`.`name`             AS `name`,
  `s`.`supp_supplier_id` AS `supp_supplier_id`,
  `c`.`comment`          AS `comment`,
  `c`.`updated_on`       AS `updated_on`,
  `c`.`updated_by`       AS `updated_by`,
  `c`.`created_on`       AS `created_on`,
  `c`.`code`             AS `status`,
  `conf`.`name`          AS `status_name`,
  `c`.`created_by`       AS `created_by`,
  `u`.`username`         AS `created_by_name`,
  `u`.`display_name`     AS `created_by_disp_name`
FROM ((((`suppliers` `s`
      LEFT JOIN `v_latest_comment_dates` `lcd`
        ON (`lcd`.`supplier_id` = `s`.`id`))
     LEFT JOIN `supplier_comments` `c`
       ON (`c`.`supplier_id` = `lcd`.`supplier_id`
           AND `c`.`code` <> 90
           AND `c`.`updated_on` = `lcd`.`latest_updated_on`))
    LEFT JOIN `sys_config` `conf`
      ON (`conf`.`code` = `c`.`code`))
   LEFT JOIN `users` `u`
     ON (`c`.`created_by` = `u`.`id`))
GROUP BY `s`.`id`)$$

DELIMITER ;