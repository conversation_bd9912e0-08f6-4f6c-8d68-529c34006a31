<?php

/**
 * Class SupplierAction
 */
class SupplierAction extends BaseAction
{
    /**
     * @var SupplierModel
     */
    private $supplier_model = null;

    function __construct()
    {
        parent::__construct();

        /**
         * SupplierModel
         */
        $this->supplier_model =& Loader::get_instance()->load_model('SupplierModel');
    }

    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $name = trim($row['name'] ?? '');

        if (empty($name)) {
            $this->return(true, "Please fill Number.");
        }

        $id = $row['id'] ?? null;

        if (empty($id)) {
            $this->perm_restrict(PID_SUPPLIER_CREATE);

            if ($this->supplier_model->get_by_field('name', $name)) {
                $this->error = true;
                $this->msg = 'Name: ' . $name . ' already exists!';
                $this->return();
            }

            $success = false;
            unset($row['id']);
            $id = $this->supplier_model->insert($row, true, false, true);
            if ($id) {
                $success = true;
            }
        } else {
            $this->perm_restrict(PID_SUPPLIER_EDIT);

            $existed_one = $this->supplier_model->get_by_where("name="
                . $this->supplier_model->get_escape_string($name, true)
                . " AND id != " . $this->supplier_model->get_escape_string($id, true)
            );
            if ($existed_one) {
                $this->error = true;
                $this->msg = 'Name: ' . $name . ' already exists!';
                $this->return();
            }

            $success = $this->supplier_model->update($row, $id, true);
        }
        $this->data['row'] = $this->supplier_model->get_with_latest_comment($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->supplier_model->get($id, 1);
        if (!empty($row)) {
            $this->supplier_model->delete($id, 'supplier_comments', 'supplier_id');
            $this->supplier_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list_with_form_data()
    {
        $this->post_restrict();
        $this->msg = '';

        $with = $_POST['with'] ?? '';
        $params_with = [
            SupplierModel::WITH_LATEST_COMMENT,
            SupplierModel::WITH_TOP5_NO_CONTACTS,
            SupplierModel::WITH_TOP3_COMMENTS,
            SupplierModel::WITH_OFFERS
        ];
        // Get totals
        $this->supplier_model->calc_count(TRUE);
        $rows_count = $this->supplier_model->get_list($_POST, $params_with);

        // Get limited rows
        $this->supplier_model->set_pager($this->get_pagination($rows_count));
        $this->supplier_model->calc_count(FALSE);
        $rows = $this->supplier_model->get_list($_POST, $params_with);

        if ($_POST['inc_supp_supplier'] ?? null) {
            if ($rows) {
                /** @var SuppSupplierModel $supp_supplier_model */
                $supp_supplier_model =& Loader::get_instance()->load_model('SuppSupplierModel');

                $suppIds = [];
                foreach ($rows as $x) {
                    if ($x['supp_supplier_id']) {
                        $suppIds[$x['supp_supplier_id']] = true;
                    }
                }

                $suppIds = array_keys($suppIds);
                if ($suppIds) {
                    $suppliersKv = $supp_supplier_model->get_list(['ids' => $suppIds], [], 'id');

                    foreach ($rows as $ind => $row) {
                        $suppId = $row['supp_supplier_id'];
                        if ($suppId) {
                            $rows[$ind]['supp_supplier'] = $suppliersKv[$suppId] ?? null;
                        }
                    }
                }
            }
        }

        $this->data = [
            'rows' => $rows,
            'pager' => $this->pagination->getPagerResponse(),
            'sql' => $this->db->show_query_history(false)
        ];

        if ($with == 'form') {
            $this->data['sql'] = $this->db->show_query_history(false);
        }

        $this->return();
    }

    public function save_comment()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $supplier_id = $row['supplier_id'] ?? '';
        if (empty($supplier_id)) {
            $this->return(true, "Invalid request!");
        }

        $s = $this->supplier_model->get($supplier_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Supplier does not exist!");
        }

        $comment_model =& Loader::get_instance()->load_model('SupplierCommentModel');

        $id = $row['id'] ?? null;
        $row['updated_on'] = time2db_datetime();
        $row['updated_by'] = Auth::current_user_id();
        $row['code'] = $row['status'];
        unset($row['status']);
        if (empty($id)) {
            $success = false;
            unset($row['id']);
            $row['created_on'] = $row['updated_on'];
            $row['created_by'] = $row['updated_by'];
            $id = $comment_model->insert($row);
            if ($id) {
                $success = true;
            }
        } else {
            $success = $comment_model->update($row, $id);
        }

        $ref_page = $_POST['ref_page'] ?? '';
        if ($ref_page == 'offers') {
            $offer_model =& Loader::get_instance()->load_model('OfferModel');
            $this->data['row'] = $offer_model->get_row($row['offer_id']);
        } else
            $this->data['row'] = $this->supplier_model->get_with_latest_comment($supplier_id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function view_comments()
    {
        $this->post_restrict();
        $this->msg = '';

        $supplier_id = $_POST['supplier_id'] ?? '';
        if (empty($supplier_id)) {
            $this->return(true, "Invalid request!");
        }

        $s = $this->supplier_model->get($supplier_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Supplier does not exist!");
        }

        $params = ['supplier_id' => $supplier_id, 'code' => $_POST['type'] ?? ''];
        $comment_model =& Loader::get_instance()->load_model('SupplierCommentModel');
        $rows = $comment_model->get_list($params);

        Loader::get_instance()->load_helper('supplier_helper');
        $this->data['html'] = table_supplier_comments($rows);

        $this->return();
    }

    /**
     * View offers by supplier ID.
     * Called on ajax.
     */
    public function view_offers()
    {
        $this->post_restrict();
        $this->msg = '';

        $supplier_id = $_POST['supplier_id'] ?? '';
        if (empty($supplier_id)) {
            $this->return(true, "Invalid request!");
        }

        $s = $this->supplier_model->get($supplier_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Supplier does not exist!");
        }

        $params = [
            'supplier_id' => $supplier_id,
            'orgAList' => -1
        ];
        $offer_model =& Loader::get_instance()->load_model('OfferModel');
        $rows = $offer_model->get_list($params);

        Loader::get_instance()->load_helper('offer_helper');
        $this->data['html'] = table_supplier_offers($rows);

        $this->return();
    }

    public function update_info()
    {
        $this->post_restrict();

        $supplier_id = get_var('supplier_id');
        $comment = get_var('comment');
        $asp = get_var('asp');
        $row = compact('supplier_id', 'comment', 'asp');

        $old = $this->supplier_model->get_by_field('supplier_id', $supplier_id, 'id', BaseModel::TBL_SUPPLIER_INFO);
        if ($old) {
            $row['id'] = $old['id'];
            $row['updated_on'] = time2db_datetime();
            $this->error = !$this->db->update(BaseModel::TBL_SUPPLIER_INFO, $row, $this->db->where_equal('id', $row['id'], ''));
        } else {
            $row['id'] = $this->db->gen_pk(BaseModel::TBL_SUPPLIER_INFO);
            $row['created_on'] = $row['updated_on'] = time2db_datetime();

            $this->error = !$this->db->insert(BaseModel::TBL_SUPPLIER_INFO, $row);
        }
        if ($this->error)
            $this->msg = 'Failed to update information.';
        $this->data['row'] = $row;
        $this->return();
    }

    /**
     *
     * Get suppliers list for auto completion list.
     */
    public function get_ac_suppliers()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';

        $where .= $this->db->where_like('name', $_POST['keyword'] ?? '', true);
        $where .= $this->db->where_equal('name', $_POST['exactName'] ?? '');
        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                id as data,
                IFNULL(`name`, ' - ') as value 
            FROM suppliers
            WHERE TRUE $where
            $limit_str
        ";
        $this->data = $this->db->query_select($sql);
        $this->return();
    }
}