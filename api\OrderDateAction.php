<?php

/**
 * Class OrderDateAction
 */
class OrderDateAction extends BaseAction
{
    /**
     * @var CustomerModel
     */
    private $order_date_model = null;

    function __construct()
    {
        parent::__construct();

        $this->order_date_model =& Loader::get_instance()->load_model('OrderDateModel');
    }

    public function save()
    {
        $this->post_restrict();

    
        $row = $_POST['data'];
        $type = $row['type'] ?? '';
        if (empty($type)) {
            $this->return(true, "Please fill Type.");
        }

        $id = $row['id'] ?? null;
        if (empty($id)) {
            // validation
            $x = $this->order_date_model->get_by_field('type', $type, 1);
            if ($x) {
                $this->return(true, "The entry '" . $type . "' already exists!");
            }
            $success = false;
            unset($row['id']);
            $id = $this->order_date_model->insert($row, false);
            if ($id) {
                $success = true;
            }
        } else {
            // validation
            $where = sprintf(" id != %s AND `type` = %s", $this->db->safe_value($id), $this->db->safe_value($type));
            $x = $this->order_date_model->get_by_where($where, 1);
            if ($x) {
                $this->return(true, "The entry '" . $type . "' already exists!");
            }
            $success = $this->order_date_model->update($row, $id, false);
        }

        $this->data['row'] = $this->order_date_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->order_date_model->get($id, 1);
        if (!empty($row)) {
            $this->order_date_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $rows = $this->order_date_model->get_list($_POST);
        $this->data = [
            'rows' => $rows,
            'sql' => $this->db->show_query_history(false)
        ];
        $this->return();
    }
}