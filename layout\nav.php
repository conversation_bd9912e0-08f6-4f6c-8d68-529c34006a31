<header class="site-header">
    <nav class="navbar navbar-expand-sm bg-light">
        <a class="navbar-brand mr-0" href="<?php echo $BASE_URL ?>"><img src="<?php echo $BASE_URL ?>/assets/favicon/favicon-32x32.png"/></a>
        <ul class="navbar-nav">
            <?php if (Auth::is_logged_in()) { ?>
                <?php if (Auth::has_permission(PID_SUPPLIERS)) { ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $BASE_URL ?>/supplier.php">Suppliers</a>
                    </li>
                <?php } ?>
                <?php if (Auth::has_permission(PID_OFFERS)) { ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $BASE_URL ?>/offer.php">Offers</a>
                    </li>
                <?php } ?>
                <?php if (Auth::has_permission(PID_ORDERS)) { ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarorders" data-toggle="dropdown">
                            Orders
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/order.php">Aufträge</a>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/order_invoice.php">Rechnungen - Ausgang</a>
                        </div>
                    </li>
                <?php } ?>
                <?php if (Auth::has_permission(PID_CUSTOMERS)) { ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $BASE_URL ?>/customer.php">Customers</a>
                    </li>
                <?php } ?>
                <?php if (Auth::has_permission(PID_TASKS)) { ?>
                    <li class="nav-item dropdown ml-4">
                        <a class="nav-link dropdown-toggle" href="#" id="navbardrop" data-toggle="dropdown">
                            Tasks
                        </a>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/task.php">Tasks Assignments</a>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/task_old.php">Tasks</a>
                            <?php if (Auth::has_permission(PID_CONFIG_TASK_CATEGORY)) { ?>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="<?php echo $BASE_URL ?>/config_task_category.php">Task Categories</a>
                            <?php } ?>
                        </div>
                    </li>
                <?php } ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbardrop" data-toggle="dropdown">
                        Config
                    </a>
                    <div class="dropdown-menu">
                        <?php if (Auth::has_permission(PID_CONFIG_STATUS)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/config_status.php">Supplier Comment Status</a>
                        <?php } ?>
                        <?php if (Auth::has_permission(PID_CONFIG_STATUS_CUSTOMER)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/config_status_customer.php">Customer Comment Status</a>
                        <?php } ?>
                        <?php if (Auth::has_permission(PID_CONFIG_STATUS_OFFER)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/config_status_offer.php">Offer Comment Status</a>
                        <?php } ?>
                        <div class="dropdown-divider"></div>
                        <?php if (Auth::has_permission(PID_CONFIG_STATUS_ORDER)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/config_status_order.php">Order Supplier & Customer Status</a>
                        <?php } ?>
                        <?php if (Auth::has_permission(PID_CONFIG_STATUS_ORDER)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/config_status_order_filter.php">Order Status Filter</a>
                        <?php } ?>
                        <?php if (Auth::has_permission(PID_ORDER_DATES)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/order_date.php">Order Dates</a>
                        <?php } ?>
                        <?php if (Auth::has_permission(PID_CATEGORIES)) { ?>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/category.php">Customer Categories</a>
                        <?php } ?>
                        <div class="dropdown-divider"></div>
                        <?php if (Auth::has_permission(PID_IMPORT)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/import.php">Import / Export</a>
                        <?php } ?>
                        <div class="dropdown-divider"></div>
                        <?php if (Auth::has_permission(PID_ACCESS_LOG)) { ?>
                            <a class="dropdown-item" href="<?php echo $BASE_URL ?>/access_log.php">Access Logs</a>
                        <?php } ?>
                    </div>
                </li>
            <?php } ?>
        </ul>
        <?php if ($user) { ?>
            <div class="pt-2 ml-5">
                <label>Hi <?php echo $user['username'] ?>,</label>
                <?php if (Auth::is_admin()) { ?>
                    <a class="" href="<?php echo $ADMIN_URL ?>/">Admin Page</a> |
                <?php } ?>
                <a href="<?php echo $BASE_URL ?>/index.php?action=logout">Logout</a>
            </div>
        <?php } ?>
    </nav>
</header>
