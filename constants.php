<?php
defined('APP_PATH') OR exit('No direct script access allowed');

define("APP_VERSION", "1.2025.0730.1");

define('DATE_FORMAT_YMD', 'Y-m-d');
define('DATE_FORMAT_YMD_HIS', 'Y-m-d H:i:s');
define('DATE_FORMAT_DMY_HI', 'd.m.y H:i');
define('DATE_FORMAT_NICE_DMY', 'd.m `y');

define('DATE_EMPTY_STR', '0000-00-00');

define('FY_START_MONTH', 7);
define('DAY_SECONDS', 24 * 3600);

/**
 * --------------------------------------------------------------------------------
 * Definition of the permission ids.
 * NOTE: Values should be 'code' column on database table "sys_permissions"
 * --------------------------------------------------------------------------------
 */
define("PERM_TYPE_MENU", 'menu');
define("PERM_TYPE_DATA", 'data');

define("PID_ALLDATA", 'ALLDATA');
define("PID_ALL_CAT", -1);
define("PID_ALLMENU", 'ALLMENU');

/**
 * --------------------------------------------------------------------------------
 * Permission IDs
 * --------------------------------------------------------------------------------
 */
define('PID_SUPPLIER_CREATE', 'Supplier Create');
define('PID_SUPPLIER_EDIT', 'Supplier Edit');
define('PID_SUPPLIERS', 'Suppliers');

define('PID_CONFIG_STATUS', 'Status Setting');
define('PID_CONFIG_STATUS_CUSTOMER', 'Customer Comment Status');
define('PID_CONFIG_STATUS_OFFER', 'Offer Comment Status');
define('PID_CONFIG_TASK_CATEGORY', 'Task Categories');
define('PID_ORDER_DATES', 'Order Dates');
define('PID_ORDERS', 'Orders');
define('PID_OFFERS', 'Offers');
define('PID_IMPORT', 'Import');
define('PID_CUSTOMERS', 'Customers');
define('PID_CATEGORIES', 'Categories');

define('PID_ACCESS_LOG', 'Access Logs');

define('PID_TASKS', 'Tasks');
define('PID_TASK_USER', 'Task User');
define('PID_TASK_USER_STATUSES', 'Task User Statuses');
define('PID_CONFIG_STATUS_ORDER', 'Order Statuses');

define('PID_INVOICE_PAYMENT_EDIT', 'Invoice Payment Edit');
define('PID_ORDER_INVOICE_OUTGOING', 'Order Invoice Outgoing');


/**
 * Access Log Type Constants
 * Note: Should be matched with `types` enum in sys_access_logs table.
 */
define('LT_VIEW', 'view');
define('LT_UPDATE', 'update');
define('LT_API', 'api');
define('LT_DELETE', 'delete');

/**
 * Request Method
 */
define('REQUEST_DELETE', 'DELETE');
define('REQUEST_POST', 'POST');
define('REQUEST_GET', 'GET');
define('REQUEST_PUT', 'PUT');

/**
 * Excel MIME types
 */
define('XLS_FILE_TYPE', 'application/vnd.ms-excel');
define('XLSX_FILE_TYPE', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');


/**
 * Listing: default limit value
 */
// t odo
define('LIMIT_COUNT', 50);


/**
 * --------------------------------------------------------------------------------
 * URL related
 * --------------------------------------------------------------------------------
 */
$is_admin_site = false;
$BASE_URL = dirname($_SERVER['SCRIPT_NAME']);
$pos = strrpos($BASE_URL, '/admin');
if ($pos !== false) {
    $BASE_URL = substr($BASE_URL, 0, $pos);
    $is_admin_site = true;
}
$ADMIN_URL = $BASE_URL . "/admin";

define('IS_ADMIN_SITE', $is_admin_site);
