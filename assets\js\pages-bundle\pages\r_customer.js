(()=>{"use strict";var e,t={3122:(e,t,n)=>{var o=n(6540),r=n(961);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function l(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||m(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=a(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,l,c=[],i=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(o=a.call(n)).done)&&(c.push(o.value),c.length!==t);i=!0);}catch(e){s=!0,r=e}finally{try{if(!i&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(s)throw r}}return c}}(e,t)||m(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}var f=o.createContext(null),p="table-customer",v={},b={cust_no:["No","",!0],name:["Name","",!0],offers:["Offers"],country:["Country","",!0],pri:["Pri","",!0],categories:["Categories",""],top3_comments:["Comment"],top5_no_contacts:["No Contacts"]},g={cust_no:50,name:150,country:80,offers:200,pri:100,categories:150,top3_comments:200,top5_no_contacts:200},y=function(e){var t=u(o.useState(e.cellData),2),n=t[0],r=t[1],a=u(o.useState(!1),2),l=(a[0],a[1]),c=e.isEditing,i=n.colName,s=n.value,m=RHelper.getColType(n.colName,b);o.useEffect((function(){l(!0)}),[]),o.useEffect((function(){r(e.cellData)}),[e.cellData]),o.useEffect((function(){var t=$("#tr-"+e.item.id+" td.name");dispose_tooltip(t),init_tooltip(t)}),[e.item.info_comment]);var d=function(e){r({colName:i,value:e.target.value})},f=function(t){setDlgTitle($g_dlg,e.item.name+"'s Comments"),setDlgBody($g_dlg,no_result),$g_dlg.modal({backdrop:"static",show:!0}),App.ajax_post_ok(get_ajax_url("CustomerAction","view_comments"),{customer_id:e.item.id,type:t||""},(function(e){e.error||setDlgBody($g_dlg,e.data.html)}),$g_dlg)},p=function(){var e=RHelper.getColType(n.colName,b),t=s||"";return null!=t&&"dt"==e&&(t=CvUtil.dtNiceDMY(t)),t},y=i,h="";if(c&&RHelper.isEditable(i,b)){var E="form-control form-control-sm"+("d"==m||"i"==m?" text-right":"")+" "+i;h=o.createElement("input",{className:E,type:"text",disabled:e.loading,value:p(),onChange:d,onInput:d,onBlur:function(t){var n=t.target.value;e.onCellChange(i,n)}})}else switch(i){case"name":h=o.createElement(o.Fragment,null,o.createElement("span",{className:"tt",title:e.item.info_comment}," ",s||""),o.createElement("i",{className:"oi oi-info m-0 float-right"+((e.item.info_comment||"").length>0?" blue-f":""),onClick:e.updateInfo,title:"Please click to update customer information."}));break;case"categories":var w="";if(null!=s){var C="";s.forEach((function(e){w+=C+e.category,C=", "}))}h=o.createElement("div",{className:"position-relative mr-2 fw-150"},w,""!=e.item.id&&o.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},o.createElement("i",{className:"oi oi-pencil oi-edit m-0",onClick:function(t){return function(){var t=Object.keys(v),n='<div class="form-row">';t.forEach((function(e){var t=v[e],o=null!=s&&s.findIndex((function(t){return t.id==e}))>=0;n+='\n                        <div class="col-4">\n                            <div class="form-check d-inline-block">\n                                <input type="checkbox" name="ids[]" id="ids'.concat(e,'" value="').concat(e,'" class="form-check-input"\n                                    ').concat(o?' checked="checked"':"",'\n                                />\n                                <label for="ids').concat(e,'" class="form-check-label">').concat(t.category,"</label>\n                            </div>\n                        </div>                    \n                ")})),n+="</div>";var o=bs4pop.dialog({title:"Edit Categories",content:n,backdrop:"static",onShowEnd:function(){},btns:[{label:"Update",onClick:function(t){var n={customer_id:e.item.id,category_ids:o.$el.find('input[type="checkbox"]:checked').map((function(){return this.value})).get()};return App.ajax_post_ok(get_ajax_url("CustomerAction","update_categories"),n,(function(t){t.error||(o.hide(),e.onCellChange(i,t.data.row.categories))}),o.$el),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})}()},title:"Please click to change categories"})));break;case"top5_no_contacts":h=o.createElement(o.Fragment,null,null!=s&&""!=s&&s.map((function(e,t){return o.createElement("div",{key:t,className:"form-row"},o.createElement("div",{className:"col-3"},CvUtil.dtNiceDMY(e.created_on)),o.createElement("div",{className:"col-9"},e.comment,o.createElement("span",{className:"c-lightgrey ml-2"},e.order_id)))})),e.item.id&&o.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},o.createElement("i",{className:"oi oi-info m-0",onClick:function(e){return f("no_contacts")},title:"Please click to view all 'Nicht erreicht' comments."}))),y+=" position-relative fs-z6";break;case"top3_comments":h=o.createElement(o.Fragment,null,null!=s&&""!=s&&s.map((function(e,t){return o.createElement("div",{key:t,className:"form-row"},o.createElement("div",{className:"col-3"},CvUtil.dtNiceDMY(e.created_on)),o.createElement("div",{className:"col-9"},e.comment,o.createElement("span",{className:"c-lightgrey ml-2"},e.order_id)))})),e.item.id&&o.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},o.createElement("i",{className:"oi oi-info m-0"+(s&&s.length>0?" blue-f":""),onClick:function(e){f("contacts")},title:"Please click to view all comments."}))),y+=" position-relative fs-z6";break;case"offers":h=o.createElement(o.Fragment,null,null!=s&&""!=s&&s.map((function(e,t){return o.createElement("div",{key:t,className:"form-row"},o.createElement("div",{className:"col-3"+("1"==e.status?"":" c-lightgrey")},e.offer_sid),o.createElement("div",{className:"col-9 tt-ajax"+("1"==e.status?"":" c-lightgrey"),id:e.offer_sid,"data-offer_sid":e.offer_sid},e.offer))}))),y+=" position-relative fs-z6";break;default:h=p()}return o.createElement("td",{title:"name"==i&&e.item.info_comment||""},o.createElement("div",{style:{width:_.get(g,i,"auto")},className:y},h))},h=function(e){var t=u(o.useState(!1),2),n=t[0],r=t[1],a=u(o.useState(e.item),2),l=a[0],c=a[1],m=u(o.useState(!1),2),d=m[0],p=m[1];o.useEffect((function(){c(e.item)}),[e.item]);var v=o.useContext(f).statuses,b=function(e,t){var n=i(i({},l),{},s({},e,t));c(n)},g=function(){var e='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="6">'.concat(l.info_comment||"","</textarea>                             \n                    </div>\n                </div>\n        "),t=bs4pop.dialog({title:"Update Customer Info",content:e,backdrop:"static",width:400,onShowEnd:function(){t.$el.find(".comment").focus()},btns:[{label:"Update",className:"btn-info btn-sm",onClick:function(e){return App.ajax_post_ok(get_ajax_url("CustomerAction","update_info"),{customer_id:l.id,comment:t.$el.find(".comment").val()},(function(e){if(0==e.error){c(i(i({},l),{},{info_comment:e.data.row.comment})),t.hide();var n=$("#tr-"+l.id).find(".tt");dispose_and_init_tooltip(n)}}),t.$el),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},_=function(e,t){App.ajax_post_ok(get_ajax_url(),{class:"CustomerAction",action:"save_comment",data:e},(function(e){0==e.error&&(c(e.data.row),t.hide())}),t.$el)};return o.createElement("tr",{id:"tr-"+l.id},e.cols.map((function(e,t){return o.createElement(y,{key:t,item:l,cellData:{colName:e,value:e in l?l[e]:null},isEditing:d&&"created_on"!=e,loading:n,updateInfo:g,onCellChange:b})})),o.createElement("td",null,o.createElement("div",{style:{width:100,textAlign:"center"}},o.createElement("i",{className:"oi oi-comment-square action tt mr-4",title:"Create a comment...",onClick:function(e){return function(e){var t="",n=Object.keys(v),o=!1,r=!1,a=!1;n.forEach((function(e){var n=v[e],l="Default"==n.sc_default_position?' checked="checked"':"";t+='\n                <div class="form-check d-block">\n                    <label class="form-check-label">\n                        <input type="radio" name="status" id="status'.concat(e,'" value="').concat(e,'" class="form-check-input status" \n                        ').concat(l,"                           \n                        /> ").concat(n.name,"\n                    </label>                    \n                </div>\n            "),"Default"==n.sc_default_position&&"1"==n.sc_customer_order&&(o=!0),"Default"==n.sc_default_position&&"1"==n.sc_contact&&(a=!0),o&&n.sc_customer_order_required&&(r=!0)}));var c='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm '.concat(a?"required":"",'">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Offer</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm offer_id" type="text" />\n                        </div>                                                  \n                    </div>\n                </div>\n                <div class="form-group row">\n                    <label class="col-3 col-form-label-sm required">Status</label>\n                    <div class="col-9">\n                        ').concat(t,'                                                             \n                    </div>\n                </div>\n                <div class="form-group row customer_order-wrap ').concat(o?"":" d-none",'">                \n                    <label class="col-3 col-form-label-sm ').concat(r?" required":"",'">Customer Order</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm customer_order" type="text" />\n                        </div>\n                    </div>\n                </div>\n            '),i=bs4pop.dialog({title:"Create a Comment of '".concat(l.name,"'"),content:c,className2:"modal-dialog-scrollable",backdrop:"static",width:500,onShowEnd:function(){i.$el.find(".comment").focus();var t=i.$el.find(".offer_id");init_autocomplete(t,{class:"OfferAction",action:"get_ac_offers"},!1,null,(function(){}),(function(e){}));var n=i.$el.find(".customer_order");init_autocomplete(n,{class:"OrderAction",action:"get_ac_orders",customer_id:e},!1,null,(function(){}),(function(e){})),i.$el.find('input[name="status"]').change((function(e){var t=e.target.value,n=v[t]||null;if(null!=n){1==n.sc_customer_order?i.$el.find(".customer_order-wrap").removeClass("d-none"):i.$el.find(".customer_order-wrap").addClass("d-none");var o=i.$el.find("textarea.comment").closest(".row").find("label");1!=n.sc_contact?o.removeClass("required"):o.addClass("required"),1==n.sc_customer_order_required?i.$el.find(".customer_order-wrap label").addClass("required"):i.$el.find(".customer_order-wrap label").removeClass("required")}}))},btns:[{label:"Create",className:"btn-info btn-sm",onClick:function(t){var n="",o=i.$el.find('input[name="status"]:checked').val(),r=v[o]||null;null!=r&&1==r.sc_customer_order&&(n=i.$el.find(".customer_order").attr("selected-val"));var a={customer_id:e,comment:i.$el.find(".comment").val(),sc_id:i.$el.find('input[name="status"]:checked').val(),offer_id:i.$el.find(".offer_id").attr("selected-val"),order_id:n};return null!=r&&1==r.sc_contact&&a.comment.length<1?(App.info("Please fill comment."),!1):a.sc_id.length<1?(App.info("Please select status."),!1):null!=r&&1==r.sc_customer_order&&1==r.sc_customer_order_required&&n.length<1?(App.info("Please fill the Customer Order."),i.$el.find(".customer_order").focus(),!1):(_(a,i),!1)}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})}(l.id)}}),o.createElement("i",{className:"oi oi-edit action tt m-0 mr-2"+(d?" oi-check":" oi-pencil"),title:"Update customer...",onClick:function(e){e.preventDefault(),d?(r(!0),c(i({},l)),App.ajax_post(get_ajax_url("CustomerAction","save"),{isNew:0,data:{id:l.id,cust_no:l.cust_no,name:l.name,country:l.country,pri:l.pri}},(function(e){r(!1),e.error||(p(!d),c(e.data.row))}),(function(e){r(!1)}))):p(!d)}}),o.createElement("i",{className:"oi oi-delete action tt m-0",title:"Delete customer.",onClick:function(t){return e.handleRowDelete(l)}}))))},E=function(e){var t=u(o.useState(!1),2),n=t[0],r=t[1],a=u(o.useState(e.item),2),l=a[0],c=a[1];o.useEffect((function(){}),[l]),o.useEffect((function(){c(e.item)}),[e.item]),o.useEffect((function(){r(e.loading)}),[e.loading]);var m=function(e,t){var n=i(i({},l),{},s({},e,t));c(n)};return o.createElement("tr",{id:"tr-"+l.id},e.cols.map((function(e,t){return o.createElement(y,{key:t,id:l.id,item:l,cellData:{colName:e,value:e in l?l[e]:""},loading:n,isEditing:RHelper.isEditable(e,b),onCellChange:m})})),o.createElement("td",{style:{textAlign:"center"}},o.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=i({},l);e.handleRowCreate(t),c(t)}},"Create")))};function w(e){var t=e.loading,n=e.form,r=e.searchData,a=u(o.useState(n.name),2),l=a[0],c=a[1],i=u(o.useState(n.country),2),s=i[0],m=i[1],d=function(e){r({name:l,country:s})};return o.createElement("div",{className:"card border-0 bg-transparent"},o.createElement("div",{className:"card-body p-0 pt-1"},o.createElement("div",{className:"form-row"},o.createElement("div",{className:"col-auto"},o.createElement("label",{className:"mr-1",htmlFor:"name"},"Name:"),o.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"name",id:"name",value:l,onChange:function(e){return c(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&d()}})),o.createElement("div",{className:"col-auto"},o.createElement("label",{className:"mr-1",htmlFor:"country"},"Country:"),o.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",name:"country",id:"country",value:s,disabled:t,onChange:function(e){return m(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&d()}})),o.createElement("div",{className:"col-auto"},o.createElement("button",{className:"btn btn-sm btn-info",onClick:d,disabled:t},"Search")))))}function C(e){var t=e.initialFormStates,n=e.settings,r={id:"",name:"",country:"",pri:"",categories:null},a=u(o.useState(!0),2),c=a[0],s=a[1],m=u(o.useState(!0),2),d=m[0],g=m[1],_=u(o.useState(!1),2),y=(_[0],_[1]),C=u(o.useState(r),2),k=C[0],N=C[1],j=u(o.useState({}),2),S=j[0],O=j[1],x=u(o.useState([]),2),A=x[0],D=x[1],P=u(o.useState(""),2),q=P[0],R=P[1],T=u(o.useState(t),2),I=T[0],F=T[1],H=u(o.useState(n),2),U=H[0],M=H[1];o.useEffect((function(){z(i(i({},I),{},{with:"form"}))}),[]),o.useEffect((function(){B()}),[A]),o.useEffect((function(){$("#"+p).floatThead({autoReflow:!0})}),[c]);var B=function(){var e={};A.map((function(t,n){e[t.id]=n})),O(e)},z=function(e){var t={};t=Object.assign({},t,e),App.ajax_post(get_ajax_url("CustomerAction","get_list"),t,(function(e){y(!0),g(!1),e.error||(W(e.data),s(!1))}),(function(e){y(!0)}))},W=function(e){D(l(e.rows)),R(e.sql),v=e.categories,init_tooltip()},Y=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url("CustomerAction","delete"),{id:e.id},(function(t){0==t.error&&D(A.filter((function(t){return t.id!=e.id})))}))}),{title:"Delete entry"})},K=Object.keys(b);return o.createElement(f.Provider,{value:i(i({},U),{},{setStateSettings:M})},o.createElement(o.Fragment,null,o.createElement(w,{form:I,setForm:F,loading:d,setLoading:g,searchData:function(e){g(!0),z(e)}}),o.createElement("h4",null,A?"Results (".concat(A.length," records)"):"No Results",o.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),o.createElement("div",{className:"sql-log-wrap"},o.createElement("pre",null,q)),o.createElement("div",{className:"table-wrap"},o.createElement("table",{id:p,className:"data-table editable border-0",style:{minWidth:500}},o.createElement("thead",null,o.createElement("tr",null,K.map((function(e,t){return o.createElement("th",{key:e},RHelper.getColName(e,b))})),o.createElement("th",null))),o.createElement("tbody",null,o.createElement(E,{key:"new-row",item:k,cols:K,loading:d,handleRowCreate:function(e){g(!0);var t={cust_no:e.cust_no,name:e.name,country:e.country,pri:e.pri};App.ajax_post(get_ajax_url("CustomerAction","save"),{data:t},(function(e){g(!1),e.error||(D([e.data.row].concat(l(A))),N(i({},r)))}),(function(e){g(!1)}))}}),A.map((function(e,t){return o.createElement(h,{key:e.id,isTotal:!1,item:e,cols:K,rowIndex:S[e.id],loading:d,handleRowDelete:Y})})))))))}var k="undefined"!=typeof RCustomerProps?RCustomerProps:"undefined"!=typeof App?App.get_params(window.location):{};r.render(o.createElement(C,k),document.getElementById("root"))}},n={};function o(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(a.exports,a,a.exports,o),a.loaded=!0,a.exports}o.m=t,e=[],o.O=(t,n,r,a)=>{if(!n){var l=1/0;for(u=0;u<e.length;u++){for(var[n,r,a]=e[u],c=!0,i=0;i<n.length;i++)(!1&a||l>=a)&&Object.keys(o.O).every((e=>o.O[e](n[i])))?n.splice(i--,1):(c=!1,a<l&&(l=a));if(c){e.splice(u--,1);var s=r();void 0!==s&&(t=s)}}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[n,r,a]},o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),o.j=277,(()=>{var e={277:0};o.O.j=t=>0===e[t];var t=(t,n)=>{var r,a,[l,c,i]=n,s=0;if(l.some((t=>0!==e[t]))){for(r in c)o.o(c,r)&&(o.m[r]=c[r]);if(i)var u=i(o)}for(t&&t(n);s<l.length;s++)a=l[s],o.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return o.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),o.nc=void 0;var r=o.O(void 0,[96],(()=>o(3122)));r=o.O(r)})();