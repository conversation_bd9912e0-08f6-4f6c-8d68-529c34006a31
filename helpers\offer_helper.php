<?php

function table_supplier_offers($rows)
{
    $head = '<tr>';
    $head .= "<th class='text-center' width='50'>No</th>";
    $head .= "<th class='text-center' width='100'>Offer ID</th>";
    $head .= "<th>Offer</th>";
    $head .= "<th width='60'>Status</th>";
    $head .= "<th class='text-center'width='100'>Created on</th>";
    $head .= "<th class='text-center' width='100'>Updated on</th>";
    $head .= "</tr>";

    $ind = 1;
    $body = '';
    if (!empty($rows)) {
        foreach ($rows as $row) {
            $body .= '<tr>';
            $body .= "<td class='text-center'>" . $ind++ . "</td>";
            $body .= "<td class='text-center'><span class='badge badge-success'>" . $row['offer_sid'] . "</span></td>";
            $body .= "<td>" . $row['offer'] . "</td>";
            $body .= "<td class='text-center'><div class='fw-50'>" . get_icon($row['status']) . "</div></td>";
            $body .= "<td class='text-center'>" . dt_nice_dmy($row['created_on']) . "</td>";
            $body .= "<td class='text-center'>" . dt_nice_dmy($row['updated_on']) . "</td>";
            $body .= '</tr>';
        }
    }

    $html_tpl =  '
        <table id="offers" class="data-table border-0 w-100">
            <thead>%s</thead>
            <tbody>%s</tbody>    
        </table>';

    return sprintf($html_tpl, $head, $body);
}


function table_offer_comments($rows)
{
    if (empty($rows)) return 'No comments.';
    $head = '<tr>';
    $head .= "<th class='text-center' width='150'>Comment</th>";
    $head .= "<th class='text-center' width='50'>Status</th>";
    $head .= "<th class='text-center' width='100'>Supplier</th>";
    $head .= "<th class='text-center' width='100'>Customer</th>";
    $head .= "<th class='text-center' width='100'>Order</th>";
    $head .= "<th class='text-center' width='60'>Updated by</th>";
    $head .= "<th class='text-center' width='100'>Updated on</th>";
    $head .= "</tr>";

    $ind = 1;
    $body = '';
    if (!empty($rows)) {
        foreach ($rows as $row) {
            $body .= '<tr>';
            $body .= "<td>" . $row['comment'] . "</td>";
            $body .= "<td class='text-center'><span class='badge badge-success'>" . $row['status_name'] . "</span></td>";
            $body .= "<td class='text-center'>" . ($row['supplier_name']) . "</td>";
            $body .= "<td class='text-center'>" . ($row['customer_name']) . "</td>";
            $body .= "<td class='text-center'>" . ($row['customer_order_desc']) . "</td>";
            $body .= "<td class='text-center'>" . ($row['created_by_name']) . "</td>";
            $body .= "<td class='text-center'>" . dt_nice_dmy($row['updated_on']) . "</td>";
            $body .= '</tr>';
        }
    }

    $html_tpl =  '
        <table class=\'data-table border-0 w-100 m-0\'>
            <thead>%s</thead>
            <tbody>%s</tbody>    
        </table>';

    return sprintf($html_tpl, $head, $body);
}