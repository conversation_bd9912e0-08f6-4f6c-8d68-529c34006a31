<?php

/**
 * Class OrderInvoiceHSCodeAction
 */
class OrderInvoiceHSCodeAction extends BaseAction
{
    /**
     * @var OrderInvoiceModel
     */
    private $hs_code_model = null;

    function __construct()
    {
        parent::__construct();

        $this->hs_code_model =& Loader::get_instance()->load_model('OrderInvoiceHSCodeModel');
    }

    public function save()
    {
        $this->post_restrict();

        // Get order_invoice data
        $row = $_POST['data'];

        // pre-process
        trim_space($row);

        $id = $row['id'] ?? null;
        if (!$id) {
            // validation
            if (!($row['order_inv_no']??'')) {
                $this->return(true, "Invalid request. Invoice No is required");
            }
            // create a new entry
            $row['id'] = $this->hs_code_model->gen_pk();
            $id = $success = $this->hs_code_model->insert($row, true);
        } else {
            $x = $this->hs_code_model->get($id);
            if (!$x) {
                $this->return(true, "The invoice's HS Code '" . $id . "' does not exist!");
            }
            $success = $this->hs_code_model->update($row, $id, false);
        }

        if ($_POST['return_invoice'] ?? null) {
            load_model('OrderInvoiceModel');
            $this->data['invoice'] = OrderInvoiceModel::get_instance()->get_row($row['order_inv_no']);
        } else {
            $this->data['row'] = $this->hs_code_model->get_row($id);
        }
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->hs_code_model->get($id, 1);
        if (!empty($row)) {
            $this->hs_code_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $rows = $this->hs_code_model->get_list($_POST);
        $this->data = [
            'rows' => $rows,
            'sql' => $this->db->show_query_history(false)
        ];
        if (isset($_POST['return_html'])) {
            Loader::get_instance()->load_helper('order_invoice_hs_code_helper');
            $this->data['html'] = table_order_invoice_hs_codes($rows);
        }
        $this->return();
    }

    /**
     *
     * Get customers list for auto completion list.
     */
    public function get_ac_full()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';
        $keyword = $_POST['keyword'] ?? '';

        $where .= $this->db->where_like("CONCAT(IFNULL(hs_code, ''), '^', IFNULL(`gw`, ''), '^', IFNULL(nw, ''))", $keyword, true);

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                CONCAT(IFNULL(hs_code, ''), '^', IFNULL(`gw`, ''), '^', IFNULL(nw, '')) as data,
                CONCAT(IFNULL(hs_code, ''), '/', IFNULL(`gw`, ''), '/', IFNULL(nw, '')) as value
            FROM {$this->hs_code_model->get_table()}
            WHERE TRUE $where
            $limit_str
        ";
        $this->logger->debug($sql);
        $this->data = $this->db->query_select($sql);
        $this->return();
    }

    /**
     *
     * Get customers list for auto completion list.
     */
    public function get_ac_description()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';
        $keyword = $_POST['keyword'] ?? '';

        $searchField = "description";
        $where .= $this->db->where_like($searchField, $keyword, true);

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                CONCAT(IFNULL(hs_code, ''), '^', IFNULL(`description`, '')) as data,
                IFNULL(description, '') as value
            FROM {$this->hs_code_model->get_table()}
            WHERE TRUE $where
            $limit_str
        ";
        $this->logger->debug($sql);
        $this->data = $this->db->query_select($sql);
        $this->return();
    }
}