(()=>{"use strict";var e,t={2629:(e,t,n)=>{var a=n(6540),r=n(961),l=n(2280),o=n(2827);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function i(e){return function(e){if(Array.isArray(e))return p(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function m(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=c(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=c(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==c(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,l,o,c=[],i=!0,s=!1;try{if(l=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;i=!1}else for(;!(i=(a=l.call(n)).done)&&(c.push(a.value),c.length!==t);i=!0);}catch(e){s=!0,r=e}finally{try{if(!i&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(s)throw r}}return c}}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return p(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var b="supplier",v={org_a:["OrgA","",!0],name:["Supplier","",!0],supp_supplier_id:["WHC_Supplier ID","",!0],action_btn:[""],offers:["Offers"],created_on:["Last Success","dt"],updated_on:["Last Contact","dt"],created_by_disp_name:["Last User"],status:["Status"],top3_comments:["Comment"],top5_no_contacts:["No Contacts"]},g={org_a:40,name:150,supp_supplier_id:50,action_btn:50,offers:300,created_on:70,updated_on:70,created_by_disp_name:40,status:70,top3_comments:200,top5_no_contacts:200},h=["org_a","org_b","name","created_on","updated_on","status","created_by_disp_name"],y=function(e){var t=d(a.useState(!1),2),n=t[0],r=(t[1],d(a.useState(e.item),2)),l=r[0],o=r[1],c=d(a.useState(!1),2),i=c[0],s=(c[1],d(a.useState(!1),2));s[0],s[1];a.useEffect((function(){o(e.item)}),[e.item]),a.useEffect((function(){dispose_tooltip($(".supplier-info")),init_tooltip($(".supplier-info"))}),[l.info_comment]);var f=function(e,t){App.ajax_post_ok(get_ajax_url(),{class:"SupplierAction",action:"save_comment",data:e},(function(e){0==e.error&&(o(e.data.row),t.hide())}),t.$el)},p=function(e,t){App.ajax_post_ok(get_ajax_url(),{class:"OfferAction",action:"save",data:e},(function(e){0==e.error&&t.hide()}),t.$el)},b="";if(null!=l&&null!=l.updated_on){var h=moment(l.updated_on).add(-tzLocalOffset-tzServerOffset);if(h.isValid()){var y=-h.diff(moment(),"days");y>20?b="light-pink":y>10&&(b="light-green")}}var E=function(){setDlgTitle($g_dlg,l.name+"'s Offers"),setDlgBody($g_dlg,no_result),$g_dlg.modal({backdrop:"static",show:!0}),App.ajax_post_ok(get_ajax_url("SupplierAction","view_offers"),{supplier_id:l.id},(function(e){e.error||setDlgBody($g_dlg,e.data.html)}),$g_dlg)},w=function(e){setDlgTitle($g_dlg,l.name+"'s Comments"),setDlgBody($g_dlg,no_result),$g_dlg.modal({backdrop:"static",show:!0}),App.ajax_post_ok(get_ajax_url("SupplierAction","view_comments"),{supplier_id:l.id,type:e||""},(function(e){e.error||setDlgBody($g_dlg,e.data.html)}),$g_dlg)},k=function(e,t){RHelper.getColType(t,v);var n=l[t],a=n;return"updated_on"==t?void 0===e||e||(a=null!=n?CvUtil.dtAgo(n):""):"status"==t?a=n in statuses?statuses[n].name:"":"created_on"==t?a=CvUtil.dtNiceDMY(n):"org_b"==t&&(a=0==n?"":n),a},C=function(e){var t=e.target;!function(e,t){o(u(u({},l),{},m({},e,t)))}(t.name,t.value)},N=function(e){var t=k(i,e),r=RHelper.getColType(e,v),c=RHelper.isEditable(e,v),s=Object.keys(statuses),m="d"==r||"i"==r,d="form-control form-control-sm input-edit fw-80"+(m?" text-right":""),f="",p=e;if(i&&c)f="status"!=e?a.createElement("input",{className:d,type:"text",disabled:n,value:k(i,e,l[e]),onChange:C}):a.createElement("select",{className:d,value:l[e]||"",onChange:C},a.createElement("option",{value:""}),s.map((function(e,t){return a.createElement("option",{key:e,value:e},null!=statuses&&statuses[e+""]||"")})));else switch(f=k(!1,e),e){case"org_a":case"org_b":case"supp_supplier_id":p+=" text-center";break;case"created_on":p+=" text-center fs-z8";break;case"updated_on":f=a.createElement("span",{className:"badge badge-secondary"},f),p+=" text-center";break;case"status":f=a.createElement("span",{className:"badge badge-info"},f),p+=" text-center";break;case"top5_no_contacts":f=a.createElement(a.Fragment,null,t.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-4 pr-0"},CvUtil.dtNiceDMYShort(e.created_on,!0)," (",e.created_by_name,")"),a.createElement("div",{className:"col-8"},e.comment,a.createElement("span",{className:"c-lightgrey ml-2"},e.order_id)))})),a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},a.createElement("i",{className:"oi oi-info m-0",onClick:function(e){w("no_contacts")},title:"Please click to view all 'Nicht erreicht' comments."}))),p+=" position-relative fs-z6";break;case"top3_comments":f=a.createElement(a.Fragment,null,t.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-3 pr-0"},CvUtil.dtNiceDM(e.created_on),"  (",e.created_by_name,")"),a.createElement("div",{className:"col-9"},e.comment,a.createElement("span",{className:"c-lightgrey ml-2"},e.customer_order)))})),a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},a.createElement("i",{className:"oi oi-info m-0"+(t&&t.length>0?" blue-f":""),onClick:function(e){w("contacts")},title:"Please click to view all comments."}))),p+=" position-relative fs-z6";break;case"offers":f=a.createElement(a.Fragment,null,t.map((function(e,t){return a.createElement("div",{key:t,className:"form-row"},a.createElement("div",{className:"col-4 tt"+("1"==e.status?"":" c-lightgrey"),title:e.offer_comments},a.createElement("a",{href:base_url+"/offer.php?offer_sid="+e.offer_sid,target:"_blank"},e.offer_sid)),a.createElement("div",{className:"col-6 tt-ajax"+("1"==e.status?"":" c-lightgrey"),id:e.offer_sid,"data-offer_sid":e.offer_sid},e.offer),a.createElement("div",{className:"col-2 fs-z6 tt-ajax"+("1"==e.status?"":" c-lightgrey"),id:e.offer_sid,"data-offer_sid":e.offer_sid},e.updated_on?"[".concat(CvUtil.dtNiceDM(e.updated_on),"]"):""))})),a.createElement("div",{className:"position-absolute",style:{top:"50%",right:0,marginTop:-7}},a.createElement("i",{className:"oi oi-info m-0",onClick:E,title:"Please click to view all offers"}))),p+=" position-relative fs-z8";break;case"comment":f=a.createElement("span",{style:{cursor:"pointer"},onClick:function(e){return w("")}},f),p+=" ";break;case"created_by_disp_name":p+=" fs-z6 text-center";break;case"name":var b=_.get(l,"info_comment","");f=a.createElement(a.Fragment,null,a.createElement("span",{className:"supplier-info tt",title:l.info_asp||""}," ",f),a.createElement("i",{className:"oi oi-info m-0 float-right"+(b&&b.length>0?" blue-f":""),onClick:function(e){return t='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="8">'.concat(l.info_comment||"",'</textarea>                             \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">ASP</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm info_asp" rows="6">').concat(l.info_asp||"","</textarea>                             \n                    </div>\n                </div>\n        "),void(n=bs4pop.dialog({title:"Update Supplier Info",content:t,backdrop:"static",width:600,onShowEnd:function(){n.$el.find(".comment").focus()},btns:[{label:"Update",className:"btn-info btn-sm",onClick:function(e){return App.ajax_post_ok(get_ajax_url("SupplierAction","update_info"),{supplier_id:l.id,comment:n.$el.find(".comment").val(),asp:n.$el.find(".info_asp").val()},(function(e){0==e.error&&(o(u(u({},l),{},{info_comment:e.data.row.comment,info_asp:e.data.row.asp})),n.hide(),dispose_and_init_tooltip($("#tr"+l.id).find(".tt")))}),n.$el),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]}));var t,n},title:"Please click to update supplier information."})),p+=" "}return a.createElement("div",{className:p+(m?" text-right":""),style:{width:RHelper.getColWidth(e,g)||"auto"}},f)};return a.createElement("tr",{id:"tr"+l.id,className:b},e.cols.map((function(e,t){return"action_btn"!==e?a.createElement("td",{key:e},N(e)):a.createElement("td",{key:t},a.createElement("div",{className:"text-center",style:{width:30}},a.createElement("i",{className:"oi oi-comment-square action tt",title:"Create a comment...",onClick:function(e){return function(e){var t="",n=Object.keys(statuses),a=!1,r=!1,o=!1;n.forEach((function(e){var n=statuses[e],l="Default"==n.sc_default_position?' checked="checked"':"";t+='\n                <div class="form-check d-block">\n                    <label class="form-check-label">\n                        <input type="radio" name="status" id="status'.concat(e,'" value="').concat(e,'" class="form-check-input status" \n                        ').concat(l,"                           \n                        /> ").concat(n.name,"\n                    </label>                    \n                </div>\n            "),"Default"==n.sc_default_position&&"1"==n.sc_customer_order&&(a=!0),"Default"==n.sc_default_position&&"1"==n.sc_contact&&(o=!0),a&&n.sc_customer_order_required&&(r=!0)}));var c='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm '.concat(o?"required":"",'">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Offer</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm offer_id" type="text" />\n                        </div>                                                  \n                    </div>\n                </div>\n                <div class="form-group row">\n                    <label class="col-3 col-form-label-sm required">Status</label>\n                    <div class="col-9">\n                        ').concat(t,'                                                             \n                    </div>\n                </div> \n                <div class="form-group row customer_order-wrap ').concat(a?"":" d-none",'">                \n                    <label class="col-3 col-form-label-sm ').concat(r?" required":"",'">Customer Order</label>\n                    <div class="col-9">\n                        <div class="input-wrap-with-icon">\n                            <input class="form-control form-control-sm customer_order" type="text" />\n                        </div>\n                    </div>\n                </div>\n            '),i=bs4pop.dialog({title:"Create a Comment of '".concat(l.name,"'"),content:c,className2:"modal-dialog-scrollable",backdrop:"static",width:500,onShowEnd:function(){i.$el.find(".comment").focus();var t=i.$el.find(".offer_id");init_autocomplete(t,{class:"OfferAction",action:"get_ac_offers",supplier_id:e},!1,null,(function(){}),(function(e){}));var n=i.$el.find(".customer_order");init_autocomplete(n,{class:"OrderAction",action:"get_ac_orders",supplier_id:e},!1,null,(function(){}),(function(e){})),i.$el.find('input[name="status"]').change((function(e){var t=e.target.value,n=statuses[t]||null;if(null!=n){1==n.sc_customer_order?i.$el.find(".customer_order-wrap").removeClass("d-none"):i.$el.find(".customer_order-wrap").addClass("d-none");var a=i.$el.find("textarea.comment").closest(".row").find("label");1!=n.sc_contact?a.removeClass("required"):a.addClass("required"),1==n.sc_customer_order_required?i.$el.find(".customer_order-wrap label").addClass("required"):i.$el.find(".customer_order-wrap label").removeClass("required")}}))},btns:[{label:"New Offer...",className:"btn-outline-info btn-sm mr-5",onClick:function(e){var t='\n                        <div class="form-group row">                \n                            <label class="col-3 col-form-label-sm required">ID</label>\n                            <div class="col-9">\n                                <input class="form-control form-control-sm offer_sid" name="offer_sid" />                             \n                            </div>\n                        </div>\n                        <div class="form-group row">                \n                            <label class="col-3 col-form-label-sm">Name</label>\n                            <div class="col-9">\n                                <input class="form-control form-control-sm offer" name="offer" />                             \n                            </div>\n                        </div>\n                        <div class="form-group row">                \n                            <label class="col-3 col-form-label-sm">Supplier</label>\n                            <div class="col-9">\n                                <div class="input-wrap-with-icon">\n                                    <input class="form-control form-control-sm supplier_id" type="text" value="'.concat(l.name,'" selected-val="').concat(l.id,'" />\n                                </div>                       \n                            </div>\n                        </div>\n                    '),n=bs4pop.dialog({title:"Create an Offer",content:t,backdrop:"static",width:400,onShowEnd:function(){n.$el.find(".offer_sid").focus();var e=n.$el.find(".supplier_id");init_autocomplete(e,{class:"SupplierAction",action:"get_ac_suppliers",exactName:""},!0,null,(function(){}),(function(e){}))},btns:[{label:"Create",className:"btn-info btn-sm",onClick:function(e){var t={supplier_id:n.$el.find(".supplier_id").attr("selected-val"),offer_sid:n.$el.find(".offer_sid").val(),offer:n.$el.find(".offer").val()};return t.offer_sid.length<1?(App.info("Please fill Offer ID."),!1):(p(t,n),!1)}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]});return!1}},{label:"Create",className:"btn-info btn-sm",onClick:function(t){var n="",a=i.$el.find('input[name="status"]:checked').val(),r=statuses[a]||null;null!=r&&1==r.sc_customer_order&&(n=i.$el.find(".customer_order").attr("selected-val")||"");var l={supplier_id:e,comment:i.$el.find(".comment").val(),status:i.$el.find('input[name="status"]:checked').val(),offer_id:i.$el.find(".offer_id").attr("selected-val"),customer_order:n};return null!=r&&1==r.sc_contact&&l.comment.length<1?(App.info("Please fill comment."),!1):l.status.length<1?(App.info("Please select status."),!1):null!=r&&1==r.sc_customer_order&&1==r.sc_customer_order_required&&n.length<1?(App.info("Please fill the Customer Order."),i.$el.find(".customer_order").focus(),!1):(f(l,i),!1)}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})}(l.id)}})))})),App.has_perm("Supplier Edit")&&a.createElement("td",null,a.createElement("i",{className:"oi oi-pencil oi-edit act-update-supplier tt m-0",title:"Update supplier...",onClick:function(t){return e.onClickCreateSupplier(t,l)}})))},E=function(e){var t=e.loading,n=e.searchData,r=d(a.useState({}),2),o=r[0],c=r[1],i=function(e){var t=e.target,a=t.name,r=t.value;c(u(u({},o),{},m({},a,r))),n(u(u({},o),{},m({},a,r)))},s=Object.keys(v),f=Object.keys(statuses),p="form-control form-control-sm";return a.createElement("tr",{id:"row-search",className:"row-search"+(t?" loading":"")},s.map((function(e,t){return"action_btn"!==e?a.createElement("td",{key:e},("top3_comments"==e||"name"==e||"status"==e)&&a.createElement("div",{style:{width:_.get(g,e,"auto")}},"status"!=e?a.createElement(l.A,{className:p,name:e,value:e in o?o[e]:"",onChange:i}):a.createElement("select",{className:p,name:e,value:e in o?o[e]:"",onChange:i},a.createElement("option",{value:""}),f.map((function(e,t){return a.createElement("option",{key:e,value:e},null!=statuses&&statuses[e+""].name||"")}))))):a.createElement("td",{key:e})})),App.has_perm("Supplier Edit")&&a.createElement("td",null))};function w(e){a.useEffect((function(){}),[e]);var t=e.orgAListAll;return a.createElement("div",{className:"card border-0 bg-transparent form-card"},a.createElement("div",{className:"card-body p-0"},a.createElement("div",{className:"form-row"},a.createElement("div",{className:"col-auto"},a.createElement("label",{className:"mt-1"},"OrgA:"),t.map((function(t,n){return a.createElement("div",{key:"orgAList"+t,className:"form-check d-inline-block ml-3"},a.createElement("input",{type:"checkbox",name:"orgAList[]",id:"orgAList"+t,className:"form-check-input",value:t,readOnly:e.loading,checked:_.indexOf(e.form.orgAList,t)>=0,onChange:function(t){return e.onSearchFormChange("orgAList",t.target.value,t.target.checked)}}),a.createElement("label",{htmlFor:"orgAList"+t,className:"form-check-label"},t||" - "))}))),a.createElement("div",{className:"col-auto ml-3"},a.createElement("label",null,"No contact since"),a.createElement(l.A,{type:"text",className:"form-control form-control-sm text-right fw-50 d-inline-block mx-2",value:_.get(e,"form.no_contact_since",""),onChange:function(t){return e.onSearchFormChange("no_contact_since",t.target.value)}}),a.createElement("label",null,"days")),a.createElement("div",{className:"col-auto ml-3"},a.createElement("label",null,"Contact in last "),a.createElement(l.A,{type:"text",className:"form-control form-control-sm text-right fw-50 d-inline-block mx-2",value:_.get(e,"form.contact_last_hour",""),onChange:function(t){return e.onSearchFormChange("contact_last_hour",t.target.value)}}),a.createElement("label",null,"hours")),a.createElement("div",{className:"col-auto ml-3 mt-1"},a.createElement("div",{className:"form-check d-inline-block"},a.createElement("input",{type:"checkbox",name:"allOldOffers",id:"allOldOffers",className:"form-check-input",value:1,readOnly:e.loading,checked:"1"==e.form.allOldOffers,onChange:function(t){return e.onSearchFormChange("allOldOffers",t.target.value,t.target.checked)}}),a.createElement("label",{htmlFor:"allOldOffers",className:"form-check-label"},"All Old Offers"))),a.createElement("div",{className:"col-auto ml-3 mt-1"},a.createElement("div",{className:"form-check d-inline-block"},a.createElement("input",{type:"checkbox",name:"null_supp_supplier_id",id:"null_supp_supplier_id",className:"form-check-input",value:1,readOnly:e.loading,checked:"1"==e.form.null_supp_supplier_id,onChange:function(t){return e.onSearchFormChange("null_supp_supplier_id",t.target.value,t.target.checked)}}),a.createElement("label",{htmlFor:"null_supp_supplier_id",className:"form-check-label"},"No WHC_Supplier ID?"))))))}function k(e){var t=d(a.useState(!0),2),n=t[0],r=t[1],l=d(a.useState(!0),2),c=l[0],s=l[1],f=d(a.useState(!1),2),p=f[0],k=f[1],C=d(a.useState(e.settings.orgAList),2),N=C[0],S=(C[1],d(a.useState(u(u({},e.defaultSearchForm),{},{orgAList:N?[N[0]]:[]})),2)),O=S[0],x=S[1],A=d(a.useState(e.defaultOrder.field),2),j=A[0],D=A[1],P=d(a.useState(e.defaultOrder.dir),2),L=P[0],F=P[1],q=d(a.useState([]),2),H=q[0],T=q[1],R=d(a.useState(""),2),U=R[0],I=R[1],z={page:0,pageCount:0},W=d(a.useState(z),2),B=W[0],M=W[1];a.useEffect((function(){Q(u(u({},O),{},{with:"form"}))}),[]),a.useEffect((function(){p&&(c?wait_icon($("#list-loading")):hide_wait_icon($("#list-loading")))}),[c]),a.useEffect((function(){$("#"+b).floatThead({autoReflow:!0})}),[n]),a.useEffect((function(){p&&K(null)}),[L,j]),a.useEffect((function(){p&&K(null)}),[O]),a.useEffect((function(){p&&K(null)}),[B.page]);var Y=function(e){s(!0),e=Object.assign({},O,e),Q(e)},Q=function(e){var t={pager:B};Object.assign(t,e),t.order_field=j,t.order_dir=L,App.ajax_post(get_ajax_url("SupplierAction","get_list_with_form_data"),t,(function(e){k(!0),s(!1),V(e.data),r(!1)}),(function(e){k(!0)}))},V=function(e){T(e.rows),M(_.get(e,"pager",z)),I(e.sql),init_tooltip()},G=function(e,t){s(!0),App.ajax_post(get_ajax_url(),{class:"Supplier",action:"save",data:e},(function(n){if(s(!1),!n.error){if(""==e.id){var a=[n.data.row].concat(i(H));T(a)}else T(H.map((function(t){return t.id===e.id?Object.assign({},t,n.data.row):t})));t.hide()}}),(function(e){s(!1)}))},J=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"SupplierAction",action:"delete",id:e.id},(function(t){0==t.error&&T(H.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete a Supplier"})},K=function(e){var t={};$(".row-search input, .row-search select").each((function(e,n){t[n.name]=n.value})),Y(t)},X=function(e,t){var n=_.isUndefined(t);n&&(t={});var a='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Supplier</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm" name="name" type="text" value="'.concat(t.name||"",'" />                                     \n                    </div>\n                </div> \n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">OrgA</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm fw-30" name="org_a" type="text" maxlength="1" value="').concat(t.org_a||"",'" />                                     \n                    </div>\n                </div> \n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">OrgB</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm text-right" name="org_b" type="text"  value="').concat(t.org_b||"",'" />                                     \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">WHC_Supplier ID</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm text-right" name="supp_supplier_id" type="text"  value="').concat(t.supp_supplier_id||"",'" />                                     \n                    </div>\n                </div>\n            '),r=bs4pop.dialog({title:n?"Create a Supplier":"Update a Supplier",content:a,backdrop:"static",onShowEnd:function(){r.$el.find("input:first").focus()},btns:[{label:n?"Create":"Update",onClick:function(e){var n=r.$el;return G({id:t.id||"",name:n.find('[name="name"]').val(),org_a:n.find('[name="org_a"]').val(),org_b:n.find('[name="org_b"]').val(),supp_supplier_id:n.find('[name="supp_supplier_id"]').val()},r),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},Z=Object.keys(v);return a.createElement(a.Fragment,null,a.createElement(w,{form:O,setForm:x,loading:c,setLoading:s,searchData:Y,orgAListAll:N,handleCreate:G,onSearchFormChange:function(e,t,n){"orgAList"==e?n&&x(u(u({},O),{},m({},e,[t]))):x(u(u({},O),{},m({},e,"allOldOffers"==e||"null_supp_supplier_id"==e?n?1:0:t)))}}),a.createElement("h4",null,H?"Results (".concat(H.length," records)"):"No Results",a.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),a.createElement("div",{className:"sql-log-wrap"},a.createElement("pre",null,U)),a.createElement("div",{className:"table-wrap position-relative"},a.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},a.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:K,disabled:e.loading},"Refresh"),a.createElement("button",{className:"btn btn-sm btn-success",onClick:X,disabled:e.loading},"Create Supplier"),a.createElement("span",{className:"ml-5"},c?"Loading ...":"")),a.createElement("table",{className:"data-table editable border-0",id:b,style:{minWidth:500}},a.createElement("thead",null,a.createElement("tr",null,Z.map((function(e,t){return"action_btn"!==e?a.createElement("th",{key:e,className:RHelper.isSortable(e,h)?" icon-order-wrap":"",onClick:function(t){return function(e){RHelper.isSortable(e,h)&&(e==j?F((function(e){return"desc"==e?"asc":"desc"})):(D(e),F("asc")))}(e)}},a.createElement("div",{style:{width:_.get(g,e,"auto"),wordWrap:"break-word"}},RHelper.getColName(e,v),RHelper.isSortable(e,h)&&j==e&&a.createElement("a",{className:"icon-order "+L,href:"#"}))):a.createElement("th",{key:e})})),App.has_perm("Supplier Edit")&&a.createElement("th",null))),a.createElement("tbody",null,a.createElement(E,{loading:c,searchData:Y}),null!=H&&H.map((function(e,t){return a.createElement(y,{key:e.id,isTotal:!1,item:e,cols:Z,loading:c,handleRowDelete:J,onClickCreateSupplier:X})})))),a.createElement(o.A,{pager:B,onPageChange:function(e){var t=u(u({},B),{},{page:e.selected});M(t)}})))}var C="undefined"!=typeof SupplierProps?SupplierProps:"undefined"!=typeof App?App.get_params(window.location):{};r.render(a.createElement(k,C),document.getElementById("root"))}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var l=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(l.exports,l,l.exports,a),l.loaded=!0,l.exports}a.m=t,e=[],a.O=(t,n,r,l)=>{if(!n){var o=1/0;for(u=0;u<e.length;u++){for(var[n,r,l]=e[u],c=!0,i=0;i<n.length;i++)(!1&l||o>=l)&&Object.keys(a.O).every((e=>a.O[e](n[i])))?n.splice(i--,1):(c=!1,l<o&&(o=l));if(c){e.splice(u--,1);var s=r();void 0!==s&&(t=s)}}return t}l=l||0;for(var u=e.length;u>0&&e[u-1][2]>l;u--)e[u]=e[u-1];e[u]=[n,r,l]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),a.j=728,(()=>{var e={728:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var r,l,[o,c,i]=n,s=0;if(o.some((t=>0!==e[t]))){for(r in c)a.o(c,r)&&(a.m[r]=c[r]);if(i)var u=i(a)}for(t&&t(n);s<o.length;s++)l=o[s],a.o(e,l)&&e[l]&&e[l][0](),e[l]=0;return a.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),a.nc=void 0;var r=a.O(void 0,[96],(()=>a(2629)));r=a.O(r)})();