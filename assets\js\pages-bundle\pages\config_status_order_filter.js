(()=>{"use strict";var e,t={8134:(e,t,n)=>{var r=n(6540),a=n(961),o=[{accessor:"name",width:130,editable:!0},{accessor:"value",name:"Filter",width:300,editable:!0},{accessor:"order",name:"Order",value_type:"int",width:60,editable:!0,align:"right"}],l=n(9648),i=n(18);n(6151);function c(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return u(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}const s=function(e){var t=e.loading,n=e.form,a=e.searchData,o=c(r.useState(!1),2),l=(o[0],o[1]),i=c(r.useState(_.get(n,"name",null)),2),u=i[0],s=i[1];(0,r.useEffect)((function(){l(!0)}),[]);var f=function(e){a({name:u})};return r.createElement("div",{className:"card border-0 bg-transparent"},r.createElement("div",{className:"card-body p-0 pt-1"},r.createElement("div",{className:"form-row"},r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"name"},"Name:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"name",id:"name",value:u,onChange:function(e){return s(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&f()}})),r.createElement("div",{className:"col-auto"},r.createElement("button",{className:"btn btn-sm btn-info",onClick:f,disabled:t},"Search")))))};function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function m(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||v(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function b(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||v(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e,t){if(e){if("string"==typeof e)return g(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var h=function(e){var t=y(r.useState(e.cellData),2),n=t[0],a=t[1],o=e.isEditing,i=n.colName,c=n.value;r.useEffect((function(){a(e.cellData)}),[e]);var u=function(t){var n=t.target.value;a({colName:i,value:n}),e.onCellChange(i,n)},s=e.colDef,f=function(){l.Pw(s);var e=c;switch(i){case"sc_contact":case"sc_customer_order":case"sc_customer_order_required":case"outside_eu":case"ui_border":e=1==e?"Yes":"No";break;default:e=e||""}return e},m=(l.Pw(s),"form-control form-control-sm"+(l.kf(s)?" text-right":"")+" "+i),d="",b=i,p=l.Yl(s);if(o&&l.Xl(s)){if("value"===i)d=r.createElement("textarea",{className:m+"",type:"text",disabled:e.loading,value:f(),rows:4,onChange:u,onInput:u});else d=r.createElement("input",{className:m+"",type:"text",disabled:e.loading,value:f(),onChange:u,onInput:u})}else d=c||"";return r.createElement("td",null,r.createElement("div",{className:b,style:{width:l.RG(s),textAlign:p}},d))},E=function(e){var t=y(r.useState(!1),2),n=t[0],a=t[1],l=y(r.useState(e.item),2),c=l[0],u=l[1],s=y(r.useState(!1),2),f=s[0],m=s[1];r.useEffect((function(){u(e.item)}),[e.item]);var d=function(e,t){var n=b(b({},c),{},p({},e,t));u(n)},v=e.rowIndex;return r.createElement("tr",{id:"tr-"+c.id},r.createElement("td",null,isNaN(v)?"":v+1),o.map((function(e,t){return r.createElement(h,{key:t,cellData:{colName:e.accessor,value:_.get(c,e.accessor)},colDef:e,isEditing:f,loading:n,isNew:!1,onCellChange:d})})),r.createElement("td",{className:"text-center"},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){if(e.preventDefault(),f){a(!0);var t={id:c.id,type:i.$S.ORD_STATUS_FILTER};o.forEach((function(e){e.accessor in c&&(t[e.accessor]=c[e.accessor])})),App.ajax_post(get_ajax_url("ConfigAction","save"),{isNew:0,data:t},(function(e){m(!f),a(!1),u(e.data)}),(function(e){a(!1)}))}else m(!f)},disabled:n},f?"Save":"Edit"),r.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(c)},disabled:n},"Delete")))},w=function(e){var t=y(r.useState(!1),2),n=t[0],a=t[1],l=y(r.useState(e.item),2),i=l[0],c=l[1];r.useEffect((function(){}),[i]),r.useEffect((function(){c(e.item)}),[e.item]),r.useEffect((function(){a(e.loading)}),[e.loading]);var u=function(e,t){c(b(b({},i),{},p({},e,t)))};return r.createElement("tr",{id:"tr-"+i.id},r.createElement("td",null),o.map((function(e,t){return r.createElement(h,{key:t,loading:n,cellData:{colName:e.accessor,value:_.get(i,e.accessor)},colDef:e,isNew:!0,isEditing:"created_on"!=e.accessor,onCellChange:u})})),r.createElement("td",{className:"text-center"},r.createElement("div",{style:{width:100}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=b({},i);e.handleRowCreate(t),c(t)}},"Create"))))};const S=function(e){var t=e.initialFormStates,n={id:0,code:null,type:i.$S.ORD_STATUS_FILTER,name:"",value:""},a=y(r.useState(!0),2),c=a[0],u=a[1],f=y(r.useState(!0),2),d=f[0],p=f[1],v=y(r.useState(!1),2),g=(v[0],v[1]),h=y(r.useState(n),2),S=h[0],O=h[1],_=y(r.useState({}),2),j=_[0],N=_[1],A=y(r.useState([]),2),C=A[0],D=A[1],x=y(r.useState(""),2),T=x[0],k=x[1],R=y(r.useState(t),2),I=R[0],P=R[1];r.useEffect((function(){L(b(b({},I),{},{with:"form"}))}),[]),r.useEffect((function(){F()}),[C]),r.useEffect((function(){$("#table-config-status").floatThead({autoReflow:!0})}),[c]);var F=function(){var e={};C.map((function(t,n){e[t.id]=n})),N(e)},L=function(e){var t=b(b({},e),{},{type:i.$S.ORD_STATUS_FILTER});App.ajax_post(get_ajax_url("ConfigAction","get_list"),t,(function(e){g(!0),p(!1),U(e.data),u(!1)}),(function(e){g(!0)}))},U=function(e){D(m(e.rows)),k(e.sql),init_tooltip()},q=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.code," / ").concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"ConfigAction",action:"delete",id:e.id},(function(t){0==t.error&&D(C.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete entry"})};return r.createElement(r.Fragment,null,r.createElement(s,{form:I,setForm:P,loading:d,setLoading:p,searchData:function(e){p(!0),L(e)}}),r.createElement("h4",null,C?"Results (".concat(C.length," records)"):"No Results",r.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),r.createElement("div",{className:"sql-log-wrap"},r.createElement("pre",null,T)),r.createElement("div",{className:"table-wrap"},r.createElement("table",{className:"data-table editable border-0",id:"table-config-status",style:{minWidth:500}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"No"),o.map((function(e,t){return r.createElement("th",{key:e.accessor},r.createElement("div",{style:{width:l.RG(e)}},l.mG(e),"order"==e.accessor&&r.createElement("i",{className:"oi oi-info",title:"Display order.","data-placement":"right"}),"value"==e.accessor&&r.createElement("i",{className:"oi oi-info",title:"Define quick filter. <br />e.g. ((s1000 !=1 OR s60=1) OR (c1 = 1)) AND (s500=1)","data-placement":"right"})))})),r.createElement("th",null))),r.createElement("tbody",null,r.createElement(w,{key:"new-row",item:S,loading:d,handleRowCreate:function(e){p(!0),App.ajax_post(get_ajax_url("ConfigAction","save"),{data:b(b({},e),{},{type:i.$S.ORD_STATUS_FILTER})},(function(e){p(!1),e.error||(D([e.data].concat(m(C))),O(b({},n)))}),(function(e){p(!1)}))}}),C.map((function(e,t){return r.createElement(E,{key:e.id,isTotal:!1,item:e,rowIndex:j[e.id],loading:d,handleRowDelete:q})}))))))};var O="undefined"!=typeof ConfigStatusOrderFilterProps?ConfigStatusOrderFilterProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(r.createElement(S,O),document.getElementById("root"))}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var l=1/0;for(s=0;s<e.length;s++){for(var[n,a,o]=e[s],i=!0,c=0;c<n.length;c++)(!1&o||l>=o)&&Object.keys(r.O).every((e=>r.O[e](n[c])))?n.splice(c--,1):(i=!1,o<l&&(l=o));if(i){e.splice(s--,1);var u=a();void 0!==u&&(t=u)}}return t}o=o||0;for(var s=e.length;s>0&&e[s-1][2]>o;s--)e[s]=e[s-1];e[s]=[n,a,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=975,(()=>{var e={975:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[l,i,c]=n,u=0;if(l.some((t=>0!==e[t]))){for(a in i)r.o(i,a)&&(r.m[a]=i[a]);if(c)var s=c(r)}for(t&&t(n);u<l.length;u++)o=l[u],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(s)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r.nc=void 0;var a=r.O(void 0,[96],(()=>r(8134)));a=r.O(a)})();