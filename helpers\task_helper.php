<?php
function table_tasks($rows)
{
    $head = '<div class="form-row header">';
    $head .= "<div class='col-3'>Title</div>";
    $head .= "<div class='col-5'>Comment</div>";
    $head .= "<div class='col-2'>Prio</div>";
    $head .= "<div class='col-2 text-center'>Created on</div>";
    $head .= "</div>";

    $ind = 1;
    $body = '';
    if (!empty($rows)) {
        foreach ($rows as $row) {
            $body .= '<div class="form-row task" id="task_' . $row["id"] . '" data-id="'.$row["id"].'">';
            $body .= "<div class='col-3 task-title'>" . $row['title'] . "</div>";
            $body .= "<div class='col-5 task-comment'>" . $row['comment'] . "</div>";
            $body .= "<div class='col-2 task-prio'>" . ($row['prio']) . "</div>";
            $body .= "<div class='col-2 task-created_on'>" . dt_nice_dmy($row['created_on']) . "</div>";
            $body .= '</div>';
        }
    }

    $html_tpl = '
        <div id="tasks" class="drag-connected">
                %s %s
        </div>';

    return sprintf($html_tpl, $head, $body);
}

function assignment_wrap($rows)
{
    $html = '';
    for ($i = 0; $i < 4; $i++) {
        $html .= '<div class="form-row grid-row">';
        $html .= "<div class='col-3'></div>";
        $html .= "<div class='col-3'></div>";
        $html .= "<div class='col-3'></div>";
        $html .= "<div class='col-3'></div>";
        $html .= "</div>";
    }
    return $html;
}