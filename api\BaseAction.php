<?php

use app\SysMsg\SysMsg;

/**
 * Class BaseAction
 *
 * Define the base action class for ajax requests.
 */
class BaseAction
{
    const MSG_INVALID_REQUEST_AND_RELOAD_PAGE = "Invalid request. Please reload a page and try again.";

    protected $model = null;

    /**
     * @var SimDB|null
     */
    protected $db = null;
    protected $logger = null;

    /**
     * Ajax return variables. Will return [error, msg, data]
     * @var bool
     */
    protected $error = false;
    protected $msg = 'Success';
    protected $data = null;

    private $perm_check_required = TRUE;

    private $_msg_lists = array();
    private $pager = null;
    /**
     * @var Pagination
     */
    protected $pagination = null;

    function __construct()
    {
        global $logger;

        $this->db = &SimDB::get_instance();
        $this->logger = $logger;
        $this->pager = $_REQUEST['pager'] ?? null;
    }

    /**
     * We get start page as non-zero value
     */
    public function get_pager_page()
    {
        $page = 0;
        if ($this->pager) {
            $page = (int)$this->pager['page'] ?? 0;
        }
        $page += 1;
        return $page;
    }

    public function get_pager_limit()
    {
        $default_limit = (is_dev()? 10 : LIMIT_COUNT);
        if ($this->pager) {
            $limit = (int)($this->pager['limit'] ?? $default_limit);
            return max($default_limit, $limit);
        }
        return $default_limit;
    }

    public function get_pager()
    {
        return $this->pager;
    }

    public function get_pagination($rows_count)
    {
        $this->pagination = new Pagination($this->get_pager_page(), $rows_count, $this->get_pager_limit());
        return $this->pagination;
    }

    public function set_perm_check_required($required = TRUE)
    {
        return $this->perm_check_required = $required;
    }

    /**
     * Accept POST request only.
     */
    public function post_restrict()
    {
        if ($this->perm_check_required) {
            if (!Auth::current_user_id()) {
                $this->ajax_return(true, 'Please login to view data.');
            }

            if (!is_post()) {
                $this->error = true;
                $this->msg = "Invalid request. No POST request.";
                $this->ajax_return();
            }
        }
    }

    public function login_restrict()
    {
        if (!Auth::current_user_id()) {
            $this->ajax_return(true, 'Please login to view data.');
        }
    }


    /**
     * Restrict call by permission
     *
     * @param $perm_code
     */
    public function perm_restrict($perm_code)
    {
        $permitted = Auth::has_permission($perm_code);
        if (!$permitted) {
            $this->error = true;
            $this->msg = "No permission!";
            $this->ajax_return();
        }
    }

    /**
     * Update location info
     *
     */
    public function save_location()
    {
        $this->post_restrict();
        if (!isset($_POST['location_id']))
            $this->return(true, 'Invalid request. Location Id is required.');

        $x = BaseModel::get_location(true);
        $location_id = $_POST['location_id'];
        $data = ['location_id' => $location_id];
        if ($x) {
            $success = $this->db->update(BaseModel::TBL_SYS_SYNC_LOCATION, $data);
        } else {
            $success = $this->db->insert(BaseModel::TBL_SYS_SYNC_LOCATION, $data);
        }
        if ($success) {
            $this->msg = 'Updated successfully.';
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    /**
     * Return ajax result as a JSON format.
     */
    public function ajax_return($error = null, $msg = null, $data = null)
    {
        if ($error !== null) {
            $this->error = $error;
        }
        if ($msg !== null) {
            $this->msg = $msg;
        }
        if ($data !== null) {
            $this->data = $data;
        }

        $sys_msg =& SysMsg::get_instance();
        if ($this->msg) {
            $sys_msg->add($this->msg, $this->error ? SysMsg::ERROR : SysMsg::SUCCESS);
            $this->msg = null;
        }
        ajax_return($this->error, $this->msg, $this->data, $sys_msg->get_msg_list());
    }

    public function return($error = null, $msg = null, $data = null)
    {
        $this->ajax_return($error, $msg, $data);
    }
}