<?php

/**
 * Class SupplierModel
 */
class SupplierModel extends BaseModel
{
    const WITH_LATEST_COMMENT = 'latest_comment';
    const WITH_OFFERS = 'offers';
    const WITH_TOP5_NO_CONTACTS = 'top5_no_contacts';
    const WITH_TOP3_COMMENTS = 'top3_comments';

    protected function initialize()
    {
        $this->table = BaseModel::TBL_SUPPLIER;
    }

    public function get_with_latest_comment($id)
    {
        $params = ['id' => $id];
        $with = [
            SupplierModel::WITH_LATEST_COMMENT,
            SupplierModel::WITH_TOP5_NO_CONTACTS,
            SupplierModel::WITH_TOP3_COMMENTS,
            SupplierModel::WITH_OFFERS
        ];
        $rows = $this->get_list($params, $with);
        if ($rows) return current($rows);
        else return null;
    }

    /**
     * Get supplier data with the latest comment.
     *
     * @param $params
     * @param array $with
     * @param null $assoc_field
     * @return array
     */
    public function get_list($params, $with = [SupplierModel::WITH_LATEST_COMMENT], $assoc_field = null)
    {
        $where = $this->_where_list($params, $with);
        $select_from = ' suppliers s';
        $select_with = '';
        if ($with != null) {
            if (in_array(SupplierModel::WITH_LATEST_COMMENT, $with)) {
                $select_from = 'v_suppliers s';
            }
        }

        if ($this->calc_count()) {
            $sql = "
                SELECT COUNT(*)
                FROM $select_from
                  LEFT JOIN supplier_info si ON si.supplier_id=s.id             
                WHERE 1 $where
                ;";
            $this->db->append_query_history($sql);
            return $this->db->query_scalar($sql);
        }

        $order_by = $this->_order_by_list($params, $with);
        $limit_str = $this->limit_by_pager();

        $sql = "
            SELECT 
                s.* 
                $select_with
                , s.created_by_disp_name
                , si.comment AS info_comment
                , si.asp AS info_asp
            FROM $select_from
                LEFT JOIN supplier_info si ON si.supplier_id=s.id                
            WHERE 1 $where
            $order_by
            $limit_str
        ;";
        $this->db->append_query_history($sql);
        $rows = $assoc_field ? $this->db->query_select_manualkey($sql, $assoc_field) : $this->db->query_select($sql);

        if ($rows && $with) {
            // Get supplier keys
            $ids = [];
            foreach ($rows as $ind => $s) {
                $ids[] = $rows[$ind]['id'];
            }

            if (in_array(SupplierModel::WITH_TOP5_NO_CONTACTS, $with)) {
                $where_sub_query = '';
                $where_ids = $this->db->where_in('sc.supplier_id', $ids);
                $where_sub_query .= $where_ids;
                $where_sub_query .= $this->db->where_not_equal('conf.sc_contact', 1);

                $no_contact_since = intval($params['no_contact_since'] ?? 14);
                $where_sub_query .= $this->db->where_opt('sc.created_on >= ', time2db_datetime(time() - $no_contact_since * DAY_SECONDS));

                $sql2 = "
                    SELECT 
                        id,
                        supplier_id,
                        created_on,
                        (SELECT display_name FROM users WHERE id=t.created_by) AS created_by_name,
                        `comment`,
                        `customer_order`
                    FROM (
                        SELECT 
                            sc.id,
                            supplier_id,
                            created_on,
                            created_by,
                            `comment`,
                            `customer_order`,
                            @num := IF(@supplier_id = `supplier_id`, @num + 1,IF(@supplier_id := `supplier_id`, 1, 1)) AS row_number2 
                        FROM
                            supplier_comments sc 
                            INNER JOIN ( SELECT @num := 0, @supplier_id := NULL) c
                            LEFT JOIN sys_config conf ON conf.code=sc.code
                        WHERE 1 $where_sub_query
                        ORDER BY supplier_id, created_on DESC
                        ) AS t 
                    WHERE t.row_number2 <= 5
                ;";

                $this->db->append_query_history($sql2);
                $latest_5_sc = $this->db->query_select_manualkey_deep($sql2, 'supplier_id');
                foreach ($rows as $ind => $row) {
                    $supplier_id = $rows[$ind]['id'];
                    $rows[$ind][SupplierModel::WITH_TOP5_NO_CONTACTS] = $latest_5_sc[$supplier_id] ?? [];
                }
            }

            if (in_array(SupplierModel::WITH_TOP3_COMMENTS, $with)) {
                $where_sub_query = '';
                $where_ids = $this->db->where_in('sc.supplier_id', $ids);
                $where_sub_query .= $where_ids;
                $where_sub_query .= $this->db->where_equal('conf.sc_contact', 1);
                $where_sub_query .= $this->db->where_like('sc.comment', $params['top3_comments'] ?? '', true);

                $sql2 = "
                    SELECT 
                        id,
                        supplier_id,
                        created_on,
                        (SELECT display_name FROM users WHERE id=t.created_by) AS created_by_name,
                        `comment`,
                        `customer_order` 
                    FROM (
                        SELECT 
                            sc.id,
                            supplier_id,
                            created_on,
                            created_by,
                            `comment`,
                            `customer_order`,
                            @num := IF(@supplier_id = `supplier_id`, @num + 1,IF(@supplier_id := `supplier_id`, 1, 1)) AS row_number2 
                        FROM
                            supplier_comments sc 
                            INNER JOIN ( SELECT @num := 0, @supplier_id := NULL) c
                            LEFT JOIN sys_config conf ON conf.code=sc.code
                            WHERE 1 $where_sub_query
                        ORDER BY supplier_id, created_on DESC
                        ) AS t 
                    WHERE t.row_number2 <= 3
                ;";

                $this->db->append_query_history($sql2);
                $arr = $this->db->query_select_manualkey_deep($sql2, 'supplier_id');
                foreach ($rows as $ind => $row) {
                    $supplier_id = $rows[$ind]['id'];
                    $rows[$ind][SupplierModel::WITH_TOP3_COMMENTS] = $arr[$supplier_id] ?? [];
                }
            }

            if (in_array(SupplierModel::WITH_OFFERS, $with)) {

                $where = '';
                $where .= $this->db->where_in('supplier_id', $ids);
                $allOldOffers = $params['allOldOffers'] ?? '';
                if ($allOldOffers != '1') {
                    $sql = "
                        SELECT * FROM (
                            SELECT * FROM (SELECT 
                                offer_sid, 
                                offer, 
                                status, 
                                supplier_id,
                                updated_on,
                                @num := IF(@supplier_id = `supplier_id`, @num + 1,IF(@supplier_id := `supplier_id`, 1, 1)) AS row_number2, 
                                (
                                    SELECT 
                                        GROUP_CONCAT(oc.comment SEPARATOR '<br />')
                                    FROM offer_comments oc, sys_config sc 
                                    WHERE sc.id=oc.sc_id AND offer_id=offers.id AND sc.link_to_supplier=1
                                    GROUP BY offer_id 
                                ) AS offer_comments
                            FROM offers
                                INNER JOIN ( SELECT @num := 0, @supplier_id := NULL) c
                            WHERE 1 $where AND status=0
                            ORDER BY supplier_id, updated_on DESC
                            ) a
                            WHERE row_number2 <= 3
                        UNION ALL 
                            SELECT 
                                offer_sid, 
                                offer, 
                                status, 
                                supplier_id,
                                updated_on,
                                1,
                                (
                                    SELECT 
                                        GROUP_CONCAT(oc.comment SEPARATOR '<br />')
                                    FROM offer_comments oc, sys_config sc 
                                    WHERE sc.id=oc.sc_id AND offer_id=offers.id AND sc.link_to_supplier=1
                                    GROUP BY offer_id 
                                ) AS offer_comments
                            FROM offers
                            WHERE 1 $where AND status=1
                        ) a    
                        ORDER BY updated_on DESC    
                        ;";
                } else {
                    $sql = "
                        SELECT 
                            offer_sid, 
                            offer, 
                            status, 
                            supplier_id,
                            updated_on,
                            (
                                SELECT 
                                    GROUP_CONCAT(oc.comment SEPARATOR '<br />')
                                FROM offer_comments oc, sys_config sc 
                                WHERE sc.id=oc.sc_id AND offer_id=offers.id AND sc.link_to_supplier=1
                                GROUP BY offer_id 
                            ) AS offer_comments
                        FROM offers
                        WHERE 1 $where
                        ORDER BY updated_on DESC
                        ;";
                }
                $this->db->append_query_history($sql);
                $arr = $this->db->query_select_manualkey_deep($sql, 'supplier_id');

                foreach ($rows as $ind => $row) {
                    $supplier_id = $rows[$ind]['id'];
                    $rows[$ind][SupplierModel::WITH_OFFERS] = $arr[$supplier_id] ?? [];
                }
            }
        }

        return $rows;
    }

    private function _where_list(&$params, &$with)
    {
        $where = '';
        $id = $params['id'] ?? '';
        if ($id) {
            $where .= ' AND s.id = ' . $this->safe_value($id);
        } else {
            $where .= $this->db->where_like('s.name', $params['name'] ?? '');
            $where .= $this->db->where_like('s.org_a', $params['org_a'] ?? '');

            if ($params['orgAList'] ?? null){
                $params['orgAList'] = array_filter($params['orgAList'], function ($a){
                    return $a != 'All';
                });
            }
            $where .= $this->db->where_in('s.org_a', $params['orgAList'] ?? [], 'AND', false);

            $contact_last_hour = intval($params['contact_last_hour'] ?? '');
            if ($contact_last_hour) {
                $sub_where = '';
                $sub_where .= $this->db->where_opt('created_on >= ', date(DATE_FORMAT_YMD_HIS, time() - $contact_last_hour * 3600));
                $where .= " AND EXISTS (SELECT 1 FROM supplier_comments WHERE supplier_id = s.id $sub_where LIMIT 1)";
            }

            if ($with != null) {
                if (in_array('latest_comment', $with)) {
                    $where .= $this->db->where_like('s.created_by_name', $params['created_by_name'] ?? '', true);
                    $where .= $this->db->where_like('s.created_by_disp_name', $params['created_by_disp_name'] ?? '', true);
                    $where .= $this->db->where_like('s.updated_on', $params['updated_on'] ?? '', true);
                    $where .= $this->db->where_like('s.comment', $params['comment'] ?? '', true);
                    $where .= $this->db->where_equal('s.status', $params['status'] ?? '');
                }
            }

            if ($params['null_supp_supplier_id'] ?? '') {
                $where .= $this->db->where_equal('s.supp_supplier_id', NULL);
            }
        }
        return $where;
    }

    private function _order_by_list(&$params, &$with)
    {
        $order_field = $params['order_field'] ?? 'id';
        $order_dir = $params['order_dir'] ?? 'desc';

        switch ($order_field) {
            case 'name':
            case 'created_on':
            case 'updated_on':
            case 'created_by_disp_name':
            case 'status':
            case 'org_a':
                $order_by_str = 's.' . $order_field;
                break;
            default:
                $order_by_str = '';
                break;
        }
        if ($order_by_str && $order_dir) $order_by_str .= ' ' . $order_dir;
        else $order_by_str = 's.org_a, s.name';
        return $order_by_str ? ' ORDER BY ' . $order_by_str : '';
    }

    public function get_org_a_list($includeAll = false)
    {
        $sql = "SELECT DISTINCT org_a FROM suppliers ";
        $this->db->append_query_history($sql);
        $rows = $this->db->query_col($sql);
        $rows = $includeAll? array_merge(["All"], $rows) : $rows;
        return $rows;
    }
}