<?php
require_once '_admin_includes.php';

$uid = $_GET['id'] ?? 0;

// -------------------------------------------------------------------- //
// Permission Checking
// -------------------------------------------------------------------- //

if (!$uid) {
    $msg->error("Invalid request.", "$ADMIN_URL/users.php");
}

$u = $db->query_row("SELECT * FROM users where id='$uid'");
if (!$u) {
    $msg->error("Invalid request. User doesn't exist.", "$ADMIN_URL/users.php");
}

// -------------------------------------------------------------------- //
// POST request handling
// -------------------------------------------------------------------- //

if (isset($_POST['btn_submit'])) {
    $u_perms = $_POST['u_perms'] ?? [];
    $db->query("DELETE FROM sys_user_permissions WHERE user_id='$uid'");
    if (!empty($u_perms)) {
        $rows = [];
        foreach ($u_perms as $pid) {
            $rows[] = array(
                'user_id' => $uid,
                'permission_id' => $pid
            );
        }
        $db->insert_batch('sys_user_permissions', $rows);
    }

    $msg->success("Updated '{$u['username']}' permissions successfully.");
}

// -------------------------------------------------------------------- //
// Start to render the page
// -------------------------------------------------------------------- //

$code_perms_map = Auth::get_permissions();

$perms = $db->query_select_manualkey("SELECT * FROM sys_permissions ORDER BY `type`, `order` ", 'id');
$perms_by_type = [];
foreach ($perms as $pid => $perm) {
    $perm_type = $perm['type'];
    $perms_by_type[$perm_type][$pid] = $perm;
}
$u_perms = $db->query_col("SELECT permission_id FROM sys_user_permissions WHERE user_id='{$u['id']}'");


$title = "Update permissions on '{$u['username']}'";
require_once APP_PATH . 'layout/header.php';

?>
    <form id="admin-form" class="mt-4" action="<?php echo $_SERVER['PHP_SELF'] . "?id={$u['id']}" ?>" method="post">
        <div class="card">
            <div class="card-header">
                <h4 class="float-left mb-0">Permissions</h4>
                <div class="card-header-pills float-right">
                    <button type="submit" name="btn_submit" class="btn btn-info btn-sm">Submit</button>
                    <a href="<?php echo $ADMIN_URL . "/users.php" ?>" class="btn btn-outline-secondary btn-sm">Cancel</a>
                </div>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <?php
                    if (Auth::is_admin($u)) {
                        $msg->info('User "' . $u['username'] . '" have full permission regardless settings below!');
                        echo '<div class="w-100">';
                        $msg->display();
                        echo '</div>';
                    }
                    ?>
                    <?php foreach ($perms_by_type as $perm_type => $perms_tmp) {
                        //if ($perm_type != 'menu') continue;

                        // sorting by `parent` db column.
                        $perms = [];
                        $parents = [];
                        foreach ($perms_tmp as $p_id => $p) {
                            $parent = $p['parent'];
                            $perms[$parent][$p_id] = $p;
                            $parents[$p['parent']] = $p['order'];
                        }
                        $parents = array_keys($parents);

                        $count = count($perms_tmp) + count($parents);
                        $row_count_per_col = ceil($count / 4);
                        ?>
                        <div class="col-sm-12 mb-3">
                            <fieldset>
                                <legend><?php echo ucfirst($perm_type) ?></legend>
                                <div class="form-row">
                                    <?php
                                    $ind = 0;
                                    foreach ($parents as $parent) {
                                        $p_ind = 0;
                                        if (($ind % $row_count_per_col) == 0) {
                                            echo '<div class="col-3">';
                                        }
                                        echo "<h6 class='mt-2 ml-1'>$parent &nbsp;</h6>";
                                        if ((($ind + 1) % $row_count_per_col) == 0) {
                                            echo "</div>";
                                        }
                                        $ind++;
                                        foreach ($perms[$parent] as $p_id => $p) {
                                            if (($ind % $row_count_per_col) == 0) {
                                                echo '<div class="col-3">';
                                            }
                                            ?>
                                            <div class="form-check ml-3 mt-1">
                                                <input type="checkbox" name="u_perms[]" id="u_perm_<?php echo $p_id ?>"
                                                       value="<?php echo $p_id ?>"
                                                       class="form-check-input perm-<?php echo $perm_type ?>"
                                                    <?php echo in_array($p_id, $u_perms) ? 'checked' : "" ?>
                                                />
                                                <label data-toggle="tooltip" for="u_perm_<?php echo $p_id ?>" class="form-check-label" title="<?php echo $p['description'] ?>"><?php echo $p['name'] ?></label>
                                            </div>
                                            <?php
                                            if ((($ind + 1) % $row_count_per_col) == 0) {
                                                echo "</div>";
                                            }
                                            $ind++;
                                        }
                                    }
                                    ?>
                                </div>
                            </fieldset>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </form>
<?php

require_once APP_PATH . 'layout/footer.php';
