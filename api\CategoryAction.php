<?php

use app\SysMsg\SysMsg;

/**
 * Class CategoryAction
 */
class CategoryAction extends BaseAction
{
    /**
     * @var CategoryModel
     */
    private $category_model = null;

    function __construct()
    {
        parent::__construct();

        $this->category_model =& Loader::get_instance()->load_model('CategoryModel');
    }

    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $category = $row['category'] ?? '';
        if (empty($category)) {
            $this->return(true, "Please fill category name.");
        }

        $id = $row['id'] ?? null;
        if (empty($id)) {
            // validation
            $x = $this->category_model->get_by_field('category', $category, 1);
            if ($x) {
                $this->return(true, "The category '" . $category . "' already exists!");
            }
            $success = false;
            unset($row['id']);
            $id = $this->category_model->insert($row, false);
            if ($id) {
                $success = true;
            }
        } else {
            // validation
            $where = sprintf(" id != %s AND category = %s", $this->db->safe_value($id), $this->db->safe_value($category));
            $x = $this->category_model->get_by_where($where, 1);
            if ($x) {
                $this->return(true, "The category '" . $category . "' already exists!");
            }
            $success = $this->category_model->update($row, $id, false);
        }
        $this->data['row'] = $this->category_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->category_model->get($id, 1);
        if (!empty($row)) {
            $success = $this->category_model->delete($id);
            if ($success) {
                $success = $this->category_model->delete_by_where('1' . $this->db->where_equal('category_id', $id), BaseModel::TBL_CUSTOMER_CATEGORIES);
            }
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();

        $this->msg = '';
        $rows = $this->category_model->get_list($_POST);
        $this->data = [
            'rows' => $rows,
            'sql' => $this->db->show_query_history(false)
        ];
        $this->data['sql'] = $this->db->show_query_history(false);

        $this->return();
    }


    /**
     *
     * Get categories list for auto completion list.
     */
    public function get_ac_categories()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';

        $where .= $this->db->where_like('name', $_POST['name'] ?? '', true);
        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                id as data,
                category as value
            FROM categories
            WHERE TRUE $where
            ORDER BY category
            $limit_str
        ";
        $this->data = $this->db->query_select($sql);
        $this->return();
    }
}