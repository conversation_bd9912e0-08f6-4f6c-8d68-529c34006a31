<?php
/**
 * Define $FILTER_DR_LIST and $filter_dr_map
 */

// Date Range definition
define('DR_TODAY', 'DR_T');
define('DR_YESTERDAY', 'DR_YESTERDAY');
define('DR_THIS_WEEK', 'DR_TW');
define('DR_LAST_WEEK', 'DR_LW');
define('DR_THIS_MONTH', 'DR_TM');
define('DR_LAST_MONTH', 'DR_LM');
define('DR_THIS_YEAR', 'DR_TY');
define('DR_LAST_YEAR', 'DR_LY');
define('DR_SINCE_BEGIN', 'DR_SB');
define('DR_CUSTOM', 'DR_CUSTOM');
define('DR_FY_THIS', 'DR_FY_THIS'); // Current fiscal year
define('DR_FY_LAST', 'DR_FY_LAST'); // last fiscal year

// Date range map for dropdown list.
$FILTER_DR_LIST = array(
    DR_TODAY => 'Today',
    DR_YESTERDAY => 'Yesterday',
    DR_THIS_WEEK => 'This Week',
    DR_LAST_WEEK => 'Last Week',
    DR_THIS_MONTH => 'This Month',
    DR_LAST_MONTH => 'Last Month',
    DR_THIS_YEAR => 'This Year',
    DR_LAST_YEAR => 'Last Year',
    DR_FY_THIS => 'This Economical Year',
    DR_FY_LAST => 'Last Economical Year',
    DR_SINCE_BEGIN => 'Since Beginning',
    DR_CUSTOM => 'Custom',
);

$today = time();
$this_year = intval(date('Y', $today));
$this_monday = strtotime('monday this week', $today);
$last_year = $this_year - 1;
$filter_dr_map = array(
    DR_TODAY => [date('Y-m-d', $today), date('Y-m-d', $today)],
    DR_YESTERDAY => [date('Y-m-d', $today - DAY_SECONDS), date('Y-m-d', $today - DAY_SECONDS)],
    DR_THIS_WEEK => [date('Y-m-d', $this_monday), date("Y-m-d", $this_monday + 6 * DAY_SECONDS)],
    DR_LAST_WEEK => [date('Y-m-d', $this_monday - 7 * DAY_SECONDS), date("Y-m-d", $this_monday - 1 * DAY_SECONDS)],

    DR_THIS_MONTH => [date('Y-m-d', strtotime('first day of this month', $today)), date("Y-m-d", strtotime('last day of this month', $today))],
    DR_LAST_MONTH => [date('Y-m-d', strtotime('first day of last month', $today)), date("Y-m-d", strtotime('last day of last month', $today))],

    DR_THIS_YEAR => [$this_year . "-01-01", $this_year . "-12-31"],
    DR_LAST_YEAR => [$last_year . "-01-01", $last_year . "-12-31"],
    DR_SINCE_BEGIN => ['', ''],
);
// fiscal years
$fy_this_year = get_fiscal_year(FY_START_MONTH);
$fy_last_year = $fy_this_year - 1;
$filter_dr_map[DR_FY_THIS] = [
    sprintf('%s-%02d-01', $fy_this_year - 1, FY_START_MONTH),
    date('Y-m-d', strtotime_ymd(sprintf('%s-%02d-01', $fy_this_year, FY_START_MONTH)) - DAY_SECONDS),
];
$filter_dr_map[DR_FY_LAST] = [
    sprintf('%s-%02d-01', $fy_last_year - 1, FY_START_MONTH),
    date('Y-m-d', strtotime_ymd(sprintf('%s-%02d-01', $fy_last_year, FY_START_MONTH)) - DAY_SECONDS),
];

function set_date_range_from_params($param_name, &$params)
{
    global $filter_dr_map;

    $filter_dr = $params[$param_name] ?? DR_TODAY;
    $params['filter_dr'] = $filter_dr;
    switch ($filter_dr) {
        case DR_TODAY:
        case DR_YESTERDAY:
        case DR_THIS_WEEK:
        case DR_LAST_WEEK:
        case DR_THIS_MONTH:
        case DR_LAST_MONTH:
        case DR_THIS_YEAR:
        case DR_LAST_YEAR:
        case DR_FY_THIS:
        case DR_FY_LAST:
            $params['from_date'] = $filter_dr_map[$filter_dr][0];
            $params['to_date'] = $filter_dr_map[$filter_dr][1];
            break;
        case DR_CUSTOM:
            break;
        default:
            $params['from_date'] = '';
            $params['from_to'] = '';
            break;
    }
}

