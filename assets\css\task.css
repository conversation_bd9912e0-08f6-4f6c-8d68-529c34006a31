/** Cards
===================================================================================  */
.panel .card {
    height: 100%;
}

div.card-body {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

div.card .card-header {
    padding: 0.35rem 1rem;
}

.card .card-header .btn {
    font-size: 0.675rem;
}

div.card-header i.oi {
    font-size: 0.675rem;
    margin-top: 5px;
    cursor: pointer;
}

div.card-header i#act-create-task {
    color: #28a745;
    font-weight: bold;
}

div.card-header i#act-create-task:hover {
    color: #1e7e34;
}


/** Icons in Tasks page
===================================================================================  */
.oi.oi-person {
    color: #777;
    margin-right: 10px;
    cursor: pointer;
}

.oi.oi-delete {
    color: #777;
    margin-right: 3px;
    cursor: pointer;
}

.oi.oi-person:hover, .oi.oi-delete:hover {
    color: #333333;
}


/** Tasks panel
===================================================================================  */

#card-tasks {
    height: 68%;
    margin-bottom: 2%;
}

#card-saved-tasks {
    height: 30%;
}

.tasks {
    font-size: 0.675rem;
}

.tasks > div.header {
    font-weight: bold;
    border-bottom: 1px solid #dddddd;
    margin-bottom: 5px;
    padding-bottom: 5px;
}

.tasks > div.task, .dropped-task {
    margin: 5px 0;
    cursor: move;
    border: 0;
    border-radius: 2px;
    box-shadow: 0 1px 2px 0 rgba(9, 30, 66, .25);
    transition: background-color 140ms ease-in-out, border-color 75ms ease-in-out;
}

.tasks > div.task:hover, .tasks > div.task:active,
.dropped-task:hover, .dropped-task:active {
    background: #deebff;
}

.dropped-task {
    padding: 3px 5px;
}

.tasks > div.task:last-child {
    border-bottom: 0;
    margin-bottom: 0;
}

#act-reload-tasks {
    padding-top: 1px;
    font-size: 0.6rem;
}

#saved-tasks div.task {
    position: relative;
}
#saved-tasks div.task .act-remove-saved-task{
    position: absolute;
    top: 3px;
    right: 3px;
    display: none;
}

#saved-tasks div.task:hover .act-remove-saved-task{
    display: block;
}


/** Grid panel --> Assignments Panel
===================================================================================  */
div#assignments-wrap {
}

#assignment-hint {
    position: absolute;
    top: 45%;
    width: 100%;
    text-align: center;
    font-style: italic;
}

.no-grid #act-new-grid-row {
    display: none;
}

.grid-row > div {
    border: 1px solid #dddddd;
    font-size: 0.875rem;
}

div.dropped-task i.oi-delete {
    position: absolute;
    right: 0;
    font-size: 0.675rem;
}

div.dropped-task i.oi-move {
    position: absolute;
    right: 1px;
    top: 15px;
    font-size: 0.675rem;
}

div.dropped-task {
    cursor: move;
}

.grid-row{
    position: relative;
    min-height: 80px;
}
.grid-row i{
    position: absolute;
    left: -10px;
    top: 0px;
}
.grid-row i:last-child{
    margin-top: 15px;
}
.grid-row i{
    font-size: 0.5rem;
    color: #dddddd;
    cursor: pointer;
}
.grid-row i.oi-plus:hover{
    color: green;
}
.grid-row i.oi-minus:hover{
    color: red;
}

/** Draggable panel
===================================================================================  */
.ui-state-highlight {
    background: #fffcba;
    color: #777777;
    border: 0;
}

.ui-state-highlight.hide-content > div {
    opacity: 0;
}

.ui-state-hover {
    border: 0;
    background: #adf8c0 !important;
}

div.task.ui-draggable-dragging {
    opacity: 0.9;
    min-width: 250px;
}

div.saved-task.ui-draggable-dragging {
    opacity: 0.9;
    min-width: 250px;
}

/** Other
===================================================================================  */

.modal-body .form-group {
    margin-bottom: 0.3rem;
}

td div.due_date, td div.created_on {
    text-align: center;
}

td div.prio {
    text-align: center !important;
}

div.task-title {
    cursor: pointer;
    color: #007bff;
    font-size: 0.8rem;
}

