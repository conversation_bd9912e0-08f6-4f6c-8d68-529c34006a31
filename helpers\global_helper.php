<?php

use app\SysMsg\SysMsg;

require_once APP_PATH . DS . 'helpers' . DS . 'common' . DS . 'date_helper.php';
require_once APP_PATH . DS . 'helpers' . DS . 'common' . DS . 'form_helper.php';

/**
 * Include the JS file.
 *
 * @param $js_file
 * @param string $default_path
 * @return string
 */
if (!function_exists('include_js')) {
    function include_js($js_file, $default_path = '/assets/js', $type = null)
    {
        global $BASE_URL;
        if (substr($js_file, 0, 1) == '/') $js_file = trim($js_file, '/');

        $path = $default_path ? "$default_path/$js_file" : $js_file;
        $path = add_cache_suffix($path);
        return PHP_EOL . "\t" . "<script src='$BASE_URL$path'" . ($type ? ' type="' . $type . '"' : "") . "></script>";
    }
}

function is_dev()
{
    return 'development' == getenv('REACT_MODE');
}

function add_cache_suffix($path)
{
    if (strpos($path, '/assets/plugins/') === false/* && strpos($path, '/pages-bundle/vendors.js') === false */) {
        $react_mode = getenv('REACT_MODE');
        if ($react_mode == 'development') {
            $path .= '?time=' . time();
        } else {
            $path .= '?time=' . APP_VERSION;
        }
    }
    return $path;
}

if (!function_exists('include_css')) {
    /**
     * @param $css_file
     * @param string $default_path
     * @param string $media
     * @return string
     */
    function include_css($css_file, $default_path = '/assets/css', $media = 'screen')
    {
        global $BASE_URL;
        if (substr($css_file, 0, 1) == '/') $css_file = trim($css_file, '/');
        $path = $default_path ? "$default_path/$css_file" : $css_file;
        $path = add_cache_suffix($path);
        return PHP_EOL . "\t" . '<link rel="stylesheet" type="text/css" href="' . "$BASE_URL$path" . '" media="' . $media . '">';
    }
}


if (!function_exists('nf')) {
    function nf($number, $is_decimal = true, $decimal = 2, $empty_for_zero = false)
    {
        if ($empty_for_zero && !$number) {
            return '';
        }

        if ($is_decimal) {
            return number_format($number, $decimal, ',', '.');
        } else {
            return number_format($number, 0, ',', '.');
        }
    }
}


if (!function_exists('nf2')) {
    function nf2($number, $is_decimal = true, $decimal = 2)
    {
        if ($number === null) {
            return '';
        }

        if ($is_decimal) {
            return number_format($number, $decimal, ',', '.');
        } else {
            return number_format($number, 0, ',', '.');
        }
    }
}

if (!function_exists('nf_ze')) {
    /**
     * Return formatted number.
     * Zero value -> Empty string.
     *
     * @param $number
     * @param bool $is_decimal
     * @param int $decimal
     * @return string
     */
    function nf_ze($number, $is_decimal = true, $decimal = 2)
    {
        if ($number === null || !$number) {
            return '';
        }

        if ($is_decimal) {
            return number_format($number, $decimal, ',', '.');
        } else {
            return number_format($number, 0, ',', '.');
        }
    }
}


if (!function_exists('nf_i')) {
    function nf_i($number, $empty_for_zero = false)
    {
        return nf($number, false, 0, $empty_for_zero);
    }
}
if (!function_exists('nf_i2')) {
    function nf_i2($number, $empty_for_zero = false)
    {
        return nf2($number, false, 0, $empty_for_zero);
    }
}

if (!function_exists('nf_i_ze')) {
    function nf_i_ze($number)
    {
        return nf_ze($number, false, 0);
    }
}

if (!function_exists('character_limiter')) {
    /**
     * Character Limiter
     *
     * Limits the string based on the character count.  Preserves complete words
     * so the character count may not be exactly as specified.
     *
     * @param string
     * @param int
     * @param string    the end character. Usually an ellipsis
     * @return    string
     */
    function character_limiter($str, $n = 20, $end_char = '...')
    {
        if (mb_strlen($str) < $n) {
            return $str;
        }

        // a bit complicated, but faster than preg_replace with \s+
        $str = preg_replace('/ {2,}/', ' ', str_replace(array("\r", "\n", "\t", "\v", "\f"), ' ', $str));

        if (mb_strlen($str) <= $n) {
            return $str;
        }

        $out = '';
        foreach (explode(' ', trim($str)) as $val) {
            $out .= $val . ' ';

            if (mb_strlen($out) >= $n) {
                $out = trim($out);
                return (mb_strlen($out) === mb_strlen($str)) ? $out : $out . $end_char;
            }
        }
    }
}

/**
 * Parsing the excel cell value.
 * Used on Excel and CSV files(AMZ, eBay, eBay-join)
 *
 * NOTE: col_type should be matched with the definition of table field.
 *
 */
if (!function_exists('parse_excel_cell_value')) {
    function parse_excel_cell_value(&$db_row, &$cell_def, &$cell_val)
    {
        $col_name = '';     // db column name
        $col_type = '';     // excel data format.
        if (is_array($cell_def)) {
            $col_name = $cell_def[0];
            $col_type = $cell_def[1] ?? '';
        } else {
            $col_name = $cell_def;
        }

        $val = $cell_val;
        switch ($col_type) {
            case 'bi':  // big integer
                if (is_string($val)) {
                    $val = doubleval($val);
                }
                break;
            case 'dt':
                $val = time2db_date(PhpOffice\PhpSpreadsheet\Shared\Date::excelToTimestamp($cell_val));
                break;
            case 'dt-ebay':
                $val = ebay_dt_str2db_datetime($val);
                break;
            case 'dt-ebayjoin':
                $val = ebay_dt_ebayjoin2db_datetime($val);
                break;
            case 'd':   // Double value
                if ($val === '') {
                    $val = NULL;
                } else {
                    $precision = $cell_def[2] ?? null;
                    if ($precision) {
                        $val = round($cell_val, $precision);
                    }
                }
                break;
            case 'ds':
                $val = preg_replace("/[^0-9,]/", "", $val);
                $val = str_replace(",", ".", $val);
                if (is_string($val)) {
                    $val = doubleval($val);
                }
                $precision = $cell_def[2] ?? null;
                if ($precision) {
                    $val = round($val, $precision);
                }
                break;
        }
        $db_row[$col_name] = $val;
    }
}

if (!function_exists('redirect')) {
    /**
     * Header Redirect
     *
     * Header redirect in two flavors
     * For very fine grained control over headers, you could use the Output
     * Library's set_header() function.
     *
     * @param string $uri URL
     * @param string $method Redirect method
     *            'auto', 'location' or 'refresh'
     * @param int $code HTTP Response status code
     * @return    void
     */
    function redirect($uri = '', $method = 'auto', $code = NULL)
    {
        // IIS environment likely? Use 'refresh' for better compatibility
        if ($method === 'auto' && isset($_SERVER['SERVER_SOFTWARE']) && strpos($_SERVER['SERVER_SOFTWARE'], 'Microsoft-IIS') !== FALSE) {
            $method = 'refresh';
        } elseif ($method !== 'refresh' && (empty($code) OR !is_numeric($code))) {
            if (isset($_SERVER['SERVER_PROTOCOL'], $_SERVER['REQUEST_METHOD']) && $_SERVER['SERVER_PROTOCOL'] === 'HTTP/1.1') {
                $code = ($_SERVER['REQUEST_METHOD'] !== 'GET')
                    ? 303    // reference: http://en.wikipedia.org/wiki/Post/Redirect/Get
                    : 307;
            } else {
                $code = 302;
            }
        }

        switch ($method) {
            case 'refresh':
                header('Refresh:0;url=' . $uri);
                break;
            default:
                header('Location: ' . $uri, TRUE, $code);
                break;
        }
        exit;
    }
}

if (!function_exists('ajax_return')) {
    /**
     * Return the ajax result as a json format.
     *
     * @param $error
     * @param $msg
     * @param null $data
     * @param null $msg_list
     */
    function ajax_return($error, $msg, $data = null, $msg_list = null)
    {
        header('Content-type: application/json');
        echo json_encode(compact('error', 'msg', 'data', 'msg_list'));
        exit;
    }
}

if (!function_exists('force_download')) {
    /**
     * Force Download
     *
     * Generates headers that force a download to happen
     *
     * @param string    filename
     * @param mixed    the data to be downloaded
     * @param bool    whether to try and send the actual file MIME type
     * @return    void
     */
    function force_download($filename = '', $data = '', $set_mime = FALSE)
    {
        if ($filename === '' OR $data === '') {
            return;
        } elseif ($data === NULL) {
            if (!@is_file($filename) OR ($filesize = @filesize($filename)) === FALSE) {
                return;
            }

            $filepath = $filename;
            $filename = explode('/', str_replace(DIRECTORY_SEPARATOR, '/', $filename));
            $filename = end($filename);
        } else {
            $filesize = strlen($data);
        }

        // Set the default MIME type to send
        $mime = 'application/octet-stream';

        $x = explode('.', $filename);
        $extension = end($x);

        if ($extension == 'xlsx') {
            $mime = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        } else if ($extension == 'xlxm') {
            $mime = 'application/vnd.ms-excel.sheet.macroEnabled.12';
        }

        if ($set_mime === TRUE) {
            if (count($x) === 1 OR $extension === '') {
                /* If we're going to detect the MIME type,
                 * we'll need a file extension.
                 */
                return;
            }

            /*// Load the mime types
            $mimes =& get_mimes();

            // Only change the default MIME if we can find one
            if (isset($mimes[$extension])) {
                $mime = is_array($mimes[$extension]) ? $mimes[$extension][0] : $mimes[$extension];
            }*/
        }

        /* It was reported that browsers on Android 2.1 (and possibly older as well)
         * need to have the filename extension upper-cased in order to be able to
         * download it.
         *
         * Reference: http://digiblog.de/2011/04/19/android-and-the-download-file-headers/
         */
        if (count($x) !== 1 && isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Android\s(1|2\.[01])/', $_SERVER['HTTP_USER_AGENT'])) {
            $x[count($x) - 1] = strtoupper($extension);
            $filename = implode('.', $x);
        }

        if ($data === NULL && ($fp = @fopen($filepath, 'rb')) === FALSE) {
            return;
        }

        // Clean output buffer
        if (ob_get_level() !== 0 && @ob_end_clean() === FALSE) {
            @ob_clean();
        }

        // Generate the server headers
        header('Content-Type: ' . $mime);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Expires: 0');
        header('Content-Transfer-Encoding: binary');
        header('Content-Length: ' . $filesize);
        header('Cache-Control: private, no-transform, no-store, must-revalidate');

        // If we have raw data - just dump it
        if ($data !== NULL) {
            exit($data);
        }

        // Flush 1MB chunks of data
        while (!feof($fp) && ($data = fread($fp, 1048576)) !== FALSE) {
            echo $data;
        }

        fclose($fp);

        exit;
    }
}


// ------------------------------------------------------------------------

if (!function_exists('get_icon')) {
    /**
     * Get check mark / uncheck mark icon in circle.
     *
     * @param string
     * @param bool $show_no
     * @return    string
     */
    function get_icon($value, $show_no = true)
    {
        if ($show_no)
            return "<i class='oi " . ($value ? "oi-circle-check" : "oi-circle-x") . "'></i>";
        else
            return $value ? "<i class='oi " . ($value ? "oi-circle-check" : "oi-circle-x") . "'></i>" : "";

    }
}

if (!function_exists('get_icon_2')) {
    /**
     * Get check mark / uncheck mark icon.
     *
     * @param string
     * @param bool $show_no
     * @return    string
     */
    function get_icon_2($value, $show_no = true)
    {
        if ($show_no)
            return "<i class='oi " . ($value ? "oi-check" : "oi-x") . "'></i>";
        else
            return $value ? "<i class='oi " . ($value ? "oi-check" : "oi-x") . "'></i>" : "";

    }
}


if (!function_exists('get_icon_info')) {
    function get_icon_info($title = '')
    {
        if (strlen($title) < 1) {
            $title = "Click to see more detail.";
        }
        return "<i class='oi oi-info' title='$title' data-toggle='tooltip' ></i>";
    }
}


if (!function_exists('get_new_file_path')) {
    function get_new_file_path($original_file_name = '', $is_full_path = true)
    {
        $info = pathinfo($original_file_name);
        $dirname = $info['dirname'];
        $filename = $info['filename'];
        $extension = $info['extension'];

        $filename = preg_replace('/[^a-zA-Z0-9_-]+/', '-', $filename);

        $final_file_name = $filename . '.' . $extension;
        $file_counter = 1;
        while (file_exists($dirname . DS . $final_file_name)) {
            $final_file_name = $filename . '_' . $file_counter++ . '.' . $extension;
        }

        return $is_full_path ? $dirname . DS . $final_file_name : $final_file_name;
    }
}

if (!function_exists('get_icon_edit')) {
    function get_icon_edit($title, $attr = '')
    {
        return "<i class='oi oi-pencil oi-edit' data-toggle='tooltip' title='$title' $attr></i>";
    }
}


if (!function_exists('is_excel_file')) {
    function is_excel_file($file_type)
    {
        return in_array($file_type, [XLS_FILE_TYPE, XLSX_FILE_TYPE]);
    }
}

if (!function_exists('base_url')) {
    function base_url($full = false)
    {
        global $BASE_URL;

        if ($full) {
            $actual_link = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
            return $actual_link . $BASE_URL;
        }
        return $BASE_URL;
    }
}


if (!function_exists('include_reactjs')) {
    function include_reactjs()
    {
        $js = '';
        $react_path = '/assets/plugins/reactjs-16';
        $react_mode = getenv('REACT_MODE');
        if ($react_mode != 'development') {
            $js .= include_js('react.production.min.js', $react_path);
            $js .= include_js('react-dom.production.min.js', $react_path);
        } else {
            $js .= include_js('react.development.js', $react_path);
            $js .= include_js('react-dom.development.js', $react_path);
        }
        //$js .= include_js('axios.min.js');
        return $js;
    }
}


if (!function_exists('include_vendor_bundle_js')) {
    function include_vendor_bundle_js()
    {
        $js = '';
        $bundle_path = '/assets/js/pages-bundle';
        $js .= include_js('vendors.js', $bundle_path);

        return $js;
    }
}

/**
 * Include highcharts libraries from CDN.
 */
if (!function_exists('include_high_charts')) {
    function include_high_charts()
    {
        $js = '';
        $js .= '<script src="https://code.highcharts.com/highcharts.js"></script>';
        $js .= '<script src="https://code.highcharts.com/modules/exporting.js"></script>';
        return $js;
    }
}

function include_js_tree()
{
    $js = '';
    $path = '/assets/plugins/jstree-master/dist';
    $js .= include_css('themes/default/style.css', $path);
    $js .= include_js('jstree.min.js', $path);
    return $js;
}


if (!function_exists('de_num_str2num')) {
    /**
     * Get a number from a number string in German
     * e.g. 1.112,95 => 1112.95
     *
     * @param $num_str_in_german
     * @param bool $double
     * @return float|int|mixed
     */
    function de_num_str2num($num_str_in_german, $double = true)
    {
        $num = (str_replace(',', '.', str_replace('.', '', $num_str_in_german)));
        if ($double)
            $num = doubleval($num);
        else
            $num = intval($num);
        return $num;
    }
}

if (!function_exists('get_file_name_with_ts')) {
    /**
     * Get and Set Pets mode.
     *
     * @return string
     */
    function get_file_name_with_ts($file_name)
    {
        $pos = strrpos($file_name, ".");
        if ($pos !== false) {
            $file_name_ts = substr($file_name, 0, $pos) . "_" . time() . substr($file_name, $pos);
            return $file_name_ts;
        }
        return $file_name;
    }
}

if (!function_exists('delete_backup_files')) {
    function delete_backup_files()
    {
        $dir = APP_PATH . 'data' . DS . 'backup' . DS;

        if (is_dir($dir)) {
            if ($dh = opendir($dir)) {
                $now = time();
                while (($file = readdir($dh)) !== false) {
                    $path = $dir . $file;
                    // todo
                    if ((strrpos($file, '.gz') !== false /*|| strpos($file, 'whcorgs_') !== false*/) && is_file($path)) {
                        if ($now - filemtime($path) >= 60 * 5) { // 5 mins
                            unlink($path);
                        }
                    }
                }
                closedir($dh);
            }
        }
    }
}

if (!function_exists('result_title_html')) {
    /**
     * Get a title html at the top of result tables.
     *
     * @param $title
     * @param string $extra_html
     * @return false|string
     */
    function result_title_html($title, $extra_html = '')
    {
        ob_start();
        $db =& SimDB::get_instance();

        echo '<h4>' . $title . '
             <button class="btn-sql-view btn btn-light btn-sm">Show/Hide SQL</button>
             ' . $extra_html . '
             </h4>';
        $db->show_query_history();

        // post message
        $msg =& SysMsg::get_instance();
        if (isset($msg) && $msg->hasMessages()) {
            echo "<div id='sys-msg-wrap'>";
            $msg->display();
            echo "</div>";
        }

        $content = ob_get_contents();
        ob_end_clean();
        return $content;
    }
}

function _get_a_html($url, $title, $show_no_link_text = true)
{
    if ($url && $url !== '#')
        return '<a href="' . $url . '" target="_blank" class="shop-link">' . $title . '</a>';
    else
        return $show_no_link_text ? "<span class='shop-no-link c-lightgrey'>$title</span>" : '';
}

if (!function_exists('pr')) {
    function pr($data)
    {
        echo "<pre>";
        print_r($data);
        echo "</pre>";
    }
}

/**
 * Request related
 */
function is_post()
{
    return ($_SERVER['REQUEST_METHOD'] === 'POST');
}

function get_var($name, $mode = null)
{
    if ($mode === null) $mode = $_POST;
    return $mode[$name] ?? '';
}

function get_log_type($action, $class = null)
{
    $type = 'view';
    if (strpos($action, 'delete') !== false) {
        return 'delete';
    }
    if (strpos($action, 'save') !== false || strpos($action, 'update') !== false || strpos($action, 'create') !== false) {
        return 'update';
    }
    return $type;
}

function &load_model($model_name)
{
    if (class_exists('Loader')) {
        return Loader::get_instance()->load_model($model_name);
    } else {
        SysMsg::get_instance()->error('Can not load a model class : '. $model_name);
    }
}

function str_replace_first($search, $replace, $subject)
{
    $search = '/'.preg_quote($search, '/').'/';
    return preg_replace($search, $replace, $subject, 1);
}


function clean_date_values(&$row, $field_names)
{
    foreach ($field_names as $name)
        if (isset($row[$name]) && (!$row[$name] || $row[$name] == DATE_EMPTY_STR)) {
            $row[$name] = NULL;
        }
}

function trim_space(&$row, $field_names = null)
{
    if (!empty($field_names)) {
        foreach ($field_names as $name)
            if (isset($row[$name])) {
                $row[$name] = trim($row[$name]);
            }
    } else {
        if (!empty($row) && is_array($row)) {
            foreach ($row as $name => $value) {
                $row[$name] = trim($value);
            }
        }
    }
}
