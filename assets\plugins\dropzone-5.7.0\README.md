<img alt="Dropzone.js" src="http://www.dropzonejs.com/images/new-logo.svg" />

Dropzone.js is a light weight JavaScript library that turns an HTML element into a dropzone.
This means that a user can drag and drop a file onto it, and the file gets uploaded to the server via AJAX.

* * *

_If you want support, please use [stackoverflow](http://stackoverflow.com/) with the `dropzone.js` tag and not the
GitHub issues tracker. Only post an issue here if you think you discovered a bug or have a feature request._

* * *

**Please read the [contributing guidelines](CONTRIBUTING.md) before you start working on Dropzone!**

<br>
<div align="center">
  <a href="https://gitlab.com/meno/dropzone/builds/artifacts/master/download?job=release"><strong>&gt;&gt; Download &lt;&lt;</strong></a>
</div>
<br>
<br>

This is no longer the official repository for Dropzone. I have switched to [gitlab.com](https://gitlab.com/meno/dropzone)
as the primary location to continue development.
 
There are multiple reasons why I am switching from GitHub to GitLab, but a few of the reasons are the
issue tracker that GitHub is providing, *drowning* me in issues that I am unable to categorise or prioritize properly,
the lack of proper continuous integration, and build files. I don't want the compiled `.js` files in my repository, and
people regularly commit changes to the compiled files and create pull requests with them.

I will write a blog post soon, that goes into detail about why I am doing the switch.

This repository will still remain, and always host the most up to date versions of dropzone, but only the distribution
files!

MIT License
-----------
