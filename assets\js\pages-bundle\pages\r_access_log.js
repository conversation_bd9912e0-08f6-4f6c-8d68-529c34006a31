(()=>{"use strict";var e,t={4489:(e,t,r)=>{var n=r(6540),a=r(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||d(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,l,i=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||d(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){if(e){if("string"==typeof e)return f(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var m="table-access-log",p={site:["Title",""],type:["Type","i"],url:["URL",""],request_method:["Method",""],ip:["IP",""],created_at:["Created On","dt"],user_disp_name:["User"],action_btn:[""]},b={site:200,type:70,url:200,request_method:70,ip:100,created_at:90,action_btn:50,user_disp_name:70},y={type:"text-center",request_method:"text-center",ip:"text-center",created_at:"text-center",action_btn:"text-center",user_disp_name:"text-center"},v=["site","type","url","request_method","ip","created_at","user_disp_name"],g=function(e){var t=s(n.useState(e.item),2),r=t[0],a=t[1],o=s(n.useState(!1),2),l=(o[0],o[1]);n.useEffect((function(){l(!0)}),[]),n.useEffect((function(){a(e.item)}),[e]);var i=e.colName,c=r[i],u=RHelper.getColType(i,p),d="";return d+=" "+_.get(y,i,""),n.createElement("td",{className:d},n.createElement("div",{style:{width:_.get(b,i,"auto")}},function(){var e=c||"";if(null!=e&&"dt"==u&&(e=CvUtil.dtNiceDMY(e,!0)),"request_method"==i)return n.createElement("span",{className:"badge"+("POST"==c?" badge-success":" badge-secondary")},e);if("type"==i){var t="badge";return"update"==c?t+=" badge-success":"delete"==c&&(t+=" badge-danger"),n.createElement("span",{className:t},e.toUpperCase())}return e}()))},h=function(e){var t=s(n.useState(!1),2),r=t[0],a=(t[1],s(n.useState(e.item),2)),o=a[0],l=a[1];n.useEffect((function(){l(e.item)}),[e.item]);var i=function(e){var t=bs4pop.dialog({id:"dlg-view-details",title:"View Details",content:"",className2:"modal-dialog-scrollable",width:700,onShowEnd:function(e){},btns:[{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]});App.ajax_post_ok(get_ajax_url("AccessLogAction","view_detail"),{id:o.id},(function(e){t.$el.find(".modal-body").html(e.data.html)}),t.$el)};return n.createElement("tr",{id:"tr-"+o.id},e.cols.map((function(e,t){return"action_btn"!=e?n.createElement(g,{isEditing:!1,key:e,loading:r,colName:e,item:o}):n.createElement("td",{key:e,style:{width:_.get(b,"action_btn","auto")},className:"text-center"},n.createElement("i",{className:"oi oi-eye",onClick:i}))})))},E=function(e){var t=e.loading,r=e.form,a=e.setForm,o=function(e){var t=e.target,n=t.name,o=t.value;a(c(c({},r),{},u({},n,o)))},l=Object.keys(p),i=null;return n.createElement("tr",{id:"row-search",className:"row-search"+(t?" loading":"")},l.map((function(e,t){return"action_btn"!==e?n.createElement("td",{key:e}," ",function(e){var t=r[e]||"",a=RHelper.getColType(e,p),l="form-control form-control-sm"+("d"==a||"i"==a?" text-right":"")+" "+e;switch(e){case"request_method":i=n.createElement("select",{className:l,defaultValue:t,name:e,onChange:o},n.createElement("option",null),n.createElement("option",{value:"POST"},"POST"),n.createElement("option",{value:"GET"},"GET"));break;case"type":i=n.createElement("select",{className:l,defaultValue:t,name:e,onChange:o},n.createElement("option",null),n.createElement("option",{value:"api"},"API"),n.createElement("option",{value:"view"},"View"),n.createElement("option",{value:"delete"},"Delete"),n.createElement("option",{value:"update"},"Update"));break;default:i=n.createElement("input",{className:l,type:"text",name:e,disabled:O.loading,value:t,onChange:o,onInput:o})}return n.createElement("div",{className:_.get(b,e,""),style:{width:RHelper.getColWidth(e,b)}},i)}(e)):n.createElement("td",{key:e})})))};function w(e){var t=e.defaultSearchForm,r=e.defaultOrder,a=s(n.useState(!0),2),o=a[0],i=a[1],u=s(n.useState(!0),2),d=u[0],f=u[1],b=s(n.useState(!1),2),y=b[0],g=b[1],w=s(n.useState(r.field),2),_=w[0],S=w[1],j=s(n.useState(r.dir),2),N=j[0],k=j[1],C=s(n.useState([]),2),P=C[0],x=C[1],A=s(n.useState(""),2),T=A[0],R=A[1],H=s(n.useState(t),2),q=H[0],I=H[1];n.useEffect((function(){U(c(c({},q),{},{with:"form"}))}),[]),n.useEffect((function(){$("#"+m).floatThead({autoReflow:!0})}),[o]),n.useEffect((function(){y&&D(null)}),[_,N]),n.useEffect((function(){y&&D(null)}),[q]);var D=function(e){var t=c({},q);L(t)},L=function(e){e=Object.assign({},q,e),U(e)},U=function(e){f(!0);var t={};"order_field"in(t=Object.assign({},t,e))||(t.order_field=_),"order_dir"in t||(t.order_dir=N),App.ajax_post(get_ajax_url("AccessLog","get_list"),t,(function(e){g(!0),f(!1),e.error||F(e.data),i(!1)}),(function(e){g(!0)}))},F=function(e){x(l(e.rows)),R(e.sql),init_tooltip()},M=Object.keys(p);return n.createElement(n.Fragment,null,n.createElement("h4",null,P?"Results (".concat(P.length," records)"):"No Results",n.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),n.createElement("div",{className:"sql-log-wrap"},n.createElement("pre",null,T)),n.createElement("div",{className:"table-wrap position-relative"},n.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},n.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:D,disabled:O.loading},"Refresh")),n.createElement("table",{id:m,className:"data-table editable border-0",style:{minWidth:500}},n.createElement("thead",null,n.createElement("tr",null,M.map((function(e,t){return"action_btn"!==e?n.createElement("th",{key:e,className:RHelper.isSortable(e,v)?" icon-order-wrap":"",onClick:function(t){return function(e){RHelper.isSortable(e,v)&&(e==_?k((function(e){return"desc"==e?"asc":"desc"})):(S(e),k("asc")))}(e)}},RHelper.getColName(e,p),RHelper.isSortable(e,v)&&_==e&&n.createElement("a",{className:"icon-order "+N,href:"#"})):n.createElement("th",{key:e})})))),n.createElement("tbody",null,n.createElement(E,{form:q,setForm:I,loading:d}),P.map((function(e,t){return n.createElement(h,{key:e.id,isTotal:!1,item:e,cols:M,loading:d})}))))))}var O="undefined"!=typeof RAccessLogProps?RAccessLogProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(n.createElement(w,O),document.getElementById("root"))}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.m=t,e=[],n.O=(t,r,a,o)=>{if(!r){var l=1/0;for(s=0;s<e.length;s++){for(var[r,a,o]=e[s],i=!0,c=0;c<r.length;c++)(!1&o||l>=o)&&Object.keys(n.O).every((e=>n.O[e](r[c])))?r.splice(c--,1):(i=!1,o<l&&(l=o));if(i){e.splice(s--,1);var u=a();void 0!==u&&(t=u)}}return t}o=o||0;for(var s=e.length;s>0&&e[s-1][2]>o;s--)e[s]=e[s-1];e[s]=[r,a,o]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.j=632,(()=>{var e={632:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var a,o,[l,i,c]=r,u=0;if(l.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(c)var s=c(n)}for(t&&t(r);u<l.length;u++)o=l[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(s)},r=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0;var a=n.O(void 0,[96],(()=>n(4489)));a=n.O(a)})();