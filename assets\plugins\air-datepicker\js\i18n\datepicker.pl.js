;(function ($) { $.fn.datepicker.language['pl'] = {
    days: ['<PERSON><PERSON><PERSON><PERSON>', 'Po<PERSON>d<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ąte<PERSON>', '<PERSON><PERSON><PERSON>'],
    daysShort: ['<PERSON>e', 'Pon', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>zw', '<PERSON><PERSON>', 'Sob'],
    daysMin: ['Nd', 'Pn', 'Wt', 'Śr', 'Czw', 'Pt', 'So'],
    months: ['<PERSON><PERSON>cz<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>d<PERSON><PERSON>','List<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
    monthsShort: ['Sty', 'Lut', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
    today: 'D<PERSON><PERSON><PERSON>',
    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    dateFormat: 'yyyy-mm-dd',
    timeFormat: 'hh:ii:aa',
    firstDay: 1
};
 })(jQ<PERSON>y);