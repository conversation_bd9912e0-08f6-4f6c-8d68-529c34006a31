<?php

/**
 * Class Supplier model in WHC_Supplier
 */
class SuppSupplierModel extends BaseModelSupp
{
    protected function initialize()
    {
        $this->table = self::TBL_SUPPLIER;
    }

    /**
     * Get offer list.
     *
     * @param $params
     * @param array $with
     * @param null $assoc_field
     * @return array
     */
    public function get_list($params, $with = [], $assoc_field = null)
    {
        $select_from = ' supplier';
        $select_with = $params['select_with'] ?? '';

        $where = $this->_where_list($params, $with);

        if ($this->calc_count()) {
            $sql = "
                SELECT COUNT(*)
                FROM $select_from
                WHERE 1 $where
                ;";
            $this->db->append_query_history($sql);
            return $this->db->query_scalar($sql);
        }

        $order_by = $this->_order_by_list($params, $with);
        $limit_str = $this->limit_by_pager();
        $sql = "
            SELECT 
                supplier.* {$select_with}         
            FROM $select_from
            WHERE 1 $where
            $order_by
            $limit_str
        ";
        $this->db->append_query_history($sql);
        $rows = $assoc_field ? $this->db->query_select_manualkey($sql, $assoc_field) : $this->db->query_select($sql);

        return $rows;
    }



    /**
     * Select a single row by ID.
     *
     * @param $id
     * @return mixed|null
     */
    public function get_row($id)
    {
        $rows = $this->get_list(['id' => $id]);
        if ($rows) {
            return current($rows);
        }
        return null;
    }

    private function _where_list(&$params, &$with)
    {
        $where = '';

        if ($params['ids'] ?? []) {
            $where .=  " AND id IN(" . $this->get_where_in($params['ids']) . ")";
        }

        if ($params['keyword'] ?? '') {
            $where .= " AND (supplier.name LIKE '%" . $this->get_escape_string( $params['keyword']) . "%')";
        }

        return $where;
    }

    private function _order_by_list(&$params, &$with)
    {
        $order_field = $params['order_field'] ?? null;
        $order_dir = $params['order_dir'] ?? 'ASC';

        if ($order_field) {
            switch ($order_field) {
                case 'name':
                    $order_by_str = 'supplier.name';
                    break;
                default:
                    $order_by_str = 'supplier.' . $order_field;
                    break;
            }

            if ($order_by_str && $order_dir)
                $order_by_str .= ' ' . $order_dir;
            return $order_by_str ? ' ORDER BY ' . $order_by_str : '';
        } else {
            return " ORDER BY supplier.org_a, supplier.name";
        }
    }
}