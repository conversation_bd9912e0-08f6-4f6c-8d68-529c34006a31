(()=>{"use strict";var e,t={7577:(e,t,n)=>{var r=n(6540),a=n(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var d=r.createContext(null),p={parent_id:["Parent"],name:["Category"],order:["Display Order","i"]},b=function(e){var t=u(r.useState(e.cellData),2),n=t[0],a=t[1],o=e.isEditing,i=n.colName,l=n.value,c=r.useContext(d),s=c.cat1_list,f=(c.cat2_list,Object.keys(s));r.useEffect((function(){a(e.cellData)}),[e.cellData]);var m=function(t){e.onCellChange(i,t.target.value)},b=function(){RHelper.getColType(i,p);var t=l;switch(i){case"type":t=t in types?types[t]:"";break;case"name":t=o?t:r.createElement("span",{className:1==e.level?"":"pl-4"},t);break;case"parent_id":t=o?t:r.createElement("span",{className:""},t in s?s[t]:"");break;default:t=t||""}return t},y=RHelper.getColType(n.colName,p),g="form-control form-control-sm"+("d"==y||"i"==y?" text-right":"")+" "+i,v="";if(o)if("parent_id"===i)v=r.createElement("select",{defaultValue:l,className:"form-control form-control-sm fw-150",onChange:m},r.createElement("option",{key:""}),f.map((function(e,t){return r.createElement("option",{key:e,value:e},s[e])})));else g+="order"==i?" fw-80":"name"==i?" fw-200":" fw-80",v=r.createElement("input",{className:g,type:"text",disabled:e.loading,value:b(),onChange:m,onInput:m});else{var h=i;"sc_contact"!=i&&"sc_customer_order"!=i||(h+=" fw-80 text-center"),v=r.createElement("div",{className:h+("d"==y||"i"==y?" text-right":"")},b())}return r.createElement("td",null,v)},y=function(e){var t=u(r.useState(!1),2),n=t[0],a=t[1],o=u(r.useState(e.item),2),i=o[0],l=o[1],f=u(r.useState(!1),2),m=f[0],p=f[1];r.useEffect((function(){l(e.item)}),[e.item]);var y=r.useContext(d),g=y.setSettings,v=y.searchData,h=function(e,t){var n=c(c({},i),{},s({},e,t));l(n)};return r.createElement("tr",{id:"tr-"+i.id},e.cols.map((function(e,t){return r.createElement(b,{key:t,cellData:{colName:e,value:e in i?i[e]:""},level:i.level,type:i.type,isEditing:m&&"created_on"!=e,loading:n,onCellChange:h})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){e.preventDefault(),m?(a(!0),App.ajax_post(get_ajax_url("ConfigTaskCategoryAction","save"),{isNew:0,data:{id:i.id,name:i.name,parent_id:i.parent_id,order:i.order}},(function(e){p(!m),a(!1),l(e.data.row),"settings"in e.data&&g(e.data.settings),v({})}),(function(e){a(!1)}))):p(!m)},disabled:n},m?"Save":"Edit"),r.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(i)},disabled:n},"Delete")))},g=function(e){var t=u(r.useState(!1),2),n=t[0],a=t[1],o=u(r.useState(e.item),2),i=o[0],l=o[1];r.useEffect((function(){}),[i]),r.useEffect((function(){a(e.loading)}),[e.loading]),r.useEffect((function(){l(e.item)}),[e.item]);var f=function(e,t){l(c(c({},i),{},s({},e,t)))};return r.createElement("tr",{id:"tr-"+i.id},e.cols.map((function(e,t){return r.createElement(b,{key:t,type:i.type,cellData:{colName:e,value:e in i?i[e]:""},isEditing:"created_on"!=e,loading:n,onCellChange:f})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=c({},i);e.handleCreate(t)}},"Create")))};function v(e){var t={id:"",parent_id:"",name:"",order:""},n=u(r.useState(!0),2),a=n[0],o=n[1],l=u(r.useState(!0),2),s=l[0],f=l[1],m=u(r.useState(!1),2),b=(m[0],m[1]),v=u(r.useState(t),2),h=v[0],E=v[1],w=u(r.useState([]),2),C=w[0],O=w[1],j=u(r.useState(""),2),S=j[0],_=j[1],k=u(r.useState(e.settings),2),N=k[0],x=k[1],A=u(r.useState(e.initialFormStates),2),P=A[0];A[1];r.useEffect((function(){T(c(c({},P),{},{with:"form"}))}),[]),r.useEffect((function(){}),[s]),r.useEffect((function(){x(N)}),[e.settings]),r.useEffect((function(){$("#table-config-status").floatThead({autoReflow:!0})}),[a]);var D=function(e){f(!0),T(e)},T=function(e){var t={class:"ConfigTaskCategoryAction",action:"get_list"};t=Object.assign({},t,e),App.ajax_post(get_ajax_url(),t,(function(e){b(!0),f(!1),I(e.data),o(!1)}),(function(e){b(!0)}))},I=function(e){O(i(e.rows)),_(e.sql),init_tooltip()},R=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.code," / ").concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"ConfigTaskCategoryAction",action:"delete",id:e.id},(function(t){0==t.error&&(O(C.filter((function(t){return t.id!=e.id}))),x(t.data.settings),D({}))}),(function(e){}))}),{title:"Delete entry"})},H=Object.keys(p);return r.createElement(d.Provider,{value:c(c({},N),{},{setSettings:x,searchData:D})},r.createElement(r.Fragment,null,r.createElement("h4",null,C?"Results (".concat(C.length," records)"):"No Results",r.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),r.createElement("div",{className:"sql-log-wrap"},r.createElement("pre",null,S)),r.createElement("div",{className:"table-wrap position-relative"},r.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},r.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:function(e){return D({})},disabled:e.loading},"Refresh")),r.createElement("table",{className:"data-table editable border-0",id:"table-config-status",style:{minWidth:500}},r.createElement("thead",null,r.createElement("tr",null,H.map((function(e,t){return r.createElement("th",{key:e},RHelper.getColName(e,p),"parent_id"==e&&r.createElement("i",{className:"oi oi-info",title:"In case of empty, Category is a parent (Cat1).","data-placement":"right"}),"name"==e&&r.createElement("i",{className:"oi oi-info",title:"If Parent is empty, Category is a parent (Cat1).","data-placement":"right"}))})),r.createElement("th",null))),r.createElement("tbody",null,r.createElement(g,{key:"new-row",item:h,cols:H,loading:s,handleCreate:function(e){f(!0),App.ajax_post(get_ajax_url(),{class:"ConfigTaskCategoryAction",action:"save",data:e},(function(n){if(f(!1),!n.error){var r=c({},t);delete r.parent_id,E(Object.assign({},e,r)),"settings"in n.data&&x(n.data.settings),D({})}}),(function(e){f(!1)}))}}),C.map((function(e,t){return r.createElement(y,{key:e.id,isTotal:!1,item:e,cols:H,loading:s,handleRowDelete:R})})))))))}var h="undefined"!=typeof ConfigTaskCategoryProps?ConfigTaskCategoryProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(r.createElement(v,h),document.getElementById("root"))}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var i=1/0;for(u=0;u<e.length;u++){for(var[n,a,o]=e[u],l=!0,c=0;c<n.length;c++)(!1&o||i>=o)&&Object.keys(r.O).every((e=>r.O[e](n[c])))?n.splice(c--,1):(l=!1,o<i&&(i=o));if(l){e.splice(u--,1);var s=a();void 0!==s&&(t=s)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,a,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=80,(()=>{var e={80:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[i,l,c]=n,s=0;if(i.some((t=>0!==e[t]))){for(a in l)r.o(l,a)&&(r.m[a]=l[a]);if(c)var u=c(r)}for(t&&t(n);s<i.length;s++)o=i[s],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r.nc=void 0;var a=r.O(void 0,[96],(()=>r(7577)));a=r.O(a)})();