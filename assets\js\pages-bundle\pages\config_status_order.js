(()=>{"use strict";var e,t={2672:(e,t,n)=>{var r=n(6540),a=n(961),o=[{accessor:"type",display_type:"select",width:120,editable:!0},{accessor:"name",width:100,editable:!0},{accessor:"value",name:"Default value",width:80,editable:!0},{accessor:"order",name:"Order",value_type:"int",width:60,editable:!0,align:"right"},{accessor:"value_type",name:"Value Type",display_type:"select",width:80,editable:!0},{accessor:"display_type",name:"Display Type",display_type:"select",width:60,editable:!0},{accessor:"option_texts",name:"Option Texts",width:150,editable:!0},{accessor:"option_values",name:"Option Values",width:150,editable:!0},{accessor:"outside_eu",name:"Outside EU",width:60,editable:!0},{accessor:"ui_border",name:"Border?",width:60,editable:!0}],i=n(9648),l=n(18),c=n(6151);function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return u(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}const d=function(e){var t=e.loading,n=e.form,a=e.searchData,o=s(r.useState(!1),2),i=o[0],l=o[1],u=s(r.useState(_.get(n,"type",null)),2),d=u[0],f=u[1],m=s(r.useState(_.get(n,"name",null)),2),p=m[0],y=m[1];(0,r.useEffect)((function(){l(!0)}),[]),(0,r.useEffect)((function(){i&&b(null)}),[d]);var b=function(e){a({type:d,name:p})},v=g();return r.createElement("div",{className:"card border-0 bg-transparent"},r.createElement("div",{className:"card-body p-0 pt-1"},r.createElement("div",{className:"form-row"},r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"code"},"Type: "),c.A.dropdown("type",v,d,(function(e){f(e.target.value)}),{class:"d-inline-block w-auto"})),r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"name"},"Name:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"name",id:"name",value:p,onChange:function(e){return y(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&b(e)}})),r.createElement("div",{className:"col-auto"},r.createElement("button",{className:"btn btn-sm btn-info",onClick:b,disabled:t},"Search")))))};function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function m(e){return function(e){if(Array.isArray(e))return h(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||E(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function b(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=f(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,i,l=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(s)throw a}}return l}}(e,t)||E(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){if(e){if("string"==typeof e)return h(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var g=function(){return[{id:l.$S.ORD_SUPPLIER_STATUS,name:l.Rc[l.$S.ORD_SUPPLIER_STATUS]},{id:l.$S.ORD_CUSTOMER_STATUS,name:l.Rc[l.$S.ORD_CUSTOMER_STATUS]}]},S=function(e){var t=v(r.useState(e.cellData),2),n=t[0],a=t[1],o=e.isEditing,s=n.colName,u=n.value;r.useEffect((function(){a(e.cellData)}),[e]);var d=function(t){var n=t.target.value;a({colName:s,value:n}),e.onCellChange(s,n)},f=e.colDef,m=(i.Pw(f),"form-control form-control-sm"+(i.kf(f)?" text-right":"")+" "+s),p="",y=s,b=i.Yl(f);if(o&&i.Xl(f)){var E=[];switch(s){case"type":E=g(),p=c.A.dropdown("type",E,u||l.$S.ORD_SUPPLIER_STATUS,d);break;case"value_type":E=[{id:l.yE.TEXT,name:l.yE.TEXT.ucfirst()},{id:l.yE.NUMERIC,name:l.yE.NUMERIC.ucfirst()}],p=c.A.dropdown("value_type",E,u||l.yE.TEXT,d);break;case"display_type":E=[{id:"",name:""},{id:l.N9.RADIO,name:l.N9.RADIO.ucfirst()},{id:l.N9.SELECT,name:l.N9.SELECT.ucfirst()},{id:l.N9.CHECKBOX,name:l.N9.CHECKBOX.ucfirst()}],p=c.A.dropdown("display_type",E,u||"",d);break;case"outside_eu":case"ui_border":var h=e.isNew?{isEmpty:!0}:{},S=e.isNew?"":0;p=c.A.dropdown_yes_no(s,u||S,d,h);break;default:p=r.createElement("input",{className:m+"",type:"text",disabled:e.loading,value:function(){i.Pw(f);var e=u;switch(s){case"sc_contact":case"sc_customer_order":case"sc_customer_order_required":case"outside_eu":case"ui_border":e=1==e?"Yes":"No";break;default:e=e||""}return e}(),onChange:d,onInput:d})}}else switch(s){case"type":p=_.get(l.Rc,u,"");break;case"display_type":case"value_type":p=u?u.ucfirst():"";break;case"outside_eu":case"ui_border":y+=" text-center",p=1==u?"Yes":"No";break;default:p=u||""}return r.createElement("td",null,r.createElement("div",{className:y,style:{width:i.RG(f),textAlign:b}},p))},w=function(e){var t=v(r.useState(!1),2),n=t[0],a=t[1],i=v(r.useState(e.item),2),l=i[0],c=i[1],s=v(r.useState(!1),2),u=s[0],d=s[1];r.useEffect((function(){c(e.item)}),[e.item]);var f=function(e,t){var n=y(y({},l),{},b({},e,t));c(n)},m=e.rowIndex;return r.createElement("tr",{id:"tr-"+l.id},r.createElement("td",null,isNaN(m)?"":m+1),o.map((function(e,t){return r.createElement(S,{key:t,cellData:{colName:e.accessor,value:_.get(l,e.accessor)},colDef:e,isEditing:u,loading:n,isNew:!1,onCellChange:f})})),r.createElement("td",{className:"text-center"},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){if(e.preventDefault(),u){a(!0);var t={id:l.id};o.forEach((function(e){e.accessor in l&&(t[e.accessor]=l[e.accessor])})),App.ajax_post(get_ajax_url("ConfigAction","save"),{isNew:0,data:t},(function(e){d(!u),a(!1),c(e.data)}),(function(e){a(!1)}))}else d(!u)},disabled:n},u?"Save":"Edit"),r.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(l)},disabled:n},"Delete")))},O=function(e){var t=v(r.useState(!1),2),n=t[0],a=t[1],i=v(r.useState(e.item),2),l=i[0],c=i[1];r.useEffect((function(){}),[l]),r.useEffect((function(){c(e.item)}),[e.item]),r.useEffect((function(){a(e.loading)}),[e.loading]);var s=function(e,t){c(y(y({},l),{},b({},e,t)))};return r.createElement("tr",{id:"tr-"+l.id},r.createElement("td",null),o.map((function(e,t){return r.createElement(S,{key:t,loading:n,cellData:{colName:e.accessor,value:_.get(l,e.accessor)},colDef:e,isNew:!0,isEditing:"created_on"!=e.accessor,onCellChange:s})})),r.createElement("td",{className:"text-center"},r.createElement("div",{style:{width:100}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=y({},l);e.handleRowCreate(t),c(t)}},"Create"))))};const N=function(e){var t=e.initialFormStates,n={id:0,code:null,type:l.$S.ORD_SUPPLIER_STATUS,name:"",order:"",value:"",value_type:l.yE.TEXT,display_type:""},a=v(r.useState(!0),2),c=a[0],s=a[1],u=v(r.useState(!0),2),f=u[0],p=u[1],b=v(r.useState(!1),2),E=(b[0],b[1]),h=v(r.useState(n),2),g=h[0],S=h[1],N=v(r.useState({}),2),A=N[0],T=N[1],j=v(r.useState([]),2),C=j[0],D=j[1],R=v(r.useState(""),2),x=R[0],P=R[1],k=v(r.useState(t),2),I=k[0],U=k[1];r.useEffect((function(){M(y(y({},I),{},{with:"form"}))}),[]),r.useEffect((function(){L()}),[C]),r.useEffect((function(){$("#table-config-status").floatThead({autoReflow:!0})}),[c]);var L=function(){var e={};C.map((function(t,n){e[t.id]=n})),T(e)},M=function(e){var t=y(y({},e),{},{types:[l.$S.ORD_CUSTOMER_STATUS,l.$S.ORD_SUPPLIER_STATUS]});App.ajax_post(get_ajax_url("ConfigAction","get_list"),t,(function(e){E(!0),p(!1),X(e.data),s(!1)}),(function(e){E(!0)}))},X=function(e){D(m(e.rows)),P(e.sql),init_tooltip()},F=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.code," / ").concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"ConfigAction",action:"delete",id:e.id},(function(t){0==t.error&&D(C.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete entry"})};return r.createElement(r.Fragment,null,r.createElement(d,{form:I,setForm:U,loading:f,setLoading:p,searchData:function(e){p(!0),M(e)}}),r.createElement("h4",null,C?"Results (".concat(C.length," records)"):"No Results",r.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),r.createElement("div",{className:"sql-log-wrap"},r.createElement("pre",null,x)),r.createElement("div",{className:"table-wrap"},r.createElement("table",{className:"data-table editable border-0",id:"table-config-status",style:{minWidth:500}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"No"),o.map((function(e,t){return r.createElement("th",{key:e.accessor},r.createElement("div",{style:{width:i.RG(e)}},i.mG(e),_.findIndex(i.Y$(e),["order"])>=0&&r.createElement("i",{className:"oi oi-info",title:"Display order.","data-placement":"right"}),_.findIndex(i.Y$(e),[])>=0&&r.createElement("i",{className:"oi oi-info",title:"Please set 'Default' if this status is a default status.","data-placement":"right"})))})),r.createElement("th",null))),r.createElement("tbody",null,r.createElement(O,{key:"new-row",item:g,loading:f,handleRowCreate:function(e){p(!0),App.ajax_post(get_ajax_url("ConfigAction","save"),{data:y({},e)},(function(e){p(!1),e.error||(D([e.data].concat(m(C))),S(y({},n)))}),(function(e){p(!1)}))}}),C.map((function(e,t){return r.createElement(w,{key:e.id,isTotal:!1,item:e,rowIndex:A[e.id],loading:f,handleRowDelete:F})}))))))};var A="undefined"!=typeof ConfigStatusOrderProps?ConfigStatusOrderProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(r.createElement(N,A),document.getElementById("root"))}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var i=1/0;for(u=0;u<e.length;u++){for(var[n,a,o]=e[u],l=!0,c=0;c<n.length;c++)(!1&o||i>=o)&&Object.keys(r.O).every((e=>r.O[e](n[c])))?n.splice(c--,1):(l=!1,o<i&&(i=o));if(l){e.splice(u--,1);var s=a();void 0!==s&&(t=s)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,a,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=354,(()=>{var e={354:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[i,l,c]=n,s=0;if(i.some((t=>0!==e[t]))){for(a in l)r.o(l,a)&&(r.m[a]=l[a]);if(c)var u=c(r)}for(t&&t(n);s<i.length;s++)o=i[s],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r.nc=void 0;var a=r.O(void 0,[96],(()=>r(2672)));a=r.O(a)})();