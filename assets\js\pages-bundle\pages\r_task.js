(()=>{"use strict";var e,t={1443:(e,t,n)=>{var a=n(6540),r=n(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||d(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=o(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var a,r,o,l,i=[],c=!0,s=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(a=o.call(n)).done)&&(i.push(a.value),i.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(s)throw r}}return i}}(e,t)||d(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var f=a.createContext(null),b="task",p={title:["Title","",!0],prio:["Prio","i",!0],comment:["Comment","",!0],due_date:["Due Date","dt",!0],assignment:["Assignment",""],created_on:["Created On","dt"],status:["Status",""],action_btn:[""]},v={title:120,prio:70,cat1:100,cat2:100,cat3:100,cat4:100,comment:150,assignment:150,created_on:90,due_date:90,status:40,action_btn:120},g=["title","created_on","prio","cat1","cat2","cat3","cat4","due_date","status"],h=function(e){var t=u(a.useState(e.item),2),n=t[0],r=t[1],o=u(a.useState(!1),2);o[0],o[1];a.useEffect((function(){r(e.item)}),[e.item]);e.isEditing;var l=e.colName,i=e.item[l],c=RHelper.getColType(l,p),s=(RHelper.isEditable(l,p),"d"==c||"i"==c);return function(){var t=i,r="",o=l;switch(o+=s?" text-right":"",l){case"created_on":case"due_date":t=CvUtil.dtNiceDMY(i);break;case"cat1":t=n.cat1_name;break;case"cat2":t=n.cat2_name;break;case"cat3":t=n.cat3_name;break;case"cat4":t=n.cat4_name;break;case"status":o+=" text-center",t=a.createElement("i",{className:"oi"+(1==n.status?" oi-circle-check":"")});break;case"assignment":o+=" fw-200 position-relative fs-z6",t=a.createElement(a.Fragment,null,i.map((function(t,n){return a.createElement("div",{key:n,className:"form-row"},a.createElement("div",{className:"col-4"},t.username),a.createElement("div",{className:"col-3"},CvUtil.dtNiceDMY(t.created_on)),a.createElement("div",{className:"col-4"},t.status_name," "),a.createElement("div",{className:"col-1"},a.createElement("i",{className:"oi oi-delete tt",onClick:function(n){return a=t,void(r=bs4pop.confirm("Are you sure you want to remove the assignment for '".concat(a.username,"'?"),(function(t){return t&&App.ajax_post_ok(get_ajax_url("TaskAction","delete_assignment"),{task_id:a.task_id,user_id:a.user_id},(function(t){r.hide(),0==t.error&&e.updateParentItem(t.data.row)}),r.$el),!1}),{title:"Delete entry"}));var a,r},title:"Remove assignments."})))})))}return r=t,a.createElement("td",null,a.createElement("div",{className:o,style:{width:RHelper.getColWidth(l,v)}},r))}()},y=function(e){var t=u(a.useState(!1),2),n=t[0],r=(t[1],u(a.useState(e.item),2)),o=r[0],l=r[1],i=u(a.useState(!1),2),d=i[0];i[1];a.useEffect((function(){l(e.item)}),[e.item]);var m=function(t){t.preventDefault(),e.onClickCreateTask(t,o)},b=function(e,t){l(c(c({},o),{},s({},e,t)))},p=a.useContext(f),g=p.tu_status_list,y=p.users,k=function(e){var t=Object.keys(y),n=Object.keys(g),a="";n.forEach((function(e){a+='\n                <div class="form-check d-block">\n                    <label class="form-check-label">\n                        <input type="radio" name="status_id" id="status'.concat(e,'" value="').concat(e,'" class="form-check-input status_id" \n                        ').concat('checked="checked"',"                           \n                        /> ").concat(g[e].name,"\n                    </label>                    \n                </div>\n            ")}));var r='\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Task</label>\n                    <div class="col-9">'.concat(o.title,'</div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Comment</label>\n                    <div class="col-9">').concat(o.comment,'</div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm required">Name</label>\n                    <div class="col-9">\n                        <select class="form-control form-control-sm fw-150 user_id">\n                            <option></option>\n                            ').concat(t.map((function(e,t){return'<option value="'.concat(e,'">').concat(y[e],"</option>")})),'\n                        </select>  \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Status</label>\n                    <div class="col-9 pt-2">\n                        ').concat(a,"                                                \n                    </div>\n                </div>\n                \n        "),i=bs4pop.dialog({title:"Assign a User",content:r,className2:"modal-dialog-scrollable",backdrop:"static",width:500,onShowEnd:function(){},btns:[{label:"Assign",className:"btn-info btn-sm",onClick:function(e){return App.ajax_post_ok(get_ajax_url("TaskAction","assign_user"),{task_id:o.id,status_id:i.$el.find('input[name="status_id"]:checked').val(),user_id:i.$el.find(".user_id").val()},(function(e){0==e.error&&(l(e.data.row),i.hide())}),i.$el),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},_="";if(null!=o&&null!=o.updated_on){var w=moment(o.updated_on).add(-tzLocalOffset-tzServerOffset);if(w.isValid()){var E=-w.diff(moment(),"days");E>20?_="light-pink":E>10&&(_="light-green")}}return a.createElement("tr",{id:"tr"+o.id,className:_},e.cols.map((function(t,r){return"action_btn"!==t?a.createElement(h,{key:t,loading:n,isEditing:d,colName:t,item:o,updateParentItem:l,onCellChange:b}):a.createElement("td",{key:r},a.createElement("div",{className:"text-center",style:{width:RHelper.getColWidth("action_btn",v)||80}},a.createElement("i",{className:"oi oi-person action tt",title:"Assign a User to this task...",onClick:k}),a.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:m,disabled:n},d?"Save":"Edit"),a.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(o)},disabled:n},"Del")))})))},k=function(e){var t=e.loading,n=e.searchData,r=u(a.useState({title:""}),2),o=r[0],l=r[1],i=function(e){var r="";switch(e){case"action_btn":case"assignment":r="";break;default:r=RHelper.isEditable(e,p)&&a.createElement("input",{className:"form-control form-control-sm",name:e,readOnly:t,value:e in o?o[e]:"",onChange:function(t){return function(e,t){var a=c(c({},o),{},s({},t,e.target.value));l(a),n(a)}(t,e)}})}return a.createElement("td",{key:e},a.createElement("div",{style:{width:RHelper.getColWidth(e,v)}},r))},d=Object.keys(p);return a.createElement("tr",{id:"row-search",className:"row-search"+(t?" loading":"")},d.map((function(e,t){return i(e)})))};function _(e){var t=e.defaultSearchForm,n=e.defaultOrder,r=(e.settings,u(a.useState(n.field),2)),o=r[0],i=r[1],s=u(a.useState(n.dir),2),d=s[0],m=s[1],v=u(a.useState(!0),2),h=v[0],_=v[1],E=u(a.useState(!0),2),S=E[0],O=E[1],j=u(a.useState(!1),2),C=j[0],N=j[1],x=u(a.useState([]),2),A=x[0],T=x[1],P=u(a.useState(""),2),D=P[0],R=P[1],H=u(a.useState(t),2),I=H[0],U=(H[1],u(a.useState(w.settings),2)),W=U[0],M=U[1];a.useEffect((function(){Y(c(c({},I),{},{with:"form"})),q()}),[]),a.useEffect((function(){C&&V(null)}),[o,d]),a.useEffect((function(){$("#"+b).floatThead({autoReflow:!0})}),[h]);var q=function(){},F=function(e){O(!0),Y(e)},Y=function(e){var t={class:"TaskAction",action:"get_list"};Object.assign(t,e),"order_field"in t||(t.order_field=o),"order_dir"in t||(t.order_dir=d),App.ajax_post(get_ajax_url(),t,(function(e){N(!0),O(!1),e.error||z(e.data),_(!1)}),(function(e){N(!0)}))},z=function(e){T(e.rows),R(e.sql),M(c(c({},W),{},{categories:e.categories})),init_tooltip()},L=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"TaskAction",action:"delete",id:e.id},(function(t){0==t.error&&T(A.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete a Supplier"})},V=function(e){var t={};$(".row-search input, .row-search select").each((function(e,n){t[n.name]=n.value})),F(t)},B=function(e,t){var n=void 0===t;n&&(t={});var a=Q(t),r=bs4pop.dialog({id:"dlg-create-task",title:'<i class="oi oi-task"></i> '+(n?"Create a Task":"Update Task"),content:a,backdrop:"static",className2:"modal-dialog-scrollable",width:500,onShowEnd:function(e){var t=r.$el;t.find("input:first").focus(),init_datepicker(t.find(".d-picker")),init_tooltip(t.find("i.oi-info"))},btns:[{label:n?"Create":"Update",onClick:function(e){var n=r.$el;n.find(".cat3").val(),n.find(".cat4").val();return function(e,t){O(!0),App.ajax_post(get_ajax_url("TaskAction","save"),{data:e},(function(n){if(O(!1),!n.error){if(""==e.id){var a=[n.data.row].concat(l(A));T(a)}else T(A.map((function(t){return t.id===e.id?Object.assign({},t,n.data.row):t})));"categories"in n.data&&M(c(c({},W),{},{categories:n.data.categories})),t.hide()}}),(function(e){O(!1)}))}({id:t.id||"",title:n.find(".title").val(),comment:n.find(".comment").val(),prio:n.find(".prio").val(),due_date:n.find(".due_date").val(),status:n.find('[name="status"]:checked').val()},r),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})},Q=function(e){var t=[{id:1,name:"Yes"},{id:0,name:"No"}].map((function(t){return'\n                <div class="form-check d-block">\n                    <label class="form-check-label">\n                        <input type="radio" name="status" id="status'.concat(t.id,'" value="').concat(t.id,'" class="form-check-input status" \n                        ').concat(t.id==e.status?' checked="checked"':"","                           \n                        /> ").concat(t.name,"\n                    </label>                    \n                </div>\n            ")})).join("");return'\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Title</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm title" type="text" value="'.concat(e.title||"",'" />                                     \n                    </div>\n                </div> \n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Comment</label>\n                    <div class="col-9">\n                        <textarea class="form-control form-control-sm comment">').concat(e.comment||"",'</textarea>                                                  \n                    </div>\n                </div>\n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Due Date</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm fw-100 d-picker due_date" type="text" value="').concat(CvUtil.dtValidYMD(e.due_date||""),'" />                                     \n                    </div>\n                </div> \n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Prio</label>\n                    <div class="col-9">\n                        <input class="form-control form-control-sm fw-100 text-right prio" type="text" value="').concat(e.prio||"",'" />                                     \n                    </div>\n                </div>             \n                <div class="form-group row">                \n                    <label class="col-3 col-form-label-sm">Status</label>\n                    <div class="col-9">\n                        ').concat(t,"                              \n                    </div>\n                </div>         \n            ")},G=Object.keys(p);return a.createElement(f.Provider,{value:c(c({},W),{},{setStateSettings:M})},a.createElement(a.Fragment,null,a.createElement("h4",null,A?"Results (".concat(A.length," records)"):"No Results",a.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),a.createElement("div",{className:"sql-log-wrap"},a.createElement("pre",null,D)),!C&&a.createElement("div",null,"Loading..."),a.createElement("div",{className:"table-wrap position-relative"},a.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},a.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:V,disabled:w.loading},"Refresh"),a.createElement("button",{className:"btn btn-sm btn-success",onClick:B,disabled:w.loading},"Create a Task")),a.createElement("table",{className:"data-table editable border-0",id:b,style:{minWidth:500}},a.createElement("thead",null,a.createElement("tr",null,G.map((function(e,t){return"action_btn"!==e?a.createElement("th",{key:e,className:RHelper.isSortable(e,g)?" icon-order-wrap":"",onClick:function(t){return function(e){RHelper.isSortable(e,g)&&(e==o?m((function(e){return"desc"==e?"asc":"desc"})):(i(e),m("asc")))}(e)}},RHelper.getColName(e,p),RHelper.isSortable(e,g)&&o==e&&a.createElement("a",{className:"icon-order "+d,href:"#"})):a.createElement("th",{key:e})})))),a.createElement("tbody",null,a.createElement(k,{loading:S,searchData:F}),null!=A&&A.map((function(e,t){return a.createElement(y,{key:e.id,isTotal:!1,item:e,cols:G,loading:S,handleRowDelete:L,onClickCreateTask:B})})))))))}var w="undefined"!=typeof RTaskProps?RTaskProps:"undefined"!=typeof App?App.get_params(window.location):{};r.render(a.createElement(_,w),document.getElementById("root"))}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,a),o.loaded=!0,o.exports}a.m=t,e=[],a.O=(t,n,r,o)=>{if(!n){var l=1/0;for(u=0;u<e.length;u++){for(var[n,r,o]=e[u],i=!0,c=0;c<n.length;c++)(!1&o||l>=o)&&Object.keys(a.O).every((e=>a.O[e](n[c])))?n.splice(c--,1):(i=!1,o<l&&(l=o));if(i){e.splice(u--,1);var s=r();void 0!==s&&(t=s)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[n,r,o]},a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),a.j=62,(()=>{var e={62:0};a.O.j=t=>0===e[t];var t=(t,n)=>{var r,o,[l,i,c]=n,s=0;if(l.some((t=>0!==e[t]))){for(r in i)a.o(i,r)&&(a.m[r]=i[r]);if(c)var u=c(a)}for(t&&t(n);s<l.length;s++)o=l[s],a.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return a.O(u)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),a.nc=void 0;var r=a.O(void 0,[96],(()=>a(1443)));r=a.O(r)})();