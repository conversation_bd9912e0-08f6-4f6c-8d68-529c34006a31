'use strict';

import React from 'react';
import ReactDOM from 'react-dom';
// import {get_customer_ele, get_offer_ele} from "src/pages/order/TableDef";
// import {updateOrderInfo} from "src/pages/order/OrderBiz";
import {OfferCommentDisplayType} from "../shared/constants/offer";
import TextSearch from "src/shared/components/TextSearch";
import Pagination from "src/shared/components/Pagination";

const ROfferContext = React.createContext(null);
const TABLE_ID = 'table-offer';
const colsDefs = {
    'org_a': ['OrgA', '', false],
    'supplier_id': ['Supplier', '', true],
    'offer_sid': ['ID', '', true],
    'offer': ['Offer', '', true],
    'we': ['WE', 'i', true],
    'status': ['Status', 'select', true],
    'warehouse': ['Warehouse', 'select', true],
    'brand': ['Brand', 'select', true],
    'spread_out': ['Spread Out', 'select', true],
    // 'created_on': ['Created On', 'dt'],
    // 'updated_on': ['Updated On', 'dt'],
    'action_btn2': [''],
    //'top3_comments': ['Comment'],
    'top3_comments_supplier': ['Supp. Comment'],
    'top3_comments_internal': ['Int. Comment'],
    'top3_comments_customer': ['Cust. Comment'],
    /*'supplier_comments': ['Supp Comments', ''],
    'customer_comments': ['Cust Comments', ''],*/
    'action_btn': [''],
};

const colsWidth = {
    'org_a': 40,
    'supplier_id': 120,
    'offer_sid': 60,
    'offer': 200,
    'we': 50,
    'status': 100,
    'customer_comments': 200,
    'supplier_comments': 200,
    'top3_comments': 200,
    'top3_comments_supplier': 200,
    'top3_comments_internal': 200,
    'top3_comments_customer': 200,
    'warehouse': 55,
    'brand': 55,
    'spread_out': 55,
    'created_on': 90,
    'updated_on': 90,
    'action_btn': 40,   // Edit button
    'action_btn2': 30,
};
const sortColsDefs = ['supplier_id', 'offer_sid', 'offer', 'status', 'warehouse', 'brand', 'spread_out', 'created_on', 'updated_on', 'we'];

const getSearchCol = (col) => {
    switch (col) {
        case 'supplier_id':
            return 'supplier_name';
            break;
        default:
            return col;
            break;
    }
};

const statusList = [
    {id: 1, name: 'Yes'},
    {id: 0, name: 'No'},
];

const OfferStatusOptions = [
    {id: 1, name: 'Active'},
    {id: 2, name: 'In Progress'},
    {id: 0, name: 'Closed'},
];

const SpreadOutKv = {
    '0': '-',
    '1': 'Yes',
    '2': 'No',
    '3': 'Publish',
};

const get_supplier_ele = (id) => {
    return $(`tr#tr-${id} td input.supplier_id`);
};

const TableRow = (props) => {
    const [loading, setLoading] = React.useState(false);
    const [item, setItem] = React.useState(props.item);
    const [isEditing, setIsEditing] = React.useState(false);
    const [domReady, setDomReady] = React.useState(false);

    React.useEffect(() => {
        setDomReady(true);
        if (_.isEmpty(item.supplier_id)) {
            init_ac_supplier_id();
        }
    }, []);

    React.useEffect(() => {
        if (domReady && isEditing) {
            init_ac_supplier_id();
        }
    }, [isEditing]);

    React.useEffect(() => {
        setItem(props.item);
    }, [props.item]);

    React.useEffect(() => {
        if (domReady) {
            if (_.isEmpty(item.supplier_id)) {
                init_ac_supplier_id();
            }
        }
    }, [item.supplier_id]);

    const {sc_statuses, cc_statuses, offerCommentStatuses} = React.useContext(ROfferContext);

    const cb_ac_supplier_id = suggestion => {
        const $sup_ele = get_supplier_ele(item.id);
        if ($sup_ele.length > 0 && $sup_ele.closest('tr').find('input.offer_sid ').length < 1) {
            // we save here.
            setLoading(false);
            wait_icon($sup_ele.closest('td'));
            let merged = getMergedItem();
            App.ajax_post(get_ajax_url('OfferAction', 'save'), {
                isNew: 0,
                data: {
                    id: merged.id,
                    offer_sid: merged.offer_sid,
                    offer: merged.offer,
                    supplier_id: merged.supplier_id,
                },
            }, function (res) {
                setLoading(false);
                hide_wait_icon($sup_ele.closest('td'));
                if (!res.error) {
                    setItem(res.data.row);
                }
            }, function (res) {
                setLoading(false);
                hide_wait_icon($sup_ele.closest('td'));
            });
        }
    };

    const init_ac_supplier_id = () => {
        const value = item['supplier_id'] || '';
        init_autocomplete(get_supplier_ele(item.id), {
            class: 'SupplierAction',
            action: 'get_ac_suppliers',
            exactName: ''
        }, value != null && value.length > 1, null, null, cb_ac_supplier_id);
    };

    const getMergedItem = (colName, value) => {
        let merged = {...item};
        if (!_.isUndefined(colName)) {
            merged[colName] = value;
        }

        const sup_ele = get_supplier_ele(item.id);
        if (sup_ele.length > 0) {
            merged.supplierName = sup_ele.val();
            merged.supplier_id = merged.supplierName ? sup_ele.attr('selected-val') : '';
        }
        return merged;
    };

    /**
     * Edit/Save action
     *
     * @param e
     */
    const handleEditButton = (e) => {
        e.preventDefault();

        if (isEditing) {
            setLoading(true);

            const ele = get_supplier_ele(item.id);
            const supplier_id = ele.attr('selected-val');
            const supplierName = ele.val();
            setItem({...item, supplier_id: supplier_id, supplierName: supplierName});

            App.ajax_post(get_ajax_url('OfferAction', 'save'), {
                isNew: 0,
                data: {
                    id: item.id,
                    offer_sid: item.offer_sid,
                    offer: item.offer,
                    status: item.status,
                    warehouse: item.warehouse,
                    brand: item.brand,
                    spread_out: item.spread_out,
                    we: item.we,
                    supplier_id: supplier_id,
                },
            }, function (res) {
                setLoading(false);
                if (!res.error) {
                    setIsEditing(!isEditing);
                    setItem(res.data.row);
                }
            }, function (res) {
                setLoading(false);
            });
        } else {
            setIsEditing(!isEditing);
        }
    };

    const changeStatus = (status) => {
        setLoading(true);
        App.ajax_post(get_ajax_url('OfferAction', 'change_status'), {
            isNew: 0,
            data: {
                id: item.id,
                status: status,
            },
        }, function (res) {
            setLoading(false);
            if (!res.error) {
                setItem(res.data.row);
            }
        }, function (res) {
            setLoading(false);
        });
    };

    const handleCellChange = (colName, value) => {
        const ele = get_supplier_ele(item.id);
        const merged = {
            ...item, [colName]: value,
            supplier_id: ele.attr('selected-val'),
            supplierName: ele.val(),
        };
        setItem(merged);
    };

    /**
     * Open Supplier comments dialog.
     */
    const viewSupplierComments = () => {
        if (item.id && item.supplier_id) {
            // Open a modal dialog
            setDlgTitle($g_dlg, `Comments - ${item.offer_sid} - ${item.offer} / ${item.supplierName}`);
            setDlgBody($g_dlg, no_result);
            $g_dlg.modal({
                'backdrop': 'static',
                'show': true
            });

            App.ajax_post_ok(get_ajax_url('SupplierComment', 'get_list'), {
                offer_id: item.id,
                supplier_id: item.supplier_id,
            }, function (res) {
                if (!res.error) {
                    setDlgBody($g_dlg, res.data.html);
                }
            }, $g_dlg);
        }
    };

    const viewComments = (off_status_type) => {
        // Open a modal dialog
        setDlgTitle($g_dlg, item.offer + "'s Comments");
        setDlgBody($g_dlg, no_result);
        $g_dlg.modal({
            'backdrop': 'static',
            'show': true
        });
        let st = '';
        if (!_.isUndefined(off_status_type)) {
            switch (off_status_type) {
                case 'top3_comments_supplier':
                    st = OfferCommentDisplayType.SUPPLIER;
                    break;
                case 'top3_comments_internal':
                    st = OfferCommentDisplayType.INTERNAL;
                    break;
                case 'top3_comments_customer':
                    st = OfferCommentDisplayType.CUSTOMER;
                    break;
            }
        }

        App.ajax_post_ok(get_ajax_url('OfferAction', 'view_offer_comments'), {
            offer_id: item.id,
            off_status_type: st
        }, function (res) {
            if (!res.error) {
                setDlgBody($g_dlg, res.data.html);
            }
        }, $g_dlg);
    };

    const createSupplierComment = () => {
        let options_html = '';

        const statusIds = Object.keys(sc_statuses);
        let sc_customer_order_shown = false;
        let sc_customer_order_required = false;
        let comment_required = false;

        statusIds.forEach((statusId) => {
            const s = sc_statuses[statusId];
            const default_checked = s.sc_default_position == 'Default' ? ' checked="checked"' : '';
            options_html += `
                <div class="form-check d-block">
                    <label class="form-check-label">
                        <input type="radio" name="status" id="status${statusId}" value="${statusId}" class="form-check-input status" 
                        ${default_checked}                           
                        /> ${s.name}
                    </label>                    
                </div>
            `;
            if (s.sc_default_position == 'Default' && s.sc_customer_order == '1') {
                sc_customer_order_shown = true;
            }
            if (s.sc_default_position == 'Default' && s.sc_contact == '1') {
                comment_required = true;
            }
            if (sc_customer_order_shown && s.sc_customer_order_required) {
                sc_customer_order_required = true;
            }
        });

        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm ${comment_required ? 'required' : ''}">Comment</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-3 col-form-label-sm required">Status</label>
                    <div class="col-9">
                        ${options_html}                                                             
                    </div>
                </div>
                <div class="form-group row customer_order-wrap ${sc_customer_order_shown ? '' : ' d-none'}">                
                    <label class="col-3 col-form-label-sm ${sc_customer_order_required ? ' required' : ''}">Customer Order</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon">
                            <input class="form-control form-control-sm customer_order" type="text" />
                        </div>
                    </div>
                </div>
            `;
        const dlgObj = bs4pop.dialog({
            title: `Create a Comment of '${item.supplierName}'`,
            content: cont,
            className2: 'modal-dialog-scrollable',
            backdrop: 'static',
            width: 500,
            onShowEnd: () => {
                const $input = dlgObj.$el.find('.comment');
                $input.focus();

                // auto completion for an order
                const $customer_order = dlgObj.$el.find('.customer_order');
                init_autocomplete($customer_order, {
                    class: 'OrderAction',
                    action: 'get_ac_orders',
                    supplier_id: item.supplier_id
                }, false, null, () => {
                }, (suggestion) => {
                });

                // change events for status checkboxes.
                dlgObj.$el.find('input[name="status"]').change((e) => {
                    const statusId = e.target.value;
                    const s = sc_statuses[statusId] || null;

                    if (s != null) {
                        if (s.sc_customer_order == 1) {
                            dlgObj.$el.find('.customer_order-wrap').removeClass('d-none');
                        } else {
                            dlgObj.$el.find('.customer_order-wrap').addClass('d-none');
                        }
                        const comment_label = dlgObj.$el.find('textarea.comment').closest('.row').find('label');
                        if (s.sc_contact != 1) {
                            comment_label.removeClass('required');
                        } else {
                            comment_label.addClass('required');
                        }
                        if (s.sc_customer_order_required == 1) {
                            dlgObj.$el.find('.customer_order-wrap label').addClass('required');
                        } else {
                            dlgObj.$el.find('.customer_order-wrap label').removeClass('required');
                        }
                    }
                });
            },
            btns: [{
                label: 'Create',
                className: 'btn-info btn-sm',
                onClick: (evt) => {
                    // Getting customer order
                    let customer_order = '';
                    const statusId = dlgObj.$el.find('input[name="status"]:checked').val();
                    const s = sc_statuses[statusId] || null;
                    if (s != null) {
                        if (s.sc_customer_order == 1)
                            customer_order = dlgObj.$el.find('.customer_order').attr('selected-val');
                    }
                    const data = {
                        supplier_id: item.supplier_id,
                        comment: dlgObj.$el.find('.comment').val(),
                        status: dlgObj.$el.find('input[name="status"]:checked').val(),
                        offer_id: item.id,
                        customer_order: customer_order,
                    }
                    if ((s != null && s.sc_contact == 1) && data.comment.length < 1) {
                        App.info('Please fill comment.')
                        return false;
                    }
                    if (data.status.length < 1) {
                        App.info('Please select status.')
                        return false;
                    }
                    if (s != null && s.sc_customer_order == 1 && s.sc_customer_order_required == 1) {
                        if (customer_order.length < 1) {
                            App.info('Please fill the Customer Order.');
                            dlgObj.$el.find('.customer_order').focus();
                            return false;
                        }
                    }

                    // Save data.
                    App.ajax_post_ok(get_ajax_url('SupplierAction', 'save_comment'), {
                        data: data,
                        ref_page: 'offers'
                    }, (res) => {
                        if (res.error == false) {
                            setItem(res.data.row);
                            dlgObj.hide();
                        }
                    }, dlgObj.$el);
                    return false;
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    };


    const createCustomerComment = () => {
        const comments = item.customer_comments || [];
        let url = base_url + '/customer.php';
        if (comments && comments.length) {
            // select the customer
            const c_list = comments.filter(x => (x.customer_id || '').length == 15);
            if (c_list.length > 0) {
                url += `?customer_id=${c_list[0].customer_id}&offer_id=${c_list[0].offer_id}`;
            }
        }
        window.open(url, '_blank');
    };

    /**
     * Create an offer comment
     * @param id
     */
    const handleCreateComment = (id) => {
        let options_html = '';
        const statusIds = Object.keys(offerCommentStatuses);
        let sc_customer_order_shown = false;
        let sc_customer_order_required = false;
        let comment_required = false;

        statusIds.forEach((statusId) => {
            const s = offerCommentStatuses[statusId];
            const default_checked = s.sc_default_position == 'Default' ? ' checked="checked"' : '';
            options_html += `
                <div class="form-check d-block">
                    <label class="form-check-label">
                        <input type="radio" name="status" id="status${statusId}" value="${statusId}" class="form-check-input status" 
                        ${default_checked}                           
                        /> ${s.name} ${s.link_to_supplier == 1 ? '<span class="fs-z6 c-lightgrey">(Linked to the supplier)</span>' : ''}
                    </label>                    
                </div>
            `;
            if (s.sc_default_position == 'Default' && s.sc_customer_order == '1') {
                sc_customer_order_shown = true;
            }
            if (s.sc_default_position == 'Default' && s.sc_contact == '1') {
                comment_required = true;
            }
            if (sc_customer_order_shown && s.sc_customer_order_required) {
                sc_customer_order_required = true;
            }
        });

        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm ${comment_required ? 'required' : ''}">Comment</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm comment" rows="6"></textarea>                             
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Customer</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon">
                            <input class="form-control form-control-sm customer_id" type="text" />
                        </div>                                                  
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-3 col-form-label-sm required">Status</label>
                    <div class="col-9">
                        ${options_html}                                                             
                    </div>
                </div>
                <div class="form-group row customer_order-wrap ${sc_customer_order_shown ? '' : ' d-none'}">                
                    <label class="col-3 col-form-label-sm ${sc_customer_order_required ? ' required' : ''}">Customer Order</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon">
                            <input class="form-control form-control-sm customer_order" type="text" />
                        </div>
                    </div>
                </div>
            `;

        const dlgObj = bs4pop.dialog({
            title: `Create a Comment of '${item.offer}'`,
            content: cont,
            className2: 'modal-dialog-scrollable',
            backdrop: 'static',
            width: 500,
            onShowEnd: () => {
                const $input = dlgObj.$el.find('.comment');
                $input.focus();

                // auto completion for a customer
                const $customer_id = dlgObj.$el.find('.customer_id');
                init_autocomplete($customer_id, {
                    class: 'CustomerAction',
                    action: 'get_ac_customers',
                }, false, null, () => {
                }, (suggestion) => {
                });

                // auto completion for an order
                const $customer_order = dlgObj.$el.find('.customer_order');
                init_autocomplete($customer_order, {
                    class: 'OrderAction',
                    action: 'get_ac_orders',
                    offer_id: id
                }, false, null, () => {
                }, (suggestion) => {
                });

                // change events for status checkboxes.
                dlgObj.$el.find('input[name="status"]').change((e) => {
                    const statusId = e.target.value;
                    const s = offerCommentStatuses[statusId] || null;

                    if (s != null) {
                        if (s.sc_customer_order == 1) {
                            dlgObj.$el.find('.customer_order-wrap').removeClass('d-none');
                        } else {
                            dlgObj.$el.find('.customer_order-wrap').addClass('d-none');
                        }
                        const comment_label = dlgObj.$el.find('textarea.comment').closest('.row').find('label');
                        if (s.sc_contact != 1) {
                            comment_label.removeClass('required');
                        } else {
                            comment_label.addClass('required');
                        }
                        if (s.sc_customer_order_required == 1) {
                            dlgObj.$el.find('.customer_order-wrap label').addClass('required');
                        } else {
                            dlgObj.$el.find('.customer_order-wrap label').removeClass('required');
                        }
                    }
                });

            },
            btns: [{
                label: 'Create',
                className: 'btn-info btn-sm',
                onClick: (evt) => {
                    // Getting customer order
                    let customer_order = '';
                    const statusId = dlgObj.$el.find('input[name="status"]:checked').val();
                    const s = offerCommentStatuses[statusId] || null;
                    if (s != null) {
                        if (s.sc_customer_order == 1)
                            customer_order = dlgObj.$el.find('.customer_order').attr('selected-val') || '';
                    }
                    const data = {
                        offer_id: id,
                        comment: dlgObj.$el.find('.comment').val(),
                        sc_id: dlgObj.$el.find('input[name="status"]:checked').val(),
                        customer_id: dlgObj.$el.find('.customer_id').attr('selected-val'),
                        customer_order: customer_order,
                    }
                    if (data.sc_id.length < 1) {
                        App.info('Please select status.')
                        return false;
                    }
                    if (s != null && s.sc_customer_order == 1 && s.sc_customer_order_required == 1) {
                        if (customer_order.length < 1) {
                            App.info('Please fill the Customer Order.');
                            dlgObj.$el.find('.customer_order').focus();
                            return false;
                        }
                    }
                    createComment(data, dlgObj);
                    return false;
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    };

    const createComment = (comment, dlgObj) => {
        App.ajax_post_ok(get_ajax_url(), {
            class: 'OfferAction',
            action: 'save_comment',
            data: comment,
        }, function (res) {
            if (res.error == false) {
                setItem(res.data.row);
                dlgObj.hide();
            }
        }, dlgObj.$el);
    };

    const updateInfo = () => {
        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Comment</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm comment" rows="6">${item.info_comment || ''}</textarea>                             
                    </div>
                </div>
        `;

        const dlgUpdateInfo = bs4pop.dialog({
            title: `Update Offer Info`,
            content: cont,
            backdrop: 'static',
            width: 400,
            onShowEnd: () => {
                const $input = dlgUpdateInfo.$el.find('.comment');
                $input.focus();
            },
            btns: [{
                label: 'Update',
                className: 'btn-info btn-sm',
                onClick: (evt) => {
                    App.ajax_post_ok(get_ajax_url('Offer', 'update_info'), {
                        offer_id: item.id,
                        comment: dlgUpdateInfo.$el.find('.comment').val(),
                    }, function (res) {
                        if (res.error == false) {
                            setItem({...item, info_comment: res.data.row.comment})
                            dlgUpdateInfo.hide();
                            const $tt = $('#tr-' + item.id).find('.tt');
                            dispose_tooltip($tt);
                            init_tooltip($tt);
                        }
                    }, dlgUpdateInfo.$el);
                    return false;       // we don't close dialog.
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        });
    };

    // TD cell render
    // -----------------------------------------------------------------------------------------------

    const getTDHtml = (colName) => {
        const value = item[colName] || '';
        const colType = RHelper.getColType(colName, colsDefs);
        const isEditableCol = RHelper.isEditable(colName, colsDefs);
        const isNumericCol = RHelper.isNumeric(colName, colsDefs);

        let html = '';
        let td_cls = '';
        switch (colName) {
            case 'org_a':
            case 'status':
            case 'we':
            case 'warehouse':
            case 'brand':
            case 'spread_out':
            case 'supplier_id':
            case 'created_on':
            case 'updated_on':
            case 'action_btn':
            case 'action_btn2':
                td_cls += ' text-center';
                break;
        }

        if (isEditableCol && (isEditing || (colName == 'supplier_id' && _.isEmpty(item.supplier_id)))) {
            let cls = "form-control form-control-sm" + ' ' + colName;
            if (isNumericCol) cls += ' text-right';

            switch (colName) {
                case 'status':
                    html = <select className={cls}
                                   value={value || ''}
                                   onChange={e => handleCellChange(colName, e.target.value)}
                                   disabled={loading}
                    >
                        <option value={1}>Active</option>
                        <option value={2}>In Progress</option>
                        <option value={0}>Closed</option>
                    </select>;
                    break;
                case 'warehouse':
                    html = <select className={cls}
                                   defaultValue={value || ''}
                                   onChange={e => handleCellChange(colName, e.target.value)}
                                   disabled={loading}
                    >
                        <option value={1}>Yes</option>
                        <option value={0}>No</option>
                    </select>;
                    break;
                case 'brand':
                    html = <select className={cls}
                                   defaultValue={value || ''}
                                   onChange={e => handleCellChange(colName, e.target.value)}
                                   disabled={loading}
                    >
                        {Object.keys(SpreadOutKv).map(x => <option key={x} value={x}>{SpreadOutKv[x]}</option>)}
                    </select>;
                    break;
                case 'spread_out':
                    html = <select className={cls}
                                   defaultValue={value || ''}
                                   onChange={e => handleCellChange(colName, e.target.value)}
                                   disabled={loading}
                    >
                        {Object.keys(SpreadOutKv).map(x => <option key={x} value={x}>{SpreadOutKv[x]}</option>)}
                    </select>;
                    break;
                case 'supplier_id':
                    html = (
                        <div className="input-wrap-with-icon">
                            <input className={cls} type="text" disabled={loading}
                                   value={item.supplierName || ''}
                                   selected-val={value || ''}
                                   onChange={e => handleCellChange(colName, e.target.value)}
                            />
                        </div>
                    );
                    break;
                default:
                    html = <input className={cls}
                                  type="text"
                                  disabled={loading}
                                  value={value || ''}
                                  onChange={e => handleCellChange(colName, e.target.value)}
                    />;
                    break;
            }
        } else {
            let val = value || '';
            if (val != null && colType == 'dt') val = CvUtil.dtNiceDMY(val);
            const supplier_comments = _.get(item, 'supplier_comments', []);
            switch (colName) {
                case 'status':
                    /*html = value == '1' ? <i className="oi oi-circle-check tt" title="Click to change status."
                                             onClick={e => changeStatus(0)}></i> :
                        <i className="oi oi-circle-x tt" title="Click to change status."
                           onClick={e => changeStatus(1)}></i>;*/
                    let cls = '';
                    if (value == 1) {
                        cls = 'badge-success';
                    } else if (value == 2) {
                        cls = 'badge-primary';
                    } else {
                        cls = 'badge-secondary';
                    }

                    html = <span className={`badge ${cls}`}>{OfferStatusOptions.find(x=> x.id == value)?.name ?? ''}</span>
                    break;
                case 'supplier_id':
                    html = <React.Fragment>
                        <span className="supplier-info"> {item.supplierName || ''}</span>
                        {supplier_comments && supplier_comments.length > 0 &&
                            <i className={"oi oi-info m-0 float-right" + (supplier_comments.length > 0 ? " blue-f" : "")}
                               onClick={viewSupplierComments} title="Please click to view supplier comments."></i>}
                    </React.Fragment>;
                    break;
                case 'warehouse':
                    html = val == 1 ? 'Yes' : '';
                    break;
                case 'brand':
                    html = SpreadOutKv[val] ?? '';
                    break;
                case 'spread_out':
                    html = SpreadOutKv[val] ?? '';
                    break;
                case 'customer_comments':
                    html = <div className="position-relative pr-3">
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-3">{CvUtil.dtNiceDMY(c.created_on)}</div>
                            <div className="col-9">
                                {c.comment}
                            </div>
                        </div>)}
                        <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            {<i className={"oi oi-comment-square tt"} onClick={createCustomerComment}
                                title="Please click to create a comment."></i>}
                        </div>
                    </div>;
                    break;
                case 'supplier_comments':
                    html = <div className="position-relative pr-3">
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-3">{CvUtil.dtNiceDMY(c.created_on)}</div>
                            <div className="col-9">
                                {c.comment}
                            </div>
                        </div>)}
                        <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            {value && value.length > 0 &&
                                <i className={"oi oi-info m-0 mr-1" + (value && value.length > 0 ? " blue-f" : "")}
                                   onClick={viewSupplierComments} title="Please click to view supplier comments."></i>}
                            {item.id && item.supplier_id &&
                                <i className={"oi oi-comment-square tt"} onClick={createSupplierComment}
                                   title="Please click to create a comment."></i>}
                        </div>
                    </div>;
                    break;
                case 'top3_comments':
                    html = <div className="position-relative pr-3 fs-z6">
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-3 pr-0">{CvUtil.dtNiceDM(c.created_on)} ({c.created_by_name})</div>
                            <div className="col-9">
                                {c.comment}
                                <span className="c-lightgrey ml-2">{c.customer_name}</span>
                            </div>
                        </div>)}
                        <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            <i className={"oi oi-info m-0" + (value && value.length > 0 ? " blue-f" : "")}
                               onClick={e => {
                                   viewComments()
                               }} title="Please click to view all comments."></i>
                        </div>
                    </div>;
                    break;
                case 'top3_comments_supplier':
                case 'top3_comments_internal':
                case 'top3_comments_customer':
                    html = <div className="position-relative pr-3 fs-z6">
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-3 pr-0">{CvUtil.dtNiceDM(c.created_on)} ({c.created_by_name})</div>
                            <div className="col-9">
                                <span className="c-lightgrey mr-2">{c.customer_name}</span>
                                {c.comment}
                            </div>
                        </div>)}
                        {(value && value.length > 0) &&
                            <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                                <i className={"oi oi-info m-0" + (value && value.length > 0 ? " blue-f" : "")}
                                   onClick={e => {
                                       viewComments(colName)
                                   }} title="Please click to view all comments."></i>
                            </div>}
                    </div>;
                    break;
                case 'action_btn':
                    html = <React.Fragment>
                        <button className="btn btn-sm btn-sm-td btn-outline-success" onClick={handleEditButton}
                                disabled={loading}>{isEditing ? 'Save' : 'Edit'}</button>
                        {/*<button className="btn btn-sm btn-sm-td btn-light ml-2"
                                onClick={(e) => props.handleRowDelete(item)} disabled={loading}>Delete
                        </button>*/}
                    </React.Fragment>;
                    break;
                case 'action_btn2':
                    html = <i className="oi oi-comment-square action tt" title="Create a comment..."
                              onClick={e => handleCreateComment(item.id)}></i>;
                    break;
                case 'offer':
                    html = <React.Fragment>
                        <span className="tt" title={item.info_comment}> {item.offer || ''}</span>
                        {<i className={"oi oi-info m-0 float-right" + (supplier_comments.length > 0 ? " blue-f" : "")}
                            onClick={updateInfo} title="Please click to update offer information."></i>}
                    </React.Fragment>;
                    break;
                default:
                    html = val;
                    break;
            }
        }
        return <td key={colName} className={td_cls}>
            <div style={{width: _.get(colsWidth, colName, '')}}>{html}</div>
        </td>;
    };

    let tr_cls = '';
    if (item.warehouse == 1) tr_cls += ' light-yellow';

    return (
        <tr id={"tr-" + item.id} className={tr_cls}>
            {props.cols.map(col => getTDHtml(col))}
        </tr>
    );
};

function SearchForm({loading, form, setForm, orgAListAll}) {

    const onChange = (e) => {
        const {name, value, checked} = e.target;
        if (name == 'orgAList[]') {
            let orgAList = [...form.orgAList].filter(x => x != value);
            if (checked) orgAList.push(value);
            setForm({...form, orgAList: orgAList});
        } else {
            setForm({...form, [name]: name == 'noSupplier' ? (e.target.checked ? 1 : 0) : value});
        }
    };

    return (
        <div className="card border-0 bg-transparent">
            <div className="card-body p-0 pt-1">
                <div className="form-row">
                    <div className="col-auto mr-4">
                        <label className="mt-1">OrgA:</label>
                        {orgAListAll.map((a, ind) => (
                            <div key={'orgAList' + a} className="form-check d-inline-block ml-3">
                                <input type="checkbox" name="orgAList[]" id={'orgAList' + a}
                                       className="form-check-input"
                                       value={a}
                                       readOnly={loading}
                                       checked={_.indexOf(form.orgAList, a) >= 0}
                                       onChange={onChange}
                                />
                                <label htmlFor={'orgAList' + a} className="form-check-label">{a || ' - '}</label>
                            </div>
                        ))}
                    </div>

                    <div className="col-auto">
                        <div className="form-check d-inline-block mt-1">
                            <input type="checkbox" id="noSupplier" name="noSupplier" value={1}
                                   className="form-check-input"
                                   defaultChecked={form.noSupplier}
                                   onChange={onChange}
                            />
                            <label htmlFor="noSupplier" className="form-check-label">No Supplier?</label>
                        </div>
                    </div>
                    {/*<div className="col-auto">
                        <button className="btn btn-sm btn-info" onClick={onClickSearchButton} disabled={loading}>Search</button>
                    </div>*/}
                </div>
            </div>
        </div>
    );
}

const TableSearchRow = ({loading, form, setForm}) => {
    const onChange = (e) => {
        const {name, value} = e.target;
        setForm({...form, [name]: value});
    };

    const cols = Object.keys(colsDefs);

    let inputCls = "form-control form-control-sm";
    return (
        <tr id="row-search" className={"row-search" + (loading ? ' loading' : '')}>
            {cols.map((col, ind) => (
                (col !== 'action_btn' && col !== 'action_btn2') ?
                    <td key={col}>
                        {(col != 'top5_no_contacts' && col != 'offers' && col != 'org_a') &&
                            <div style={{width: _.get(colsWidth, col, 'auto')}}>
                                {(col != 'status' && col != 'warehouse' && col != 'brand' && col != 'spread_out') ?
                                    <TextSearch className={inputCls}
                                                name={getSearchCol(col)}
                                                value={form[getSearchCol(col)] || ''}
                                                onChange={onChange}
                                    /> :
                                    (col == 'spread_out' || col == 'brand' ? <select className={inputCls}
                                                                                     name={getSearchCol(col)}
                                                                                     defaultValue={form[form[getSearchCol(col)]] || ''}
                                                                                     onChange={onChange}
                                    >
                                        <option value=""></option>
                                        {Object.keys(SpreadOutKv).map((x, ind) => (
                                            <option key={x} value={x}>{SpreadOutKv[x]}</option>
                                        ))}
                                    </select> : <select className={inputCls}
                                                        name={getSearchCol(col)}
                                                        defaultValue={form[form[getSearchCol(col)]] || ''}
                                                        onChange={onChange}
                                    >
                                        <option value=""></option>
                                        {statusList.map((x, ind) => (
                                            <option key={x.id} value={x.id}>{x.name}</option>
                                        ))}
                                    </select>)
                                }
                            </div>
                        }
                    </td> :
                    <td key={col}></td>
            ))}
        </tr>
    );
};

/**
 * Offer component
 *
 * @param initialFormStates
 * @returns {*}
 * @constructor
 */
function ROffer({defaultSearchForm, defaultOrder, settings}) {
    // page statuses
    const [initFloatThead, setInitFloatThead] = React.useState(true);
    const [loading, setLoading] = React.useState(true);
    const [isLoaded, setIsLoaded] = React.useState(false);

    // sorting
    const [orderField, setOrderField] = React.useState(defaultOrder.field);
    const [orderDir, setOrderDir] = React.useState(defaultOrder.dir);

    // Settings
    const [stateSettings, setStateSettings] = React.useState(settings);
    const [orgAList, setOrgAList] = React.useState(settings.orgAList);

    // New item
    const newItemDefault = {
        id: '',
        offer_sid: '',
        offer: '',
        supplier_id: '',
        status: 1,
        warehouse: 1,
        brand: 0,
        spread_out: 0,
    };
    const [newItem, setNewItem] = React.useState(newItemDefault);

    // result data
    const [items, setItems] = React.useState([]);
    const [sql, setSql] = React.useState('');

    // Search Form
    const [form, setForm] = React.useState({...defaultSearchForm, orgAList: orgAList});

    // pagination
    const pagerDefault = {
        page: 0,
        pageCount: 0
    };
    const [pager, setPager] = React.useState(pagerDefault);

    // effect hooks
    // ----------------------------------------------------------------------------------
    React.useEffect(() => {
        getDataList({...form, with: 'form'});
    }, []);

    React.useEffect(() => {
        $('#' + TABLE_ID).floatThead({autoReflow: true});
    }, [initFloatThead]);

    React.useEffect(() => {
        if (isLoaded) {
            onRefresh(null);
        }
    }, [orderField, orderDir]);

    React.useEffect(() => {
        if (isLoaded) {
            onRefresh(null);
        }
    }, [form]);

    React.useEffect(() => {
        if (isLoaded) {
            onRefresh(null);
        }
    }, [pager.page]);

    // Function definitions
    // ----------------------------------------------------------------------------------
    /**
     * Refresh page with form filters
     *
     * @param e
     */
    const onRefresh = (e) => {
        let params = {...form};
        searchData(params);
    };

    const searchData = (data) => {
        data = Object.assign({}, form, data);
        getDataList(data);
    };

    const getDataList = (params) => {
        setLoading(true);

        let post_data = {pager};
        post_data = Object.assign({}, post_data, params);
        if (!('order_field' in post_data))
            post_data['order_field'] = orderField;
        if (!('order_dir' in post_data))
            post_data['order_dir'] = orderDir;

        App.ajax_post(get_ajax_url('OfferAction', 'get_list'), post_data, function (res) {
            setIsLoaded(true);
            setLoading(false);
            if (!res.error) {
                setResponseData(res.data);
            }
            setInitFloatThead(false);
        }, function (res) {
            setIsLoaded(true);
        });
    };

    /**
     * Set the response data.
     * */
    const setResponseData = (data) => {
        setItems([...data['rows']]);
        setPager(_.get(data, 'pager', pagerDefault));
        setSql(data['sql']);
        init_tooltip();
    };

    const changeOrderField = (col) => {
        if (!RHelper.isSortable(col, sortColsDefs)) return;
        if (col == orderField) {
            setOrderDir(prev => prev == 'desc' ? 'asc' : 'desc');
        } else {
            setOrderField(col);
            setOrderDir('asc');
        }
    }

    /**
     * Creating a new item
     * */
    const handleRowCreate = (item, $dlg) => {
        setLoading(true);
        let data = {...item};
        delete data.supplierName;
        App.ajax_post(get_ajax_url('Offer', 'save'), {
            data: data,
        }, function (res) {
            setLoading(false);
            if (!res.error) {
                setItems([res.data.row, ...items]);
                setNewItem({...newItemDefault});
                if (!_.isUndefined($dlg)) $dlg.hide();
            }
        }, function (res) {
            setLoading(false);
        });
    };

    const handleRowDelete = (item) => {
        bs4pop.confirm(`Are you sure you want to delete '${item.offer_sid}'?`, function (sure) {
            if (sure) {
                App.ajax_post(get_ajax_url('OfferAction', 'delete'), {
                    id: item.id,
                }, function (res) {
                    if (res.error == false) {
                        setItems(items.filter(function (obj) {
                            return obj.id != item.id;
                        }));
                    }
                });
            }
        }, {title: 'Delete entry'});
    };

    const onClickCreateOffer = (e, item) => {
        const isNew = !item.id;
        const cont = _get_offer_creation_form(item);
        const dlgObj = bs4pop.dialog({
            id: 'dlg-create-offer',
            title: isNew ? 'Create an Offer' : 'Update an Offer',
            content: cont,
            backdrop: 'static',
            className2: 'modal-dialog-scrollable',
            width: 500,
            onShowEnd: (e) => {
                const $el = dlgObj.$el;
                const $input = $el.find('input:first');
                $input.focus();
                init_autocomplete(
                    $('#dlg-create-offer input[name="supplier_id"]'), {
                        class: 'Supplier',
                        action: 'get_ac_suppliers',
                    }, _.get(item, 'supplier_id', '') != ''
                );
            },
            btns: [{
                label: isNew ? 'Create' : 'Update',
                onClick: (evt) => {
                    const $el = dlgObj.$el;
                    let data = {
                        id: item.id || '',
                        supplier_id: $el.find('[name="supplier_id"]').attr('selected-val') || '',
                        offer_sid: $el.find('[name="offer_sid"]').val(),
                        offer: $el.find('[name="offer"]').val(),
                        status: $el.find('[name="status"]').val(),
                        warehouse: $el.find('[name="warehouse"]').val(),
                        brand: $el.find('[name="brand"]').val(),
                        spread_out: $el.find('[name="spread_out"]').val(),
                        we: $el.find('[name="we"]').val(),
                    };
                    handleRowCreate(data, dlgObj);
                    return false;
                }
            }, {
                label: 'Close',
                className: 'btn-secondary btn-sm',
                onClick: e => {
                    e.hide();
                }
            }]
        })
    };

    const handlePageClick = (data) => {
        const merged = {...pager, page: data.selected};
        setPager(merged);
    };

    const _get_offer_creation_form = (item) => {
        const cont = `
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Supplier</label>
                    <div class="col-9">
                        <div class="input-wrap-with-icon fw-150">
                            <input class="form-control form-control-sm" name="supplier_id" type="text" value="${item.supplierName || ''}" selected-val="${item.supplier_id || ''}" />
                        </div>                                                             
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Offer ID</label>
                    <div class="col-9">
                        <input class="form-control form-control-sm" name="offer_sid" type="text" value="${_.get(item, 'offer_id', '')}" />                                     
                    </div>
                </div> 
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Offer</label>
                    <div class="col-9">
                        <textarea class="form-control form-control-sm" name="offer">${item.offer || ''}</textarea>
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">WE</label>
                    <div class="col-9">
                        <input class="form-control form-control-sm text-right fw-150" name="we" type="text" value="${_.get(item, 'we', '')}" />                                     
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Status</label>
                    <div class="col-9">
                        ${OfferStatusOptions.map((a, ind) => `
                             <div class="form-check d-inline-block ml-3 mt-1">
                                <input type="radio" name="status" id="${'status' + a.id}" class="form-check-input"
                                       value="${a.id}"
                                       ${item.status == a.id ? ' checked="checked"' : ''}
                                />
                                <label for="${'status' + a.id}" class="form-check-label">${a.name}</label>
                            </div>    
                        `).join('')}                                                 
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Warehouse?</label>
                    <div class="col-9">
                        ${statusList.map((a, ind) => `
                             <div class="form-check d-inline-block ml-3 mt-1">
                                <input type="radio" name="warehouse" id="${'warehouse' + a.id}" class="form-check-input"
                                       value="${a.id}"
                                       ${item.warehouse == a.id ? ' checked="checked"' : ''}
                                />
                                <label for="${'warehouse' + a.id}" class="form-check-label">${a.name}</label>
                            </div>    
                        `).join('')}
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Brand?</label>
                    <div class="col-9">
                        ${Object.keys(SpreadOutKv).map((a, ind) => `
                             <div class="form-check d-inline-block ml-3 mt-1">
                                <input type="radio" name="brand" id="${'brand' + a}" class="form-check-input"
                                       value="${a}"
                                       ${item.brand == a ? ' checked="checked"' : ''}
                                />
                                <label for="${'brand' + a}" class="form-check-label">${SpreadOutKv[a]}</label>
                            </div>    
                        `).join('')}
                    </div>
                </div>
                <div class="form-group row">                
                    <label class="col-3 col-form-label-sm">Spread Out?</label>
                    <div class="col-9">
                        ${Object.keys(SpreadOutKv).map((a, ind) => `
                             <div class="form-check d-inline-block ml-3 mt-1">
                                <input type="radio" name="spread_out" id="${'spread_out' + a}" class="form-check-input"
                                       value="${a}"
                                       ${item.spread_out == a ? ' checked="checked"' : ''}
                                />
                                <label for="${'spread_out' + a}" class="form-check-label">${SpreadOutKv[a]}</label>
                            </div>    
                        `).join('')}
                    </div>
                </div>
            `;
        return cont;
    };

    const cols = Object.keys(colsDefs);
    return (
        <ROfferContext.Provider value={{...stateSettings, setStateSettings: setStateSettings}}>
            <SearchForm
                form={form}
                setForm={setForm}
                loading={loading}
                orgAListAll={orgAList}
            />

            <h4>{items ? `Results (${items.length} records)` : "No Results"}
                <button className='btn-sql-view btn btn-light btn-sm'>Show/Hide SQL</button>
            </h4>
            <div className='sql-log-wrap'>
                <pre>{sql}</pre>
            </div>
            <div className="table-wrap position-relative">
                <div className="table-btn-wrap position-absolute" style={{top: -40, left: 450}}>
                    <button className="btn btn-sm btn-info mr-4" onClick={onRefresh} disabled={loading}>Refresh</button>
                    <button className="btn btn-sm btn-success" onClick={(e) => onClickCreateOffer(e, newItemDefault)} disabled={loading}>Create an
                        Offer
                    </button>
                    <span className="ml-5">{loading ? "Loading ..." : ""}</span>
                </div>
                <table id={TABLE_ID} className="data-table editable border-0" style={{minWidth: 500}}>
                    <thead>
                    <tr>
                        {cols.map((col, ind) =>
                            ('action_btn' !== col && 'action_btn2' !== col) ?
                                <th
                                    key={col}
                                    className={(RHelper.isSortable(col, sortColsDefs) ? ' icon-order-wrap' : '') + RHelper.getAlign(col, colsDefs)}
                                    onClick={e => changeOrderField(col)}
                                >
                                    <div style={{width: _.get(colsWidth, col, '')}}>
                                        {RHelper.getColName(col, colsDefs)}
                                        {RHelper.isSortable(col, sortColsDefs) && orderField == col &&
                                            <a className={"icon-order " + (orderDir)} href="#"></a>}
                                    </div>
                                </th> :
                                <th key={col}></th>
                        )}
                    </tr>
                    </thead>
                    <tbody>
                    <TableSearchRow
                        form={form}
                        setForm={setForm}
                        loading={loading}
                    />
                    {items.map((item, ind) => (
                        <TableRow key={item.id}
                                  isTotal={false}
                                  item={item}
                                  cols={cols}
                                  loading={loading}
                                  handleRowDelete={handleRowDelete}
                        />
                    ))}
                    </tbody>
                </table>
                <Pagination
                    pager={pager}
                    onPageChange={handlePageClick}
                />
            </div>
        </ROfferContext.Provider>
    );
}

// Setting up Props.
let props = typeof ROfferProps !== "undefined" ? ROfferProps : (typeof App !== 'undefined' ? App.get_params(window.location) : {});
ReactDOM.render(
    React.createElement(ROffer, props),
    document.getElementById('root')
);