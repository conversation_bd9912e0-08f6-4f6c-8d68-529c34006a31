<?php

/**
 * Class CustomerAction
 */
class CustomerAction extends BaseAction
{
    /**
     * @var CustomerModel
     */
    private $customer_model = null;

    function __construct()
    {
        parent::__construct();

        $this->customer_model =& Loader::get_instance()->load_model('CustomerModel');
    }

    public function save()
    {
        $this->post_restrict();

        $category_id = $_POST['category_id'] ?? null;

        $row = $_POST['data'];
        $name = $row['name'] ?? '';
        if (empty($name)) {
            $this->return(true, "Please fill customer name.");
        }

        $id = $row['id'] ?? null;
        if (empty($id)) {
            // validation
            $x = $this->customer_model->get_by_field('name', $name, 1);
            if ($x) {
                $this->return(true, "The customer '" . $name . "' already exists!");
            }
            $success = false;
            unset($row['id']);
            $id = $this->customer_model->insert($row, false);
            if ($id) {
                $success = true;
            }
        } else {
            // validation
            $where = sprintf(" id != %s AND name = %s", $this->db->safe_value($id), $this->db->safe_value($name));
            $x = $this->customer_model->get_by_where($where, 1);
            if ($x) {
                $this->return(true, "The customer name '" . $name . "' already exists!");
            }
            $success = $this->customer_model->update($row, $id, false);
        }

        // Update category info
        if ($success) {
            if (isset($_POST['category_id'])) {
                $category_id = $_POST['category_id'];
                $category = $this->customer_model->get($category_id, 1, BaseModel::TBL_CATEGORIES);

                $customer_category = [
                    'category_id' => $category_id,
                    'customer_id' => $id,
                ];
                if ($category) {
                    $this->db->replace(BaseModel::TBL_CUSTOMER_CATEGORIES, $customer_category);
                } else {
                    $this->customer_model->delete_by_where($this->db->where_by_array($customer_category), BaseModel::TBL_CUSTOMER_CATEGORIES);
                }
            }
        }

        $this->data['row'] = $this->customer_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->customer_model->get($id, 1);
        if (!empty($row)) {
            $this->customer_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $category_model =& Loader::get_instance()->load_model('CategoryModel');
        $categories = $category_model->get_list([], true);

        $rows = $this->customer_model->get_list($_POST);
        $this->data = [
            'rows' => $rows,
            'categories' => $categories,
            'sql' => $this->db->show_query_history(false)
        ];
        $this->data['sql'] = $this->db->show_query_history(false);
        $this->return();
    }

    public function update_categories()
    {
        $this->post_restrict();

        $customer_id = $_POST['customer_id'] ?? '';
        $category_ids = $_POST['category_ids'] ?? [];
        if (empty($customer_id)) $this->return(true, 'Invalid request. Customer does not exist.');

        $this->customer_model->delete_by_where($this->db->where_by_array(['customer_id' => $customer_id]), BaseModel::TBL_CUSTOMER_CATEGORIES);
        if (!empty($category_ids)) {
            $data = [];
            foreach ($category_ids as $category_id) {
                $data[] = [
                    'customer_id' => $customer_id,
                    'category_id' => $category_id,
                ];
            }
            $this->error = !$this->db->insert_batch(BaseModel::TBL_CUSTOMER_CATEGORIES, $data);
        }
        if ($this->error) {
            $this->msg = 'Updated successfully.';
        } else
            $this->msg = 'Failed to update categories.';

        $this->data['row'] = $this->customer_model->get_row($customer_id);

        $this->return();
    }

    public function update_info()
    {
        $this->post_restrict();

        $customer_id = get_var('customer_id');
        $comment = get_var('comment');
        $row = compact('customer_id', 'comment');

        $old = $this->customer_model->get_by_field('customer_id', $customer_id, 'id', BaseModel::TBL_CUSTOMER_INFO);
        if ($old) {
            $row['id'] = $old['id'];
            $this->error = !$this->db->update(BaseModel::TBL_CUSTOMER_INFO, $row, $this->db->where_equal('id', $row['id'], ''));
        } else {
            $row['id'] = $this->db->gen_pk(BaseModel::TBL_CUSTOMER_INFO);
            $this->error = !$this->db->insert(BaseModel::TBL_CUSTOMER_INFO, $row);
        }
        if ($this->error) $this->msg = 'Failed to update information.';
        $this->data['row'] = $row;
        $this->return();
    }

    public function view_comments()
    {
        $this->post_restrict();
        $this->msg = '';

        $customer_id = $_POST['customer_id'] ?? '';
        if (empty($customer_id)) {
            $this->return(true, "No customer is connected to this order!");
        }

        $s = $this->customer_model->get($customer_id, 1);
        if (empty($s)) {
            $this->return(true, "Customer does not exist!");
        }

        $params = $_POST;
        $comment_model =& Loader::get_instance()->load_model('CustomerCommentModel');
        $rows = $comment_model->get_list($params);

        Loader::get_instance()->load_helper('customer_helper');
        $this->data['html'] = table_customer_comments($rows);

        $this->return();
    }

    public function save_comment()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $customer_id = $row['customer_id'] ?? '';
        if (empty($customer_id)) {
            $this->return(true, "Invalid request!");
        }

        $s = $this->customer_model->get($customer_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Supplier does not exist!");
        }

        $comment_model =& Loader::get_instance()->load_model('CustomerCommentModel');

        $id = $row['id'] ?? null;
        if (empty($id)) {
            $success = false;
            unset($row['id']);
            $id = $comment_model->insert($row, true, true);
            if ($id) {
                $success = true;
            }
        } else {
            $success = $comment_model->update($row, $id);
        }
        $this->data['row'] = $this->customer_model->get_row($customer_id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    /**
     *
     * Get customers list for auto completion list.
     */
    public function get_ac_customers()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';
        $keyword = $_POST['keyword'] ?? '';

        $where .= $this->db->where_like('(name)', $keyword, true);

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                id as data,
                IFNULL(`name`, ' - ') as value
            FROM customers
            WHERE TRUE $where
            $limit_str
        ";
        $this->logger->debug($sql);
        $this->data = $this->db->query_select($sql);
        $this->return();
    }

    /**
     *
     * Get customers list for auto completion list.
     */
    public function get_ac_customers_full()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';
        $keyword = $_POST['keyword'] ?? '';

        $where .= $this->db->where_like("CONCAT(IFNULL(cust_no, ''), '^', IFNULL(`name`, ''), '^', IFNULL(country, ''))", $keyword, true);

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                CONCAT(id, '^', IFNULL(cust_no, ''), '^', IFNULL(`name`, ''), '^', IFNULL(country, '')) as data,
                CONCAT(IFNULL(cust_no, ''), '/', IFNULL(`name`, ''), '/', IFNULL(country, '')) as value
            FROM customers
            WHERE TRUE $where
            $limit_str
        ";
        $this->logger->debug($sql);
        $this->data = $this->db->query_select($sql);
        $this->return();
    }

}