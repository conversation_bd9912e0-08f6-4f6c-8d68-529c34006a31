<?php

/**
 * Get a date from the specified timestamp by offset years.
 *
 * @param $years
 * @param string $format
 * @param null $ts
 * @return false|string
 */
function get_years_date($years, $format = 'Y-m-d', $ts = null)
{
    $prefix = ($years > 0) ? "+$years" : "$years";

    return date($format, strtotime("$prefix years", $ts == null ? time() : $ts));
}


/**
 * Convert timestamp to db date string.
 * @param null $time
 * @return false|string|null
 */

function time2db_date($time = null)
{
    if ($time == null) return null;
    return date('Y-m-d', $time ? $time : time());
}


/**
 * Convert timestamp to db datetime string
 * @param $time
 * @return false|string
 */
function time2db_datetime($time = null)
{
    return date('Y-m-d H:i:s', $time ? $time : time());
}


/**
 * Convert the date string in Excel file to db date format.
 * This is used by importing from AMZ excel file.
 *
 * e.g. 27. Okt 2019 08:16 PM MEZ   --->    2019-10-27
 * @param $str
 * @return bool|string|null
 */
function ebay_dt_str2db_datetime($str)
{
    if (strlen($str) < 10) return NULL;

    $de_en = array(
        'Mär' => 'Mar',
        'Mai' => 'May',
        'Okt' => 'Oct',
        'Dez' => 'Dec',
    );
    $str = str_replace("Mrz", 'Mar', $str);

    foreach ($de_en as $de => $en) {
        if (strpos($str, $de) !== false) {
            $str = str_replace($de, $en, $str);
            break;
        }
    }

    list($d, $m, $y, $t, $apm, $tz) = explode(' ', $str);

    $tz_str = null;
    for ($i = 0; $i < strlen($tz); $i++) {
        $tz_str .= "\\" . substr($tz, $i, 1);
    }

    $dt = date_create_from_format("j. M Y h:i A " . $tz_str, $str);
    if ($dt)
        return $dt->format('Y-m-d H:i:s');
    else {
        pr("Invalid Date Format: [$str]");
        return false;
    }
}


/**
 * Convert the date string in CSV file to db date format.
 * This format applies to ebay-join ads file.
 *
 * e.g. 18.9.19     --->    2019-09-18
 */

function ebay_dt_ebayjoin2db_datetime($str)
{
    $dt = false;
    // Check if format is dd.mm.yyyy
    if (strlen($str) == 10) {
        $dt = date_create_from_format("d.m.Y", $str);
    } else {
        $dt = date_create_from_format("j.n.y", $str);
    }

    if ($dt == false) {
        pr("Invalid Date Format on eBay-join file. [$str]");
    } else {
        return $dt->format(DATE_FORMAT_YMD);
    }
}

function get_months()
{
    return array('Jan.', 'Feb.', 'Mrz.', 'Apr.', 'Mai.', 'Jun.', 'Jul.', 'Aug.', 'Sep.', 'Okt.', 'Nov.', 'Dez.');
}

/**
 * Get last months id array: e.g. [2019-01, 2019-02, ..., ]
 * @param $n_last_months
 * @param $ts
 * @return array
 */
function get_last_months($n_last_months, $ts)
{
    $last_months = [];
    for ($i = 0; $i <= $n_last_months; $i++) {
        $month_id = date('Y-m', strtotime("first day of -$i month", $ts));
        $last_months[] = $month_id;
    }
    return $last_months;
}


/**
 * Get last months id array: e.g. [2019-01, 2019-02, ..., ]
 * @param $from_date
 * @param $to_date
 * @return array
 */
function get_months_by_range($from_date, $to_date = '')
{
    $months = [];

    $time1 = strtotime_ymd($from_date);
    $time2 = $to_date ? strtotime_ymd($to_date) : time();
    if ($time1 > $time2) return $months;

    $month_id_to = date('Y-m', $time2);
    $ind = 0;
    do {
        $month_id = date('Y-m', strtotime("first day of +$ind month", $time1));
        $months[] = $month_id;
        $ind++;
    } while ($month_id < $month_id_to);

    return $months;
}


/**
 * Get last financial years array: e.g. [19/20, 18/19, ..., ]
 * @param $years_count
 * @param int $start_month
 * @return array
 */
function get_fiscal_years($years_count, $start_month = 7)
{
    $last_year = get_fiscal_year($start_month);
    $data = [];
    for ($i = $last_year; $i > $last_year - $years_count; $i--) {
        $data[$i] = get_fiscal_year_label($i);
    }
    return $data;
}


/**
 * Get last financial year label: e.g. 19/20, 18/19, etc
 * @param $fiscal_year
 * @return string
 */
function get_fiscal_year_label($fiscal_year)
{
    return (($fiscal_year - 1) % 100) . "/" . (($fiscal_year) % 100);
}


/**
 * Get last financial months array: e.g. [07, 08, ..., ]
 * @param $fiscal_year
 * @param int $start_month
 * @param bool $sort_dir_asc
 * @return array
 */
function get_fiscal_months($fiscal_year, $start_month = 7, $sort_dir_asc = true)
{
    $data = [];
    for ($i = 0; $i < 12; $i++) {
        $ind = $sort_dir_asc ? $i : 11 - $i;
        $mon = (($start_month - 1 + $ind) % 12) + 1;
        $data[$mon] = sprintf('%s-%02d', ($start_month - 1 + $ind) >= 12 ? $fiscal_year : $fiscal_year - 1, $mon);
    }
    return $data;
}


/**
 * Get current fiscal Year
 * @param int $start_month
 * @return int
 */
function get_fiscal_year($start_month = 7)
{
    $ts_today = time();
    $last_year = intval(date('Y', $ts_today));

    if (date('Y-m-d', $ts_today) >= sprintf('%s-%02d-01', $last_year, $start_month)) {
        $last_year++;
    }
    return $last_year;
}


/**
 * Convert 2020-04-20           -> timestamp (seconds)
 *
 * @param $str
 * @param string $format
 * @return int|null
 */
function strtotime_ymd($str, $format = "Y-m-d")
{
    $dt = date_create_from_format($format, $str);
    if ($dt == false) {
        return null;
    } else {
        return $dt->getTimestamp();
    }
}


/**
 * Get diff days
 *
 * @param $date_str
 * @param null $target_str
 * @return int|null
 */
function get_days_from_dt_str($date_str, $target_str = null, $show_negative = false)
{
    $date_dt = date_create_from_format(DATE_FORMAT_YMD, $date_str);
    if ($date_dt !== false) {
        $today_dt = date_create($target_str == null ? date(DATE_FORMAT_YMD) : $target_str);
        $show = $today_dt !== false;
        if (!$show_negative) $show &= ($date_dt < $today_dt);
        if ($show) {
            $diff = date_diff($date_dt, $today_dt);
            $days = $diff->format('%a');
            if ($date_dt > $today_dt) $days = '-' . $days;
            return $days;
        }
    }
    return null;
}

/**
 * Get today date string.
 *
 * @param string $format
 * @return false|string
 */
function get_today($format = DATE_FORMAT_YMD)
{
    return date($format);
}


/**
 * 202018,2020-04-27,2020-05-03
 *
 * 18th week
 *
 * @param $from_date
 * @param $to_date
 */
function get_weeks_keys($from_date, $to_date)
{
    $dt = date_create_from_format(DATE_FORMAT_YMD, $from_date);
}


/**
 * Check if a parameter is this week? This parameter value is given by db result set.
 *
 * @param $week_str     e.g. 202032|2020-08-03|2020-08-09
 * @return bool
 */
function is_this_week($week_str)
{
    list($yw, $from_date, $to_date) = explode('|', $week_str);
    $today = get_today();
    return $from_date <= $today && $today <= $to_date;
}

function is_today($week_str, $col_ind)
{
    list($yw, $from_date, $to_date) = explode('|', $week_str);
    $current_date = date('Y-m-d', strtotime_ymd($from_date) + $col_ind * DAY_SECONDS);
    $today = get_today();
    return $from_date <= $today && $today <= $to_date && ($current_date == $today);
}

function before_10_pm()
{
    return intval(date('H')) < 22;
}

function ymd_to_mdy($str, $year_show = false)
{
    list($y1, $m1, $d1) = explode('-', $str);
    if ($year_show)
        return sprintf('%s.%s.%s', $d1, $m1, intval($y1) % 100);
    else
        return sprintf('%s.%s.', $d1, $m1);
}

function get_date_range_str(&$params, $year_show = false)
{
    $from_date = $params['from_date'] ?? '';
    $to_date = $params['to_date'] ?? '';
    $ret_str = '';
    if ($from_date) {
        $ret_str .= ymd_to_mdy($from_date, $year_show);
    } else {
        $ret_str .= 'Since beginning';
    }
    if ($ret_str) $ret_str .= ' ~ ';
    if ($to_date) {
        $ret_str .= ymd_to_mdy($to_date, $year_show);
    } else {
        $ret_str .= 'Now';
    }
    return $ret_str;
}

function get_week_range_bounds($week, $year)
{
    $dto = new DateTime();
    $dto->setISODate($year, $week);
    $ret['week_start'] = $dto->format(DATE_FORMAT_YMD);
    $dto->modify('+6 days');
    $ret['week_end'] = $dto->format(DATE_FORMAT_YMD);
    return $ret;
}

function get_monday($date_str)
{
    return date(DATE_FORMAT_YMD, strtotime("next monday", strtotime_ymd($date_str)) - 7 * DAY_SECONDS);
}

function get_sunday($date_str)
{
    return date(DATE_FORMAT_YMD, strtotime("next monday", strtotime_ymd($date_str)) - 1 * DAY_SECONDS);
}


if (!function_exists('get_label_from_year_month')) {
    /**
     * e.g. 2019-04 --> Apr '19
     * @param $year_month
     * @return string
     */
    function get_label_from_year_month($year_month)
    {
        $month_labels = get_months();
        $year = substr($year_month, 2, 2);
        $month = intval(substr($year_month, 5));
        return sprintf("%s %s", $month_labels[$month - 1], $year);
    }
}

function dt_nice_dmy($str)
{
    $ts = strtotime_ymd(substr($str, 0, 10));
    if ($ts)
        return date(DATE_FORMAT_NICE_DMY, $ts);
    return '';
}
