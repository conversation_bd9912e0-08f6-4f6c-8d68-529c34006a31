<?php

/**
 * Class OrderAction
 */
class OrderAction extends BaseAction
{
    /**
     * @var OrderModel
     */
    private $order_model = null;

    function __construct()
    {
        parent::__construct();

        $this->order_model =& Loader::get_instance()->load_model('OrderModel');
    }

    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $old_order_id = $_POST['old_order_id'] ?? null;
        $order_id = $row['order_id'] ?? null;
        $name = $row['name'] ?? '';
        $isNew = $_POST['isNew'] ?? null;
        $warehouse_changed = false;

        if (empty($order_id)/* || empty($name)*/) {
            $this->return(true, "Please fill Order ID & Name.");
        }

        // data normalization
        if (isset($row['est_loading_date']) && !$row['est_loading_date']) {
            $row['est_loading_date'] = NULL;
        }
        if (isset($row['loading_date']) && !$row['loading_date']) {
            $row['loading_date'] = NULL;
        }

        if ($isNew) {
            $x = $this->order_model->get($order_id);
            if ($x) {
                $this->return(true, "The order '" . $order_id . "' already exists!");
            }
            $success = false;
            $order_id = $this->order_model->insert($row, true, true);
            if ($order_id) {
                $success = true;
            }
            $warehouse_changed = true;
        } else {
            $x = $this->order_model->get($old_order_id);
            if (!$x) $this->return(true, "The order '" . $order_id . "' does not exist!");

            $warehouse_changed = (isset($row['warehouse']) && $row['warehouse'] != $x['warehouse']);

            // validation
            $where = sprintf(" order_id != %s AND order_id = %s", $this->db->safe_value($old_order_id), $this->db->safe_value($order_id));
            $x2 = $this->order_model->get_by_where($where, 1);
            if ($x2) {
                $this->return(true, "The order ID'" . $order_id . "' already exists!");
            }
            $success = $this->order_model->update($row, $old_order_id, false);
        }
        // Save order_info
        $order_info = $_POST['order_info'] ?? [];
        if ($success && !empty($order_info)) {
            $order_info['order_id'] = $order_id;
            $old = $this->order_model->get_by_field('order_id', $order_id, 'id', BaseModel::TBL_ORDER_INFO);
            if ($old) {
                $order_info['id'] = $old['id'];
                $success = $this->db->update(BaseModel::TBL_ORDER_INFO, $order_info, $this->db->where_equal('id', $order_info['id'], ''));
            } else {
                $order_info['id'] = $this->db->gen_pk(BaseModel::TBL_ORDER_INFO);
                $success = $this->db->insert(BaseModel::TBL_ORDER_INFO, $order_info);
            }
        }

        // update statuses
        // -----------------------------------------------------------------------
        $order = $this->order_model->get($order_id);
        if ($success && $warehouse_changed && $order['warehouse'] == 1 && $order['category'] != 'FD') {
            $order_comment_model =& load_model('OrderCommentModel');

            $c['order_id'] = $order_id;
            if (isset($c['status']) && $c['status'] === '') {
                $c['status'] = NULL;
            }

            // Setting default statuses of the order: OrderSupplierComment and OrderCustomerComment
            $order_comments = $order_comment_model->get_list([
                'order_id' => $order_id,
            ]);
            $existed_sc_ids = [];
            foreach ($order_comments as $oc) {
                $existed_sc_ids[] = $oc['sc_id'];
            }

            // all sys_config available
            $sc_list = SysConfigModel::get_instance()->get_list(['types' => [
                // SysConfigModel::CONFIG_TYPE_ORDER_CUSTOMER_STATUS,
                SysConfigModel::CONFIG_TYPE_ORDER_SUPPLIER_STATUS,
            ]]);
            $new_comments = [];
            $user_id = Auth::current_user_id();
            $today = time2db_datetime();
            foreach ($sc_list as $x) {
                if (!in_array($x['id'], $existed_sc_ids)) {
                    $default_value = $x['value'] === null || $x['value'] === '' ? 0 : $x['value'];

                    // We hard-coded this exception.
                    if ($x['order'] < 500) {
                        $default_value = 2; // N/A
                    }
                    if ($x['order'] >= 500) {
                        $default_value = 0; // N/A
                    }

                    // override
                    $default_value = $this->order_model->get_order_comment_status_default_value($order, $x, $default_value);

                    $new_comments[] = [
                        'id' => $order_comment_model->gen_pk(),
                        'order_id' => $order_id,
                        'sc_id' => $x['id'],
                        'status' => $default_value,
                        'created_by' => $user_id,
                        'created_on' => $today,
                        'updated_by' => $user_id,
                        'updated_on' => $today,
                    ];
                }
            }
            if (!empty($sc_list)) {
                $order_comment_model->insert_batch($new_comments);
            }
        }
        // -----------------------------------------------------------------------
        if ($success) {
            //$this->data['row'] = defined('API_CALL') ? $this->order_model->get($order_id) : $this->order_model->get_row($order_id);
            $this->data['row'] = $this->order_model->get_row($order_id);
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();

        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->order_model->get($id, 1);
        if (!empty($row)) {
            $this->order_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }

        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();

        $this->msg = '';

        // Get totals
        $this->order_model->calc_count(TRUE);
        $rows_count = $this->order_model->get_list($_POST);

        // Get limited rows
        $pagination = new Pagination($this->get_pager_page(), $rows_count, $this->get_pager_limit());
        $this->order_model->set_pager($pagination);
        $this->order_model->calc_count(FALSE);
        $rows = $this->order_model->get_list($_POST);

        $this->data = [
            'rows' => $rows,
            'pager' => $pagination->getPagerResponse(),
            'sql' => $this->db->show_query_history(false)
        ];
        $this->data['sql'] = $this->db->show_query_history(false);

        $this->return();
    }

    /**
     * ----------------------------------------------------------------------------------------
     * Get Orders list for auto completion list.
     * ----------------------------------------------------------------------------------------
     */
    public function get_ac_orders()
    {
        $this->post_restrict();
        $this->msg = '';

        $keyword = $_POST['keyword'] ?? '';
        $supplier_id = $_POST['supplier_id'] ?? '';
        $customer_id = $_POST['customer_id'] ?? '';
        $offer_id = $_POST['offer_id'] ?? '';

        $where = '';
        $where .= $this->db->where_like("CONCAT(ord.order_id, ' - ', ord.name, ' - ', IFNULL(c.name, ''))", $keyword, true);
        $where .= $this->db->where_equal('off.supplier_id', $supplier_id);
        $where .= $this->db->where_equal('ord.customer_id', $customer_id);
        $where .= $this->db->where_equal('ord.offer_id', $offer_id);

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                ord.order_id AS data,
                CONCAT(ord.order_id, ' - ', ord.name, ' - ', IFNULL(c.name, '')) AS value
            FROM orders ord
                INNER JOIN offers off ON off.id = ord.offer_id
                INNER JOIN customers c ON ord.customer_id=c.id 
            WHERE TRUE $where
            $limit_str
        ";
        $this->data = $this->db->query_select($sql);
        $this->logger->debug($sql);
        $this->return();
    }

    public function update_info()
    {
        $this->post_restrict();

        $order_id = get_var('order_id');
        $comment = get_var('comment');
        $row = compact('order_id', 'comment');

        $old = $this->order_model->get_by_field('order_id', $order_id, 'id', BaseModel::TBL_ORDER_INFO);
        if ($old) {
            $row['id'] = $old['id'];
            $this->error = !$this->db->update(BaseModel::TBL_ORDER_INFO, $row, $this->db->where_equal('id', $row['id'], ''));
        } else {
            $row['id'] = $this->db->gen_pk(BaseModel::TBL_ORDER_INFO);
            $this->error = !$this->db->insert(BaseModel::TBL_ORDER_INFO, $row);
        }
        if ($this->error) $this->msg = 'Failed to update information.';
        $this->data['row'] = $row;
        $this->return();
    }

    /**
     * ----------------------------------------------------------------------------------------
     * Important. This is public accessible.
     * ----------------------------------------------------------------------------------------
     */
    public function create()
    {
        $order_id = $_REQUEST['id'] ?? '';
        $name = $_REQUEST['name'] ?? '';
        $au_master = $_REQUEST['au_master'] ?? null;

        if (empty($order_id) || empty($name)) {
            $this->return(true, "Please specify 'id' and 'name' parameters in the request. e.g. /api.php?{whatever}&id=XXX&name=YYYY");
        }

        $customer_name = $_REQUEST['cname'] ?? '';
        if ($customer_name) {
            $cust = $this->order_model->get_by_field('name', $customer_name, 'id', 'customers');
            if ($cust) {
                $_POST['data']['customer_id'] = $cust['id'];
            }
        }

        $_POST['isNew'] = 1;
        $_POST['data']['order_id'] = $order_id;
        $_POST['data']['name'] = $name;
        $_POST['data']['au_master'] = $au_master;
        $this->set_perm_check_required(FALSE);
        $this->save();
    }

    /**
     * Order Supplier & Customer Comments
     */
    public function update_comments()
    {
        $this->post_restrict();

        $order_id = get_var('order_id');
        $comments = $_POST['comments'] ?? [];
        $order = $this->order_model->get_by_field('order_id', $order_id);
        if (!$order) $this->return(true, 'Order does not exist.');

        $success = true;
        if (!empty($comments)) {
            $order_comment_model =& load_model('OrderCommentModel');
            foreach ($comments as $c) {
                $comment_id = $c['id'] ?? '';
                $sc_id = $c['sc_id'];
                $sc = SysConfigModel::get_instance()->get_by_field('id', $sc_id);
                $comment = $order_comment_model->get_by_where($this->db->where_by_array([
                    'order_id' => $order_id,
                    'sc_id' => $c['sc_id'],
                ]));
                // comment exists?
                if ($comment) {
                    $comment_id = $comment['id'];
                    $c['id'] = $comment['id'];
                }

                $c['order_id'] = $order_id;
                if (isset($c['status']) && $c['status'] === '') {
                    $c['status'] = NULL;
                }
                // New or update?
                if (!$comment_id) {
                    // override status values
                    $c['status'] = $this->order_model->get_order_comment_status_default_value($order, $sc, $c['status']);
                    $success = $order_comment_model->insert($c, true, true, true, true);
                    $comment_id = $success;
                } else {
                    $success = $order_comment_model->update($c, $comment_id, true, true);
                }

                // Setting default statuses of the order: OrderSupplierComment and OrderCustomerComment
                if ($sc) {
                    $order_comments = $order_comment_model->get_list([
                        'order_id' => $order_id,
                        'type' => $sc['type']
                    ]);
                    $existed_sc_ids = [];
                    foreach ($order_comments as $x) {
                        $existed_sc_ids[] = $x['sc_id'];
                    }

                    // all sys_config available
                    $sc_list = SysConfigModel::get_instance()->get_list(['type' => $sc['type']]);
                    $new_comments = [];
                    $user_id = Auth::current_user_id();
                    $today = time2db_datetime();
                    foreach ($sc_list as $x) {
                        if (!in_array($x['id'], $existed_sc_ids)) {
                            $default_value = $x['value'] === null || $x['value'] === ''? 0: $x['value'];

                            // We hard-coded this exception.
                            if ($order['warehouse'] && $order['category'] != 'FD' &&  $sc['order'] < 500 && $x['order'] < 500 ) {
                                $default_value = 2; // N/A
                            }
                            if ($order['warehouse'] && $order['category'] != 'FD' &&  $sc['order'] < 500 && $x['order'] >= 500 ) {
                                $default_value = 2; // N/A
                            }

                            if (!$order['warehouse'] && $sc['order'] < 500 && $x['order'] >= 500) {
                                $default_value = 2; // N/A
                            }

                            // override default value by "Outside EU" status
                            $default_value = $this->order_model->get_order_comment_status_default_value($order, $x, $default_value);

                            $new_comments[] = [
                                'id' => $order_comment_model->gen_pk(),
                                'order_id' => $order_id,
                                'sc_id' => $x['id'],
                                'status' => $default_value,
                                'created_by' => $user_id,
                                'created_on' => $today,
                                'updated_by' => $user_id,
                                'updated_on' => $today,
                            ];
                        }
                    }
                    if (!empty($sc_list)) {
                        $order_comment_model->insert_batch($new_comments);
                    }
                }
            }
        }
        if ($success) {
            $this->msg = 'Updated successfully.';
            $this->data['row'] = $this->order_model->get_row($order_id);
        } else {
            $this->error = true;
            $this->msg = 'Failed to update comments.';
        }

        $this->return();
    }

    /**
     * Update a comment for invoices column.
     */
    public function update_invoices_comment()
    {
        $this->post_restrict();

        $order_id = get_var('order_id');
        $comment = $_POST['order_comment'] ?? [];
        if (empty($comment)) $this->return(true, 'Comment is required.');

        $order = $this->order_model->get_by_field('order_id', $order_id);
        if (!$order) $this->return(true, 'Order does not exist.');

        $success = true;

        $c['order_id'] = $order_id;
        $c['comment'] = $comment['comment'] ?? NULL;
        $c['sc_id'] = NULL;             // This is important.
        $c['status'] = NULL;            // This is important.

        $order_comment_model =& load_model('OrderCommentModel');
        $where = $this->db->where_by_array([
            'order_id' => $order_id,
            'sc_id' => NULL,
        ]);
        $x = $order_comment_model->get_by_where($where);
        if ($x) {
            $success = $order_comment_model->update($c, $x['id'], true, true);
        } else {
            $success = $order_comment_model->insert($c, true, true, true, true);
        }

        if ($success) {
            $this->msg = 'Updated successfully.';
            $this->data['order_comment'] = $order_comment_model->get_by_where($where);;
        } else {
            $this->error = true;
            $this->msg = 'Failed to update comments.';
        }

        $this->return();
    }
}