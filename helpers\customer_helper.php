<?php

function table_customer_comments($rows)
{
    $users =  SimDB::get_instance()->query_select_dropdown('SELECT id, display_name FROM users', 'id', 'display_name');

    $head = '<tr>';
    $head .= "<th class='text-center'>No</th>";
    $head .= "<th>Comment</th>";
    $head .= "<th>Order</th>";
    $head .= "<th>Status</th>";
    $head .= "<th>Created on</th>";
    $head .= "<th>Created by</th>";
    $head .= "</tr>";

    $ind = 1;
    $body = '';
    if (!empty($rows)) {
        foreach ($rows as $row) {
            $comment = $row['comment'];

            $body .= '<tr>';
            $body .= "<td>" . $ind++ . "</td>";
            $body .= "<td><div class='fw-150'>" . $comment . "</div></td>";
            $body .= "<td><div class='fw-150'>" . $row['order_id'] . "</div></td>";
            $body .= "<td class='text-center'><span class='badge badge-info'>" . $row['status_name'] . "</span></td>";
            $body .= "<td><div class='fw-80 text-center'>" . dt_nice_dmy($row['created_on']) . "</div></td>";
            $body .= "<td><div class='fw-60 text-center c-lightgrey'>" . ($users[$row['created_by'] ?? ''] ?? '') . "</div></td>";
            $body .= '</tr>';
        }
    }

    $html_tpl =  '
        <table id="customer-comments" class="data-table border-0 w-100">
            <thead>%s</thead>
            <tbody>%s</tbody>    
        </table>';

    return sprintf($html_tpl, $head, $body);
}