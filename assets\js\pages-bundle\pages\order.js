(()=>{var e,t={1536:(e,t,r)=>{var n=r(5784);"string"==typeof n&&(n=[[e.id,n,""]]);var o={hmr:!0,transform:undefined,insertInto:void 0};r(3027)(n,o);n.locals&&(e.exports=n.locals)},2303:(e,t,r)=>{"use strict";r.d(t,{MK:()=>a,Nh:()=>s,RI:()=>o,UQ:()=>l,WH:()=>i,ZU:()=>c});var n=r(6540),o="table-order",a={order_id:["Order ID","i",!0],au_master:["AU Master","",!0],customer_id:["Customer","",!0],name:["Name","",!0],category:["Cat.","",!0],warehouse:["WH?","",!1],action_update_info:[n.createElement("i",{className:"oi oi-info m-0",title:"Update order info.","data-placement":"right"}),""],cust_complaint_solved:[n.createElement("i",{className:"oi oi-info m-0",title:"Customer complaint status.","data-placement":"right"}),""],expected_volume:["Volumen geschätzt","",!0],est_loading_date:["Est. L. Date","dt",!0],loading_date:["Loading Date","dt",!0],offer_id:["Offer","",!0],invoices:["Invoices",""],act_invoices:["",""],supplier_comments_statuses:["Supp Statuses",""],customer_comments_statuses:["Customer Statuses",""],supplier_status:[n.createElement("i",{className:"oi oi-info m-0",title:"Supplier statuses and comments","data-placement":"right"}),""],customer_status:[n.createElement("i",{className:"oi oi-info m-0",title:"Customer statuses and comments","data-placement":"right"}),""]},i={order_id:60,au_master:35,name:200,offer_id:60,customer_id:90,customer_comments:200,supplier_comments:200,supplier_comments_statuses:700,customer_comments_statuses:500,action_btn:50,cust_complaint_solved:10,expected_volume:50,est_loading_date:50,est_loading_date2:90,loading_date:50,invoices:120,category:25,warehouse:25,customer_status:25,supplier_status:25,act_invoices:25},l=["order_id","name","offer_id","customer_id","au_master"],s=function(e){return $("tr#tr-".concat(e," td input.customer_id"))},c=function(e){return $("tr#tr-".concat(e," td input.offer_id"))}},4114:(e,t,r)=>{"use strict";r.d(t,{A:()=>E,h:()=>O});var n=r(6540),o=(r(961),r(5848),r(5556)),a=r.n(o),i=(r(6151),r(2303)),l=r(7406),s=r(5184),c=r(6234),u=r(9216),d=(r(8540),r(8239)),m=r(2827);function f(e){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},f(e)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function y(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=f(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v(e){return function(e){if(Array.isArray(e))return S(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw o}}return l}}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){if(e){if("string"==typeof e)return S(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?S(e,t):void 0}}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var E=n.createContext(null);function w(e){var t=e.defaultSearchForm,r=e.defaultOrder,o=e.settings,a=g(n.useState(!0),2),f=a[0],p=a[1],y=g(n.useState(!0),2),h=y[0],S=y[1],w=g(n.useState(!1),2),O=w[0],j=w[1],N=g(n.useState(o),2),A=N[0],x=N[1],P=g(n.useState(r.field),2),k=P[0],R=P[1],F=g(n.useState(r.dir),2),I=F[0],C=F[1],H={page:0,pageCount:0},U=g(n.useState(H),2),T=U[0],D=U[1],L={order_id:"",name:"",customer_id:"",customerName:"",offer_id:"",offerName:""},M=g(n.useState(L),2),Q=(M[0],M[1]),W=g(n.useState([]),2),q=W[0],V=W[1],B=g(n.useState(""),2),K=B[0],z=B[1],G=g(n.useState(t),2),Z=G[0],J=G[1];n.useEffect((function(){ee({with:"form"})}),[]),n.useEffect((function(){O&&$("#"+i.RI).floatThead({autoReflow:!0})}),[f]),n.useEffect((function(){O&&Y(null)}),[k,I]),n.useEffect((function(){O&&Y(null)}),[Z]),n.useEffect((function(){O&&Y(null)}),[T.page]);var X=Z.mode||"status",Y=function(e){ee({})},ee=function(e){e=Object.assign({},Z,e),te(e)},te=function(e){S(!0);var t={pager:T};"order_field"in(t=Object.assign({},t,e))||(t.order_field=k),"order_dir"in t||(t.order_dir=I),App.ajax_post(get_ajax_url("OrderAction","get_list"),t,(function(e){j(!0),S(!1),e.error||re(e.data),p(!1)}),(function(e){j(!0)}))},re=function(e){V(v(e.rows)),D(_.get(e,"pager",H)),z(e.sql),init_tooltip()},ne=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.order_id," / ").concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url("OrderAction","delete"),{id:e.order_id},(function(t){0==t.error&&V(q.filter((function(t){return t.order_id!=e.order_id})))}))}),{title:"Delete entry"})},oe=function(e){return"0"===e?" oi-x red":"1"===e?" oi-circle-check":"2"===e?" oi-ban c-lightgrey-d":null===e||"x"===e?" oi-minus c-lightgrey-d":""},ae=function(e,t){if(!_.isUndefined(t)){var r="!"+e.id,o=_.get(Z,r,"u");return n.createElement("i",{className:"oi "+oe(o)})}var a=Z.commentStatusFilterId||"",i=Z.commentStatusFilterValue||null;return a==e.id?n.createElement("i",{className:"oi position-absolute"+oe(i),style:{top:0,right:5}}):""},ie=function(e,t,r,n){if(_.isUndefined(r)){var o=["u","0",null,"2","1"];J((function(e){var r=b(b({},e),{},{commentStatusFilterId:t});if(e.commentStatusFilterId!=t)r.commentStatusFilterValue="1";else{var n=e.commentStatusFilterValue||null;r.commentStatusFilterValue=App.getNextValue(n,o)}return r}))}else{1==n&&e.preventDefault();var a=n?["x","u","0","2","1"]:["x","u","0","1","2"];J((function(e){var r=b({},e),n="!"+t;if(n in e){var o=e[n]||null;r[n]=App.getNextValue(o,a)}else r[n]="1";return r}))}},le=function(e){switch(e){case"supplier_comments_statuses":return"invoice"==X?null:o.supplierStatuses.map((function(t,r){var o="";return 1==t.ui_border&&(o=n.createElement("th",{key:t.id+"g",className:"gap"},n.createElement("div",null))),n.createElement(n.Fragment,null,o,n.createElement("th",{key:t.id,className:"cs-col"+(RHelper.isSortable(e,i.UQ)?" icon-order-wrap":1==t.ui_border||0==r||500==t.order||1e3==t.order?" sep-l":""),onClick:function(e){return ie(e,t.id)}},n.createElement("div",{key:t.id,className:"tt vt","data-placement":"right",title:t.name,style:{width:20}},t.name),ae(t)))}));case"customer_comments_statuses":return"invoice"==X?null:o.customerStatuses.map((function(t,r){var o="";return 1==t.ui_border&&(o=n.createElement("th",{key:t.id+"g",className:"gap"},n.createElement("div",null))),n.createElement(n.Fragment,null,o,n.createElement("th",{key:t.id,className:"cs-col"+(RHelper.isSortable(e,i.UQ)?" icon-order-wrap":1==t.ui_border||0==r?" sep-l":""),onClick:function(e){return ie(e,t.id)}},n.createElement("div",{key:t.id,className:"tt vt","data-placement":"right",title:t.name,style:{width:20}},t.name),ae(t)))}));case"act_invoices":if("invoice"!=X)return null;break;case"invoices":return"invoice"!=X?null:_.map(Object.values(d.HB),(function(t,r){return n.createElement("th",{key:t},n.createElement("div",{style:{width:RHelper.getColWidth(e,i.WH)}},d.by[t]))}))}return n.createElement("th",{key:e,className:e+(RHelper.isSortable(e,i.UQ)?" icon-order-wrap":""),onClick:function(t){return function(e){RHelper.isSortable(e,i.UQ)&&(e==k?C((function(e){return"desc"==e?"asc":"desc"})):(R(e),C("asc")))}(e)}},n.createElement("div",{className:RHelper.isSortable(e,i.UQ)?"sortable":"",style:{width:_.get(i.WH,e,"")}},n.createElement("span",null,RHelper.getColName(e,i.MK)),RHelper.isSortable(e,i.UQ)&&k==e&&n.createElement("a",{className:"icon-order "+I,href:"#"})))},se=Object.keys(i.MK);return n.createElement(E.Provider,{value:b(b({},A),{},{setStateSettings:x})},n.createElement(l.A,{form:Z,setForm:J,loading:h}),n.createElement("h6",null,q&&q.length>0?"Results (".concat((0,m.w)(T),")"):"No Results",n.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),n.createElement("div",{className:"sql-log-wrap"},n.createElement("pre",null,K)),n.createElement("div",{className:"table-wrap position-relative"},n.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},n.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:Y,disabled:h},"Refresh"),n.createElement("button",{className:"btn btn-sm btn-success",onClick:function(e,t){(0,u.d)(e,t,(function(e,t){!function(e,t){S(!0);var r=b({},e);delete r.customerName,delete r.offerName,App.ajax_post(get_ajax_url("Order","save"),{data:r,isNew:1},(function(e){S(!1),e.error||(V([e.data.row].concat(v(q))),Q(b({},L)),_.isUndefined(t)||t.hide())}),(function(e){S(!1)}))}(e,t)}))},disabled:h},"Create an Order"),n.createElement("span",{className:"ml-5"},h?"Loading ...":"")),n.createElement("table",{id:i.RI,className:"data-table editable border-0",style:{minWidth:500}},n.createElement("thead",null,n.createElement("tr",null,se.map((function(e,t){var r=le(e);if(r)return r})))),n.createElement("tbody",null,n.createElement(s.A,{form:Z,setForm:J,loading:h,commentStatusFilterChange:ie,commentStatusFilter:ae}),q.map((function(e,t){return n.createElement(c.A,{key:e.order_id,isTotal:!1,item:e,cols:se,form:Z,loading:h,handleRowDelete:ne})})))),n.createElement(m.A,{pager:T,onPageChange:function(e){var t=b(b({},T),{},{page:e.selected});D(t)}})))}w.propTypes={defaultSearchForm:a().object.isRequired,defaultOrder:a().object.isRequired,settings:a().object.isRequired};const O=w},4670:(e,t,r)=>{"use strict";var n=r(6540),o=r(961),a=r(4114),i=(r(1536),"undefined"!=typeof ROrderProps?ROrderProps:"undefined"!=typeof App?App.get_params(window.location):{});o.render(n.createElement(a.h,i),document.getElementById("root"))},5784:(e,t,r)=>{(e.exports=r(4765)(!1)).push([e.id,"\r\ntable td {\r\n    word-break: break-word;\r\n}\r\n\r\nth.cs-col {\r\n    vertical-align: bottom;\r\n    padding-top: 20px !important;\r\n    position: relative;\r\n    cursor: pointer;\r\n}\r\n\r\ntd.cs-col {\r\n    text-align: center;\r\n    cursor: pointer;\r\n}\r\n\r\ntd.cs-col i {\r\n    position: relative !important;\r\n    top: initial !important;\r\n    right: initial !important;\r\n}\r\n\r\nth.supplier_status, td.supplier_status {\r\n    border-left: 2px solid #555 !important;\r\n}\r\n\r\ntd.expected_volume div {\r\n    font-size: 0.6rem;\r\n}\r\n\r\ntable .gap {\r\n    border-left: 2px solid #555 !important;\r\n    border-right: 2px solid #555 !important;\r\n    background: #ffffff;\r\n}\r\n\r\ntable .gap > div {\r\n    width: 10px;\r\n    height: 5px;\r\n}\r\n\r\ntable tbody tr:hover > td {\r\n    background: #dae8f9;\r\n}\r\ntable tbody tr:hover > td {\r\n    border-top-color: #a0baf9;\r\n    border-bottom-color: #a0baf9;\r\n}\r\n\r\ntable tbody tr:hover > td .oi.oi-ban {\r\n    color: #d2cdcd;\r\n}\r\n\r\n.light-grey-status {\r\n    background: #f5f3f3 !important;\r\n}\r\n\r\ntable tbody tr:hover > td.light-grey-status {\r\n    /*background: #eef2f7 !important;*/\r\n}\r\n\r\n.light-grey-status .oi.oi-ban {\r\n    color: #d2cdcd;\r\n}\r\n\r\n.light-yellow-wh td.yellow-wh{\r\n    background: lightyellow !important;\r\n}",""])},8239:(e,t,r)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,{AI:()=>l,HB:()=>a,by:()=>i,qh:()=>s});var a={FINAL:"final",PROFORMA:"proforma",FREIGHT:"freight",OTHER:"other",PALLETS:"pallets"},i=o(o(o(o(o({},a.FINAL,"Final"),a.PROFORMA,"Proforma"),a.FREIGHT,"Freight"),a.OTHER,"Other"),a.PALLETS,"Pallets"),l={PAID:"paid",UNPAID:"unpaid"},s=o(o({},l.PAID,"Paid"),l.UNPAID,"Unpaid")}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=t,e=[],n.O=(t,r,o,a)=>{if(!r){var i=1/0;for(u=0;u<e.length;u++){for(var[r,o,a]=e[u],l=!0,s=0;s<r.length;s++)(!1&a||i>=a)&&Object.keys(n.O).every((e=>n.O[e](r[s])))?r.splice(s--,1):(l=!1,a<i&&(i=a));if(l){e.splice(u--,1);var c=o();void 0!==c&&(t=c)}}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[r,o,a]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.j=864,(()=>{var e={864:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var o,a,[i,l,s]=r,c=0;if(i.some((t=>0!==e[t]))){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(s)var u=s(n)}for(t&&t(r);c<i.length;c++)a=i[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(u)},r=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0;var o=n.O(void 0,[96],(()=>n(4670)));o=n.O(o)})();