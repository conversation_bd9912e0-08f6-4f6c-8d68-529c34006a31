<?php
require_once '_admin_includes.php';
require_once APP_PATH . 'models' . DS . 'UserModel.php';
$user_model =& UserModel::get_instance();

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'delete':
        $uid = $_POST['id'] ?? '';
        if ($uid) {
            if ($user_model->delete($uid))
                $msg->success("Deleted user successfully.", $ADMIN_URL . "/users.php");
            else
                $msg->error('Failed to delete a user. Please try again.');
        } else {
            $msg->error("Invalid request.", "$ADMIN_URL/users.php");
        }
        break;
    case 'copy_perm':
        $from_user = $_POST['from_user'];
        $to_user = $_POST['to_user'];

        $users_dropdown = $user_model->get_users_dropdown(['no_admin' => 1]);
        $user_ids = array_keys($users_dropdown);
        $is_valid = true;
        if (!in_array($from_user, $user_ids) || !in_array($to_user, $user_ids)) {
            $msg->error('Invalid users. Please choose correct one.');
            $is_valid = false;
        }
        if ($is_valid && $from_user == $to_user) {
            $msg->error('Invalid users. Please choose correct one.');
            $is_valid = false;
        }

        if ($is_valid) {
            $result = $user_model->copy_user_permissions($from_user, $to_user);
            if ($result) {
                $msg->success("Copied permissions from user '{$users_dropdown[$from_user]}' to user '{$users_dropdown[$to_user]}' successfully.");
            } else {
                $msg->error("Failed to copy permissions from user '{$users_dropdown[$from_user]}' to user '{$users_dropdown[$to_user]}'.");
            }
        }

        break;

    case 'del_perm':
        $from_user = $_POST['del_from_user'];

        $users_dropdown = $user_model->get_users_dropdown(['no_admin' => 1]);
        $user_ids = array_keys($users_dropdown);
        $is_valid = true;
        if (!in_array($from_user, $user_ids)) {
            $msg->error('Invalid user. Please choose correct one.');
            $is_valid = false;
        }

        if ($is_valid) {
            $result = $user_model->delete_user_permissions($from_user);
            if ($result) {
                $msg->success("Deleted permissions of user '{$users_dropdown[$from_user]}' successfully.");
            } else {
                $msg->error("Failed to delete permissions of user '{$users_dropdown[$from_user]}'.");
            }
        }

        break;
}

$title = "Users management";
require_once APP_PATH . 'layout/header.php';

$sql = "
    SELECT 
        u.*, 
        GROUP_CONCAT( DISTINCT up.permission_id
            ORDER BY up.permission_id ASC
            SEPARATOR '|') as perms_str 
    FROM users u 
    LEFT JOIN sys_user_permissions up ON u.id = up.user_id
    GROUP BY u.id   
";
$users = $db->query_select($sql);
$perms = $db->query_select_manualkey("SELECT * FROM sys_permissions", 'id');
$users_dropdown = $user_model->get_users_dropdown(['no_admin' => 1]);
$users_dropdown += ['' => ''];
?>
    <form id="admin-form" class="" action="<?php echo $_SERVER['PHP_SELF'] ?>" method="post">
        <div class="card bg-transparent border-0">
            <div class="card-header border-0 bg-transparent pb-0 pt-0">
                <div class="float-right">
                    <a href="<?php echo $ADMIN_URL . "/user_add.php" ?>" class="btn btn-info btn-sm">Add</a>
                </div>
            </div>
            <div class="card-body">
                <table class="table data-table">
                    <thead class="thead-light">
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">User ID</th>
                        <th class="text-center">Display Name</th>
                        <th class="text-center">Permissions</th>
                        <th class="text-center">Admin?</th>
                        <th class="text-center"> -</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $ind = 1;
                    foreach ($users as $u) { ?>
                        <tr>
                            <td class="text-center"><?php echo $ind++; ?></td>
                            <td class="text-center"><?php echo $u['username'] ?></td>
                            <td class="text-center"><?php echo $u['display_name'] ?></td>
                            <td class="text-center"><?php
                                $u_perms = $u['perms_str'] ? explode('|', $u['perms_str']) : array();
                                foreach ($u_perms as $pid) {
                                    if (!isset($perms[$pid])) continue;
                                    echo "<span class='badge badge-success mr-2 py-2' title='{$perms[$pid]['description']}'>" . $perms[$pid]['name'] . "</span>";
                                }
                                ?></td>
                            <td class="text-center"><span class="badge badge-<?php echo $u['is_superuser'] ? "success" : "light" ?>"><?php echo $u['is_superuser'] ? "Yes" : "No" ?></span></td>
                            <td class="text-center">
                                <a href="<?php echo $ADMIN_URL ?>/user_edit.php?id=<?php echo $u['id'] ?>" class="btn btn-outline-secondary btn-sm" role="button">Edit</a>
                                <a href="<?php echo $ADMIN_URL ?>/user_permission.php?id=<?php echo $u['id'] ?>" class="btn btn-outline-secondary btn-sm" role="button">Edit Permissions</a>
                                <a href="#"
                                   data-id="<?php echo $u['id'] ?>"
                                   data-toggle="modal"
                                   data-target="#confirm-delete"
                                   data-backdrop="static"
                                   data-keyboard="false"
                                   class="btn btn-outline-danger btn-sm" role="button"
                                >Delete</a>
                            </td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card bg-transparent border-0">
            <div class="card-header border-0 bg-transparent pb-0 pt-0">

            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="col-auto">
                        <label for="from_user" class="col-form-label-sm">Copy permissions:</label>
                        <?php
                        echo form_dropdown('from_user', $users_dropdown, $_POST['from_user'] ?? '', ' id="from_user" class="form-control form-control-sm w-auto d-inline-block"');
                        echo "--->";
                        echo form_dropdown('to_user', $users_dropdown, $_POST['to_user'] ?? '', ' id="to_user" class="form-control form-control-sm w-auto d-inline-block"');
                        ?>
                        <button type="button" name="btn-copy-perm" id="btn-copy-perm" class="btn btn-sm btn-info">Copy</button>
                    </div>
                    <div class="col-auto ml-3">
                        <label for="del_from_user" class="col-form-label-sm">Remove permissions:</label>
                        <?php
                        echo form_dropdown('del_from_user', $users_dropdown, $_POST['del_from_user'] ?? '', ' id="del_from_user" class="form-control form-control-sm w-auto d-inline-block"');
                        ?>
                        <button type="button" name="btn-del-perm" id="btn-del-perm" class="btn btn-sm btn-outline-danger">Delete</button>
                    </div>
                </div>

            </div>
            </div>
        </div>

        <input type="hidden" name="action" id="action"/>
        <input type="hidden" name="id" id="id"/>
    </form>

    <div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    Delete user?
                </div>
                <div class="modal-body">
                    Are you sure you want to delete the selected user?
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-danger btn-ok">Delete</a>
                    <button type="button" class="btn btn-default btn-cancel" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            $('#confirm-delete').on('click', '.btn-ok', function (e) {
                var $modalDiv = $(e.delegateTarget);
                var id = $(this).data('id');
                $('#id').val(id);
                $('#action').val('delete');
                $("#admin-form").submit();
            });

            $('#confirm-delete').on('show.bs.modal', function (e) {
                var data = $(e.relatedTarget).data();
                //console.log(data);
                //$('.title', this).text(data.recordTitle);
                $('.btn-ok', this).data('id', data.id);
            });

            $('#btn-copy-perm').click(function (e) {
                e.preventDefault();
                const from_user = $('#from_user').val();
                const to_user = $('#to_user').val();

                if (from_user.length < 1 || to_user.length < 1) {
                    App.info("Please select users.");
                    return;
                }
                if (from_user == to_user) {
                    App.info("Please select different users.");
                    return;
                }

                const from_user_name = $('#from_user option[value="' + from_user + '"]').text();
                const to_user_name = $('#to_user option[value="' + to_user + '"]').text();

                bs4pop.confirm(`Are you sure you want to copy '${from_user_name}' permissions to '${to_user_name}'?`, (sure) => {
                    if (sure) {
                        $('#action').val('copy_perm');
                        $('#admin-form').submit();
                    }
                }, {title: 'Copy Permissions'});
            });

            $('#btn-del-perm').click(function (e) {
                e.preventDefault();
                const from_user = $('#del_from_user').val();

                if (from_user.length < 1) {
                    App.info("Please select user.");
                    return;
                }

                const from_user_name = $('#del_from_user option[value="' + from_user + '"]').text();

                bs4pop.confirm(`Are you sure you want to delete '${from_user_name}' permissions?`, (sure) => {
                    if (sure) {
                        $('#action').val('del_perm');
                        $('#admin-form').submit();
                    }
                }, {title: 'Delete Permissions'});
            });
        });
    </script>
<?php

require_once APP_PATH . 'layout/footer.php';
