<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="robots" content="noindex,nofollow"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo $BASE_URL ?>/assets/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo $BASE_URL ?>/assets/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo $BASE_URL ?>/assets/favicon/favicon-16x16.png">
    <link rel="manifest" href="<?php echo $BASE_URL ?>/assets/favicon/site.webmanifest">

    <title><?php echo isset($title) ? strip_tags($title) : 'WHCOrg' ?></title>

    <?php echo include_css('/bootstrap-4.4.1/css/bootstrap.min.css', '/assets/plugins') ?>
    <?php echo include_css('/air-datepicker/css/datepicker.min.css', '/assets/plugins') ?>
    <?php echo include_css('/jquery-toast-plugin-master/jquery.toast.min.css', '/assets/plugins') ?>
    <?php echo include_css('/jquery-waitme/waitMe.min.css', '/assets/plugins') ?>
    <?php echo include_css('/bootstrap-iconic/css/open-iconic-bootstrap.min.css', '/assets/plugins') ?>
    <?php echo include_css('/bs4-pop/bs4.pop.css', '/assets/plugins') ?>

    <?php echo include_css('/style.css') ?>
    <?php echo include_css('/print.css', '/assets/css', 'print') ?>

    <?php echo include_js('jquery-3.4.1.min.js', '/assets/plugins') ?>
    <?php echo include_js('air-datepicker/js/datepicker.min.js', '/assets/plugins') ?>
    <?php echo include_js('air-datepicker/js/i18n/datepicker.de.js', '/assets/plugins') ?>
    <?php echo include_js('bootstrap-4.4.1/js/popper.min.js', '/assets/plugins') ?>
    <?php echo include_js('bootstrap-4.4.1/js/bootstrap.min.js', '/assets/plugins') ?>
    <?php echo include_js('jquery-toast-plugin-master/jquery.toast.min.js', '/assets/plugins') ?>
    <?php echo include_js('jquery-waitme/waitMe.min.js', '/assets/plugins') ?>
    <?php echo include_js('mkoryak-floatThead/dist/jquery.floatThead.min.js', '/assets/plugins') ?>
    <?php echo include_js('jquery-autocomplete/jquery.autocomplete.min.js', '/assets/plugins') ?>
    <?php echo include_js('bs4-pop/bs4.pop.js', '/assets/plugins') ?>
    <?php echo include_js('moment-2.27/moment.min.js', '/assets/plugins') ?>
    <?php echo include_js('moment-2.27/moment-timezone.min.js', '/assets/plugins') ?>
    <?php echo include_js('moment-2.27/locale/de.min.js', '/assets/plugins') ?>
    <?php echo include_js('lodash.min.js', '/assets/plugins') ?>
    <?php echo include_js('global.js') ?>
    <script>var base_url = '<?php echo $BASE_URL ?>';</script>
    <script>var page = '<?php echo basename(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '.php') ?>';</script>
    <script>tzServerOffset = <?php echo date('Z') / 3600 ?>;</script>
    <script>userPerms = <?php echo json_encode(Auth::get_user_permissions()); ?>;</script>
</head>
<body>
<?php
if (IS_ADMIN_SITE) {
    require_once 'nav_admin.php';
} else {
    require_once 'nav.php';
}

echo "<div class='container-fluid pt-3'>";

if (isset($title) && $title) {
    if (!isset($hide_page_title) || !$hide_page_title)
        echo "<h1>$title</h1>";
}

if (isset($msg) && $msg->hasMessages()) {
    echo "<div id='sys-msg-wrap'>";
    $msg->display();
    echo "</div>";
}


