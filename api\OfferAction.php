<?php

/**
 * Class OfferAction
 */
class OfferAction extends BaseAction
{
    /**
     * @var OfferModel
     */
    private $offer_model = null;

    function __construct()
    {
        parent::__construct();

        $this->offer_model =& load_model('OfferModel');
    }

    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $offer_sid = $row['offer_sid'] ?? '';
        $offer = $row['offer'] ?? '';

        if (empty($offer_sid) || empty($offer)) {
            $this->return(true, "Please fill Offer ID & Offer.");
        }

        if (isset($row['we']) && !$row['we']) {
            unset($row['we']);
        }

        $id = $row['id'] ?? null;
        if (empty($id)) {
            // validation
            $x = $this->offer_model->get_by_field('offer_sid', $offer_sid, 1);
            if ($x) {
                $this->return(true, "This offer '" . $offer_sid . "' already exists!");
            }
            $success = false;
            unset($row['id']);
            $id = $this->offer_model->insert($row, true);
            if ($id) {
                $success = true;
            }
        } else {
            // validation
            $where = sprintf(" id != %s AND offer_sid = %s", $this->db->safe_value($id), $this->db->safe_value($offer_sid));
            $x = $this->offer_model->get_by_where($where, 1);
            if ($x) {
                $this->return(true, "This offer ID'" . $offer_sid . "' already exists!");
            }
            $success = $this->offer_model->update($row, $id, true);
        }
        $this->data['row'] = $this->offer_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    /**
     * Change status
     */
    public function change_status()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $status = $row['status'] ?? null;

        $id = $row['id'] ?? null;
        if ($status === null || $id === null) {
            $this->return(true, "Invalid request.");
        }

        $x = $this->offer_model->get($id);
        if (!$x)
            $this->return(true, "Offer '" . $id . "' does not exists!");
        unset($row['id']);
        $success = $this->offer_model->update($row, $id, true);
        $this->data['row'] = $this->offer_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    /**
     * Public accessible
     *
     */
    public function change_status_by_sid()
    {
        $row = [
            'id' => $_GET['id'] ?? null,
            'status' => $_GET['status'] ?? null,
        ];
        $status = $row['status'] ?? null;
        $id = $row['id'] ?? null;

        if ($status === null || $id === null) {
            $this->return(true, "Invalid request. sid & stats required.");
        }

        $x = $this->offer_model->get_by_field('offer_sid', $id);
        if (!$x)
            $this->return(true, "Offer '" . $id . "' does not exists!");
        unset($row['sid']);

        $id = $x['id'];
        $success = $this->offer_model->update($row, $id, true);
        $this->data['row'] = $this->offer_model->get_row($id);
        if ($success) {
            $this->error = false;
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();

        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->offer_model->get($id, 1);
        if (!empty($row)) {
            $this->offer_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }

        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        // Get totals
        $this->offer_model->calc_count(TRUE);
        $rows_count = $this->offer_model->get_list($_POST);

        // Get limited rows
        $this->offer_model->set_pager($this->get_pagination($rows_count));
        $this->offer_model->calc_count(FALSE);
        $rows = $this->offer_model->get_list($_POST);

        $this->data = [
            'rows' => $rows,
            'pager' => $this->pagination->getPagerResponse(),
            'sql' => $this->db->show_query_history(false)
        ];
        $this->data['sql'] = $this->db->show_query_history(false);

        $this->return();
    }


    /**
     *
     * Get offers list for auto completion list.
     */
    public function get_ac_offers_old()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';

        $where .= $this->db->where_like("CONCAT(IFNULL(`offer_sid`, ''), ' - ', offer)", $_POST['keyword'] ?? '', true);
        $where .= $this->db->where_equal('supplier_id', $_POST['supplier_id'] ?? '');

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                id AS data,
                CONCAT(IFNULL(`offer_sid`, ''), ' - ', offer) AS value,
                offer 
            FROM offers
            WHERE TRUE $where
            $limit_str
        ";
        $this->data = $this->db->query_select($sql);
        $this->return();
    }

    /**
     *
     * Get offers list for auto completion list.
     */
    public function get_ac_offers()
    {
        $this->post_restrict();
        $this->msg = '';
        $where = '';

        $where .= $this->db->where_like("CONCAT(IFNULL(off.`offer_sid`, ''), ' - ', off.offer)", $_POST['keyword'] ?? '', true);
        $where .= $this->db->where_equal('off.supplier_id', $_POST['supplier_id'] ?? '');

        $limit_str = 'LIMIT ' . 50;

        $sql = "
            SELECT 
                DISTINCT 
                off.id AS data,
                CONCAT(IFNULL(off.`offer_sid`, ''), ' - ', off.offer) AS value,
                off.offer 
            FROM offers off            
            WHERE TRUE $where
            $limit_str
        ";
        $this->data = $this->db->query_select($sql);
        $this->return();
    }

    /**
     *
     * Get offers list for auto completion list.
     */
    public function view_offer_comments()
    {
        $this->post_restrict();
        $this->msg = '';

        $offer_id = $_POST['offer_id'] ?? '';
        if (empty($offer_id)) {
            $this->return(true, "Invalid request!");
        }

        $s = $this->offer_model->get($offer_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Supplier does not exist!");
        }

        $params = ['offer_id' => $offer_id, 'off_status_type' => $_POST['off_status_type'] ?? ''];
        $comment_model =& Loader::get_instance()->load_model('OfferCommentModel');
        $rows = $comment_model->get_list($params);

        Loader::get_instance()->load_helper('offer_helper');
        $this->data['html'] = table_offer_comments($rows);

        $this->return();
    }

    /**
     * Important. This is public accessible.
     * @param $offer_sid    int
     * @param $offer        string
     */
    public function create_new($offer_sid, $offer)
    {
        if (empty($offer_sid) || empty($offer)) {
            $this->return(true, "Please specify 'id' and 'name' parameters in the request. e.g. /newoffer.php?id=XXX&name=YYYY");
        }

        $row = compact('offer_sid', 'offer');
        // validation
        $x = $this->offer_model->get_by_field('offer_sid', $offer_sid, 1);
        if ($x) {
            $this->return(true, "The offer '" . $offer_sid . "' already exists!");
        }
        $success = false;
        if (!isset($row['warehouse'])) $row['warehouse'] = 0;
        $id = $this->offer_model->insert($row, true);
        if ($id) {
            $success = true;
        }
        $this->data['row'] = $this->offer_model->get_row($id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    /**
     * This is public accessible
     */
    public function create()
    {
        $this->create_new($_GET['id'] ?? '', $_GET['name'] ?? '');
    }

    public function update_info()
    {
        $this->post_restrict();

        $offer_id = get_var('offer_id');
        $comment = get_var('comment');
        $row = compact('offer_id', 'comment');

        $old = $this->offer_model->get_by_field('offer_id', $offer_id, 'id', BaseModel::TBL_OFFER_INFO);
        if ($old) {
            $row['id'] = $old['id'];
            $this->error = !$this->db->update(BaseModel::TBL_OFFER_INFO, $row, $this->db->where_equal('id', $row['id'], ''));
        } else {
            $row['id'] = $this->db->gen_pk(BaseModel::TBL_OFFER_INFO);
            $this->error = !$this->db->insert(BaseModel::TBL_OFFER_INFO, $row);
        }
        if ($this->error) $this->msg = 'Failed to update information.';
        $this->data['row'] = $row;
        $this->return();
    }

    /**
     * Save an offer comment.
     */
    public function save_comment()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $offer_id = $row['offer_id'] ?? '';
        if (empty($offer_id)) {
            $this->return(true, "Invalid request!");
        }
        if (isset($row['customer_order']) && !$row['customer_order']) {
            $row['customer_order'] = NULL;
        }

        $s = $this->offer_model->get($offer_id, 1);
        if (empty($s)) {
            $this->return(true, "Invalid request! Supplier does not exist!");
        }

        $comment_model =& Loader::get_instance()->load_model('OfferCommentModel');
        $id = $row['id'] ?? null;
        if (empty($id)) {
            $success = false;
            unset($row['id']);
            $id = $comment_model->insert($row, true, true, true, true);
            if ($id) {
                $success = true;
            }
        } else {
            $success = $comment_model->update($row, $id, true, true);
        }
        $this->data['row'] = $this->offer_model->get_row($offer_id);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }
}