{"name": "WHCOrg", "version": "1.0.0", "description": "Since: 2020-09-02", "main": "index.js", "directories": {"lib": "lib"}, "dependencies": {"@types/react": "^16.9.50", "babel-loader": "^8.1.0", "lodash": "^4.17.20", "react-paginate": "^6.5.0", "styled-components": "^5.2.0", "webpack-dev-server": "^3.11.0"}, "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.1.0", "babel-jest": "^29.7.0", "babel-minify": "^0.5.1", "babel-preset-minify": "^0.5.1", "babel-preset-react-app": "^3.1.2", "css-loader": "^1.0.0", "eslint": "^7.11.0", "eslint-plugin-react": "^7.21.4", "eslint-plugin-react-hooks": "^4.1.2", "eslint-watch": "^7.0.0", "mini-css-extract-plugin": "^0.4.3", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss-loader": "^3.0.0", "react": "^16.13.1", "react-dom": "^16.13.1", "react-table": "^7.5.0", "regenerator-runtime": "^0.14.1", "sass-loader": "^7.1.0", "style-loader": "^0.23.0", "terser-webpack-plugin": "^4.2.2", "uglifyjs-webpack-plugin": "^2.0.1", "url-loader": "^1.1.1", "webpack": "^5.0.0-rc.3", "webpack-cli": "^3.3.12"}, "scripts": {"dev": "webpack --watch --mode development --devtool inline-source-map", "dev2": "webpack-dev-server  --mode development --devtool inline-source-map", "watch": "webpack --watch --mode production", "watch-dev": "webpack --watch --mode development", "build": "webpack --mode production"}, "repository": {"type": "git", "url": "git+https://github.com/"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/"}, "homepage": "https://github.com/"}