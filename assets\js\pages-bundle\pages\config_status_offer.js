(()=>{"use strict";var e,t={7849:(e,t,r)=>{var n=r(6540),a=r(961),o=r(9648),i=r(6151),l=r(18),c=[{accessor:"code",width:100,editable:!1},{accessor:"name",width:150,editable:!0},{accessor:"order",name:"Order",value_type:"int",width:100,editable:!0},{accessor:"sc_customer_order",name:"Display OrderNo?",width:100,value_type:"select",editable:!0},{accessor:"sc_customer_order_required",name:"Require OrderNo?",value_type:"select",width:100,editable:!0},{accessor:"sc_default_position",name:"Position",width:100,editable:!0},{accessor:"link_to_supplier",name:"Link to Supplier?",width:100,editable:!0},{accessor:"off_status_type",name:"Column",width:90,editable:!0}];function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,l=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}const f=function(e){var t=e.loading,r=e.form,a=e.searchData,o=u(n.useState(!1),2),i=(o[0],o[1]),l=u(n.useState(_.get(r,"type",null)),2),c=l[0],s=(l[1],u(n.useState(_.get(r,"name",null)),2)),f=s[0],m=s[1];(0,n.useEffect)((function(){i(!0)}),[]),(0,n.useEffect)((function(){d(null)}),[c]);var d=function(e){a({type:c,name:f})};return n.createElement("div",{className:"card border-0 bg-transparent"},n.createElement("div",{className:"card-body p-0 pt-1"},n.createElement("div",{className:"form-row"},n.createElement("div",{className:"col-auto"},n.createElement("label",{className:"mr-1",htmlFor:"name"},"Name:"),n.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"name",id:"name",value:f,onChange:function(e){return m(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&d(e)}})),n.createElement("div",{className:"col-auto"},n.createElement("button",{className:"btn btn-sm btn-info",onClick:d,disabled:t},"Search")))))};function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function d(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=m(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=m(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==m(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p="internal",b="customer",y=d(d(d({},"supplier","Supplier"),p,"Internal"),b,"Customer"),v=[{id:"",name:""}].concat(_.map(Object.keys(y),(function(e,t){return{id:e,name:y[e]}})));function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function E(e){return function(e){if(Array.isArray(e))return j(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||N(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach((function(t){S(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function S(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,o,i,l=[],c=!0,u=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw a}}return l}}(e,t)||N(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){if(e){if("string"==typeof e)return j(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?j(e,t):void 0}}function j(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var C=function(e){var t=O(n.useState(e.cellData),2),r=t[0],a=t[1],c=e.isEditing,u=r.colName,s=r.value;n.useEffect((function(){a(e.cellData)}),[e]);var f=function(t){var r=t.target.value;a({colName:u,value:r}),e.onCellChange(u,r)},m=e.colDef,d=(o.Pw(m),"form-control form-control-sm"+(o.kf(m)?" text-right":"")+" "+u),p="",b=u,g=o.Yl();if(c&&o.Xl(m)){var E=[];switch(u){case"value_type":E=[{id:l.yE.TEXT,name:l.yE.TEXT.ucfirst()},{id:l.yE.NUMERIC,name:l.yE.NUMERIC.ucfirst()}],p=i.A.dropdown("value_type",E,s||l.yE.TEXT,f);break;case"display_type":E=[{id:"",name:""},{id:l.N9.RADIO,name:l.N9.RADIO.ucfirst()},{id:l.N9.SELECT,name:l.N9.SELECT.ucfirst()},{id:l.N9.CHECKBOX,name:l.N9.CHECKBOX.ucfirst()}],p=i.A.dropdown("display_type",E,s||"",f);break;case"sc_customer_order":case"sc_customer_order_required":case"link_to_supplier":var h="1"==s?1:0;p=n.createElement("select",{className:d+"",value:h,onChange:f},n.createElement("option",{defaultValue:h,value:1},"Yes"),n.createElement("option",{defaultValue:h,value:0},"No"));break;case"off_status_type":p=i.A.dropdown("off_status_type",v,s||"",f);break;default:p=n.createElement("input",{className:d+"",type:"text",disabled:e.loading,value:s||"",onChange:f,onInput:f})}}else switch(u){case"type":p=_.get(l.Rc,s,"");break;case"display_type":case"value_type":p=s?s.ucfirst():"";case"sc_customer_order":case"sc_customer_order_required":case"link_to_supplier":p=1==s?"Yes":"No";break;case"off_status_type":p=_.get(y,s,"");break;default:p=s||""}return n.createElement("td",null,n.createElement("div",{className:b,style:{width:o.RG(m),textAlign:g}},p))},A=function(e){var t=O(n.useState(!1),2),r=t[0],a=t[1],o=O(n.useState(e.item),2),i=o[0],u=o[1],s=O(n.useState(!1),2),f=s[0],m=s[1];n.useEffect((function(){u(e.item)}),[e.item]);var d=function(e,t){var r=w(w({},i),{},S({},e,t));u(r)};return n.createElement("tr",{id:"tr-"+i.id},c.map((function(e,t){return n.createElement(C,{key:t,cellData:{colName:e.accessor,value:_.get(i,e.accessor)},colDef:e,isEditing:f,loading:r,onCellChange:d})})),n.createElement("td",{className:"text-center"},n.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){if(e.preventDefault(),f){a(!0);var t={id:i.id};c.forEach((function(e){e.accessor in i&&(t[e.accessor]=i[e.accessor])})),t.type=l.$S.OFFER_COMMENT_STATUS,App.ajax_post(get_ajax_url("ConfigAction","save"),{isNew:0,data:t},(function(e){m(!f),a(!1),u(e.data)}),(function(e){a(!1)}))}else m(!f)},disabled:r},f?"Save":"Edit"),n.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(i)},disabled:r},"Delete")))},k=function(e){var t=O(n.useState(!1),2),r=t[0],a=t[1],o=O(n.useState(e.item),2),i=o[0],l=o[1];n.useEffect((function(){}),[i]),n.useEffect((function(){l(e.item)}),[e.item]),n.useEffect((function(){a(e.loading)}),[e.loading]);var u=function(e,t){l(w(w({},i),{},S({},e,t)))};return n.createElement("tr",{id:"tr-"+i.id},c.map((function(e,t){return n.createElement(C,{key:t,loading:r,cellData:{colName:e.accessor,value:_.get(i,e.accessor)},colDef:e,isEditing:"created_on"!=e.accessor,onCellChange:u})})),n.createElement("td",{className:"text-center"},n.createElement("div",{style:{width:100}},n.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:r,onClick:function(){var t=w({},i);e.handleRowCreate(t),l(t)}},"Create"))))};const T=function(e){var t=e.initialFormStates,r={id:0,name:"",order:""},a=O(n.useState(!0),2),i=a[0],u=a[1],s=O(n.useState(!0),2),m=s[0],d=s[1],p=O(n.useState(!1),2),b=(p[0],p[1]),y=O(n.useState(r),2),v=y[0],g=y[1],h=O(n.useState([]),2),S=h[0],N=h[1],j=O(n.useState(""),2),C=j[0],T=j[1],D=O(n.useState(t),2),x=D[0],P=D[1];n.useEffect((function(){I(w(w({},x),{},{with:"form"}))}),[]),n.useEffect((function(){}),[S]),n.useEffect((function(){$("#table-config-status").floatThead({autoReflow:!0})}),[i]);var I=function(e){var t=w(w({},e),{},{type:l.$S.OFFER_COMMENT_STATUS});App.ajax_post(get_ajax_url("ConfigAction","get_list"),t,(function(e){b(!0),d(!1),R(e.data),u(!1)}),(function(e){b(!0)}))},R=function(e){N(E(e.rows)),T(e.sql),init_tooltip()},F=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"ConfigAction",action:"delete",id:e.id},(function(t){0==t.error&&N(S.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete entry"})};return n.createElement(n.Fragment,null,n.createElement(f,{form:x,setForm:P,loading:m,setLoading:d,searchData:function(e){d(!0),I(e)}}),n.createElement("h4",null,S?"Results (".concat(S.length," records)"):"No Results",n.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),n.createElement("div",{className:"sql-log-wrap"},n.createElement("pre",null,C)),n.createElement("div",{className:"table-wrap"},n.createElement("table",{className:"data-table editable border-0",id:"table-config-status",style:{minWidth:500}},n.createElement("thead",null,n.createElement("tr",null,c.map((function(e,t){return n.createElement("th",{key:e.accessor},o.mG(e),_.findIndex(o.Y$(e),["order"])>=0&&n.createElement("i",{className:"oi oi-info",title:"Display order.","data-placement":"right"}),_.findIndex(o.Y$(e),[])>=0&&n.createElement("i",{className:"oi oi-info",title:"Please set 'Default' if this status is a default status.","data-placement":"right"}))})),n.createElement("th",null))),n.createElement("tbody",null,n.createElement(k,{key:"new-row",item:v,loading:m,handleRowCreate:function(e){d(!0),App.ajax_post(get_ajax_url("ConfigAction","save"),{data:w(w({},e),{},{type:l.$S.OFFER_COMMENT_STATUS})},(function(e){d(!1),e.error||(N([e.data].concat(E(S))),g(w({},r)))}),(function(e){d(!1)}))}}),S.map((function(e,t){return n.createElement(A,{key:e.id,isTotal:!1,item:e,loading:m,handleRowDelete:F})}))))))};var D="undefined"!=typeof ConfigStatusOfferProps?ConfigStatusOfferProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(n.createElement(T,D),document.getElementById("root"))}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.m=t,e=[],n.O=(t,r,a,o)=>{if(!r){var i=1/0;for(s=0;s<e.length;s++){for(var[r,a,o]=e[s],l=!0,c=0;c<r.length;c++)(!1&o||i>=o)&&Object.keys(n.O).every((e=>n.O[e](r[c])))?r.splice(c--,1):(l=!1,o<i&&(i=o));if(l){e.splice(s--,1);var u=a();void 0!==u&&(t=u)}}return t}o=o||0;for(var s=e.length;s>0&&e[s-1][2]>o;s--)e[s]=e[s-1];e[s]=[r,a,o]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.j=328,(()=>{var e={328:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var a,o,[i,l,c]=r,u=0;if(i.some((t=>0!==e[t]))){for(a in l)n.o(l,a)&&(n.m[a]=l[a]);if(c)var s=c(n)}for(t&&t(r);u<i.length;u++)o=i[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(s)},r=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0;var a=n.O(void 0,[96],(()=>n(7849)));a=n.O(a)})();