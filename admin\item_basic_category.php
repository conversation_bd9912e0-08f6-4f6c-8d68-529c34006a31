<?php
/**
 * Admin - Item basic categories management.
 *
 * <AUTHOR>
 * @since 2020.09.21
 *
 */
require_once '_admin_includes.php';

// Render header
// -----------------------------------------------
$title = 'Item Basic Categories';
require_once APP_PATH . 'layout/header.php';

echo "<div id='root'></div>";

echo include_reactjs();
echo include_js('item_basic_category.js', '/assets/js/pages-bundle/pages/admin');
?>
    <script>
        const props = {
            initialFormStates: {
                name: '<?php echo $_REQUEST['name'] ?? '' ?>',
            }
        };
        ReactDOM.render(
            React.createElement(ItemBasicCategory, props),
            document.querySelector('#root')
        );
    </script>
<?php
require_once APP_PATH . 'layout/footer.php';
