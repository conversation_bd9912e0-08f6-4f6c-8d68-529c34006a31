<?php

/**
 * Class OfferAction
 */
class SuppSupplierAction extends BaseAction
{
    /**
     * @var OfferModel
     */
    private ?SuppSupplierModel $supp_supplier_model = null;

    function __construct()
    {
        parent::__construct();

        $this->supp_supplier_model =& load_model('SuppSupplierModel');
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        // Get totals
        $this->supp_supplier_model->calc_count(TRUE);
        $rows_count = $this->supp_supplier_model->get_list($_POST);

        // Get limited rows
        $this->supp_supplier_model->set_pager($this->get_pagination($rows_count));
        $this->supp_supplier_model->calc_count(FALSE);
        $rows = $this->supp_supplier_model->get_list($_POST);

        $this->data = [
            'rows' => $rows,
            'pager' => $this->pagination->getPagerResponse(),
            'sql' => $this->db->show_query_history(false)
        ];
        $this->data['sql'] = $this->db->show_query_history(false);

        $this->return();
    }


    /**
     *
     * Get suppliers list for auto completion list.
     */
    public function get_ac_suppliers()
    {
        $this->post_restrict();
        $this->msg = '';

        $_POST['select_with'] = ', supplier.id AS data, supplier.name AS value';
        
        $this->data = $this->supp_supplier_model->get_list($_POST);
        $this->return();
    }

}