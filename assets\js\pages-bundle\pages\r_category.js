(()=>{"use strict";var e,t={5474:(e,t,n)=>{var r=n(6540),a=n(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||f(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var m={category:["Category","",!0]},b=function(e){var t=s(r.useState(e.cellData),2),n=t[0],a=t[1],o=s(r.useState(!1),2),l=(o[0],o[1]),i=e.isEditing,c=n.colName,u=(n.value,RHelper.getColType(n.colName,m));r.useEffect((function(){l(!0)}),[]),r.useEffect((function(){a(e.cellData)}),[e]);var f=function(t){var n=t.target.value;a({colName:c,value:n}),e.onCellChange(c,n)},d=function(){return n.value||""},b="form-control form-control-sm"+("d"==u||"i"==u?" text-right":"")+" "+c;return b+=" fw-150",r.createElement("td",{className:""},i?r.createElement("input",{className:b,type:"text",disabled:e.loading,value:d(),onChange:f,onInput:f}):r.createElement("div",{className:n.colName+("d"==u||"i"==u?" text-right":"")},d()))},p=function(e){var t=s(r.useState(!1),2),n=t[0],a=t[1],o=s(r.useState(e.item),2),l=o[0],i=o[1],f=s(r.useState(!1),2),d=f[0],m=f[1];r.useEffect((function(){i(e.item)}),[e.item]);var p=function(e){a(!0),App.ajax_post(get_ajax_url("CategoryAction","change_status"),{isNew:0,data:{id:l.id,status:e}},(function(e){a(!1),e.error||i(e.data.row)}),(function(e){a(!1)}))},y=function(e,t){var n=c(c({},l),{},u({},e,t));i(n)},g=e.rowIndex;return r.createElement("tr",{id:"tr-"+l.id},r.createElement("td",null,isNaN(g)?"":g+1),e.cols.map((function(e,t){return r.createElement(b,{key:t,cellData:{colName:e,value:e in l?l[e]:""},isEditing:d&&"created_on"!=e,loading:n,onCellChange:y,changeStatus:p})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){e.preventDefault(),d?(a(!0),App.ajax_post(get_ajax_url("CategoryAction","save"),{isNew:0,data:{id:l.id,category:l.category}},(function(e){a(!1),e.error||(m(!d),i(e.data.row))}),(function(e){a(!1)}))):m(!d)},disabled:n},d?"Save":"Edit"),r.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(l)},disabled:n},"Delete")))},y=function(e){var t=s(r.useState(!1),2),n=t[0],a=t[1],o=s(r.useState(e.item),2),l=o[0],i=o[1];r.useEffect((function(){}),[l]),r.useEffect((function(){i(e.item),a(e.loading)}),[e]);var f=function(e,t){var n=c(c({},l),{},u({},e,t));i(n)};return r.createElement("tr",{id:"tr-"+l.id},r.createElement("td",null),e.cols.map((function(e,t){return r.createElement(b,{key:t,cellData:{colName:e,value:e in l?l[e]:""},loading:n,id:"",isEditing:RHelper.isEditable(e,m),onCellChange:f})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=c({},l);e.handleRowCreate(t),i(t)}},"Create")))};function g(e){var t=e.loading,n=e.form,a=e.searchData,o=s(r.useState(n.category),2),l=o[0],i=o[1],c=function(e){a({category:l})};return r.createElement("div",{className:"card border-0 bg-transparent"},r.createElement("div",{className:"card-body p-0 pt-1"},r.createElement("div",{className:"form-row"},r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"category"},"Category:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",id:"category",disabled:t,value:l,onChange:function(e){return i(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&c()}})),r.createElement("div",{className:"col-auto"},r.createElement("button",{className:"btn btn-sm btn-info",onClick:c,disabled:t},"Search")))))}function v(e){var t=e.initialFormStates,n={id:"",category:""},a=s(r.useState(!0),2),o=a[0],i=a[1],u=s(r.useState(!0),2),f=u[0],d=u[1],b=s(r.useState(!1),2),v=(b[0],b[1]),h=s(r.useState(n),2),E=h[0],w=h[1],S=s(r.useState({}),2),j=S[0],O=S[1],N=s(r.useState([]),2),C=N[0],_=N[1],x=s(r.useState(""),2),A=x[0],k=x[1],D=s(r.useState(t),2),P=D[0],R=D[1];r.useEffect((function(){T(c(c({},P),{},{with:"form"}))}),[]),r.useEffect((function(){I()}),[C]),r.useEffect((function(){$("#table-categories").floatThead({autoReflow:!0})}),[o]);var I=function(){var e={};C.map((function(t,n){e[t.id]=n})),O(e)},T=function(e){var t={};t=Object.assign({},t,e),App.ajax_post(get_ajax_url("CategoryAction","get_list"),t,(function(e){v(!0),d(!1),H(e.data),i(!1)}),(function(e){v(!0)}))},H=function(e){_(l(e.rows)),k(e.sql),init_tooltip()},F=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.category,"'?"),(function(t){t&&App.ajax_post(get_ajax_url("CategoryAction","delete"),{id:e.id},(function(t){0==t.error&&_(C.filter((function(t){return t.id!=e.id})))}))}),{title:"Delete entry"})},q=Object.keys(m);return r.createElement(r.Fragment,null,r.createElement(g,{form:P,setForm:R,loading:f,setLoading:d,searchData:function(e){d(!0),T(e)}}),r.createElement("h4",null,C?"Results (".concat(C.length," records)"):"No Results",r.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),r.createElement("div",{className:"sql-log-wrap"},r.createElement("pre",null,A)),r.createElement("div",{className:"table-wrap"},r.createElement("table",{id:"table-categories",className:"data-table editable border-0",style:{minWidth:500}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"No"),q.map((function(e,t){return r.createElement("th",{key:e},RHelper.getColName(e,m))})),r.createElement("th",null))),r.createElement("tbody",null,r.createElement(y,{key:"new-row",item:E,cols:q,loading:f,handleRowCreate:function(e){d(!0);var t=c({},e);App.ajax_post(get_ajax_url("CategoryAction","save"),{data:t},(function(e){d(!1),e.error||(_([e.data.row].concat(l(C))),w(c({},n)))}),(function(e){d(!1)}))}}),C.map((function(e,t){return r.createElement(p,{key:e.id,isTotal:!1,item:e,cols:q,rowIndex:j[e.id],loading:f,handleRowDelete:F})}))))))}var h="undefined"!=typeof RCategoryProps?RCategoryProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(r.createElement(v,h),document.getElementById("root"))}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var l=1/0;for(s=0;s<e.length;s++){for(var[n,a,o]=e[s],i=!0,c=0;c<n.length;c++)(!1&o||l>=o)&&Object.keys(r.O).every((e=>r.O[e](n[c])))?n.splice(c--,1):(i=!1,o<l&&(l=o));if(i){e.splice(s--,1);var u=a();void 0!==u&&(t=u)}}return t}o=o||0;for(var s=e.length;s>0&&e[s-1][2]>o;s--)e[s]=e[s-1];e[s]=[n,a,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=965,(()=>{var e={965:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[l,i,c]=n,u=0;if(l.some((t=>0!==e[t]))){for(a in i)r.o(i,a)&&(r.m[a]=i[a]);if(c)var s=c(r)}for(t&&t(n);u<l.length;u++)o=l[u],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(s)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r.nc=void 0;var a=r.O(void 0,[96],(()=>r(5474)));a=r.O(a)})();