<?php

/**
 * Class OrderInvoiceAction
 */
class OrderInvoiceAction extends BaseAction
{
    /**
     * @var OrderInvoiceModel
     */
    private $order_invoice_model = null;

    function __construct()
    {
        parent::__construct();

        $this->order_invoice_model =& Loader::get_instance()->load_model('OrderInvoiceModel');
    }

    public function save()
    {
        $this->post_restrict();
        $old_order_inv_no = intval($_POST['old_order_inv_no'] ?? '');

        // Get order_invoice data
        $row = $_POST['data'];

        // pre-process
        trim_space($row);
        clean_date_values($row, ['date']);

        $order_inv_no = $row['order_inv_no'] ?? null;
        if (!$old_order_inv_no) {
            // validation
            if (!($row['date']??'')) {
                $this->return(true, "Invoice Date is required");
            }
            $x = $this->order_invoice_model->get($order_inv_no);
            if ($x) {
                $this->return(true, "The invoice no '" . $order_inv_no . "' already exists!");
            }
            // create a new entry
            $order_inv_no = $success = $this->order_invoice_model->insert($row, false);
        } else {
            $x = $this->order_invoice_model->get($order_inv_no);
            if (!$x) {
                $this->return(true, "The invoice no '" . $order_inv_no . "' does not exist!");
            }
            $success = $this->order_invoice_model->update($row, $order_inv_no, false);
        }

        $this->data['row'] = $this->order_invoice_model->get_row($order_inv_no);
        if ($success) {
            $this->msg = "Saved successfully.";
        } else {
            $this->error = true;
            $this->msg = 'Failed to save data.';
        }
        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->order_invoice_model->get($id, 1);
        if (!empty($row)) {
            $this->order_invoice_model->delete($id);
            $this->msg = "Deleted successfully.";
        } else {
            $this->error = true;
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get list and other data such as groupA, groupB, and Categories
     *
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        // Get totals
        $this->order_invoice_model->calc_count(TRUE);
        $rows_count = $this->order_invoice_model->get_list($_POST);

        // Get limited rows
        $this->order_invoice_model->set_pager($this->get_pagination($rows_count));
        $this->order_invoice_model->calc_count(FALSE);
        $rows = $this->order_invoice_model->get_list($_POST);

        $this->data = [
            'rows' => $rows,
            'pager' => $this->pagination->getPagerResponse(),
            'sql' => $this->db->show_query_history(false),
        ];
        $this->return();
    }
}