;(function ($) { $.fn.datepicker.language['fi'] = {
    days: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    daysShort: ['<PERSON>', '<PERSON>', 'Ti', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'La'],
    daysMin: ['<PERSON>', '<PERSON>', 'Ti', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'La'],
    months: ['<PERSON><PERSON><PERSON><PERSON>','He<PERSON>iku<PERSON>','<PERSON><PERSON>ku<PERSON>','<PERSON><PERSON>ku<PERSON>','<PERSON><PERSON>okuu','<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON>aku<PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
    monthsShort: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],
    today: 'Tän<PERSON>ä<PERSON>',
    clear: '<PERSON>h<PERSON>nn<PERSON>',
    dateFormat: 'dd.mm.yyyy',
    timeFormat: 'hh:ii',
    firstDay: 1
};
 })(jQuery);