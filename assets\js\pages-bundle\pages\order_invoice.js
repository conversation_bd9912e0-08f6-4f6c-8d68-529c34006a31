(()=>{var e,t={2303:(e,t,r)=>{"use strict";r.d(t,{MK:()=>a,Nh:()=>c,RI:()=>o,UQ:()=>i,WH:()=>l,ZU:()=>s});var n=r(6540),o="table-order",a={order_id:["Order ID","i",!0],au_master:["AU Master","",!0],customer_id:["Customer","",!0],name:["Name","",!0],category:["Cat.","",!0],warehouse:["WH?","",!1],action_update_info:[n.createElement("i",{className:"oi oi-info m-0",title:"Update order info.","data-placement":"right"}),""],cust_complaint_solved:[n.createElement("i",{className:"oi oi-info m-0",title:"Customer complaint status.","data-placement":"right"}),""],expected_volume:["Volumen geschätzt","",!0],est_loading_date:["Est. L. Date","dt",!0],loading_date:["Loading Date","dt",!0],offer_id:["Offer","",!0],invoices:["Invoices",""],act_invoices:["",""],supplier_comments_statuses:["Supp Statuses",""],customer_comments_statuses:["Customer Statuses",""],supplier_status:[n.createElement("i",{className:"oi oi-info m-0",title:"Supplier statuses and comments","data-placement":"right"}),""],customer_status:[n.createElement("i",{className:"oi oi-info m-0",title:"Customer statuses and comments","data-placement":"right"}),""]},l={order_id:60,au_master:35,name:200,offer_id:60,customer_id:90,customer_comments:200,supplier_comments:200,supplier_comments_statuses:700,customer_comments_statuses:500,action_btn:50,cust_complaint_solved:10,expected_volume:50,est_loading_date:50,est_loading_date2:90,loading_date:50,invoices:120,category:25,warehouse:25,customer_status:25,supplier_status:25,act_invoices:25},i=["order_id","name","offer_id","customer_id","au_master"],c=function(e){return $("tr#tr-".concat(e," td input.customer_id"))},s=function(e){return $("tr#tr-".concat(e," td input.offer_id"))}},2420:(e,t,r)=>{(e.exports=r(4765)(!1)).push([e.id,".hs-code-popup .ac-desc{display: none;}",""])},2701:(e,t,r)=>{"use strict";var n=r(6540),o=r(961),a=r(5848),l=r.n(a),i=r(5556),c=r.n(i),s=(r(6151),[{accessor:"order_inv_no",name:"Inv No",value_type:"i",width:60,editable:!0,sortable:!0,searchable:!0},{accessor:"date",name:"Date",value_type:"dt",width:90,editable:!0,sortable:!0,searchable:!0,className:"d-picker text-center"},{accessor:"cust_no",name:"Cust. No",width:60,searchable:!0,editable:!0,sortable:!0,className:""},{accessor:"cust_name",name:"Cust. Name",width:100,searchable:!0,editable:!0},{accessor:"cust_country",name:"Cust. Country",display_type:"select",width:50,searchable:!0,editable:!0,className:"text-center"},{accessor:"value",name:"Value",value_type:"d",width:70,editable:!0,className:"text-right"},{accessor:"hs_code_value_sum",name:"Total Value",value_type:"d",width:80,className:"text-right"},{accessor:"hs_codes",name:"HS Codes",searchable:!0,width:400},{accessor:"act_buttons",name:"",value_type:"",width:90,className:"text-center"}]),u=(r(4114),function(e,t,r,n,o,a){o.setLoading&&o.setLoading(!0),App.ajax_post_ok(get_ajax_url(e,t),r,(function(e){o.setLoading&&o.setLoading(!1),n&&n(e)}),o.blockEle,(function(e){o.setLoading&&o.setLoading(!1),a&&a(e)}))}),m=function(e,t,r,n){u("OrderInvoiceAction","save",e,t,r,n)},f=function(e,t,r,n){u("OrderInvoiceHSCodeAction","save",e,t,r,n)},d=(r(2303),function(e,t,r){var o=function(e){return l().renderToStaticMarkup(n.createElement(n.Fragment,null,n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Order Inv No"),n.createElement("div",{className:"col-3"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"order_inv_no",value:e.order_inv_no||"",autoComplete:"off"})),n.createElement("label",{className:"col-3 col-form-label-sm"},"Date"),n.createElement("div",{className:"col-3"},n.createElement("input",{type:"text",className:"form-control form-control-sm d-picker",name:"date",value:e.date||"",autoComplete:"off"}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Customer No"),n.createElement("div",{className:"col-6"},n.createElement("div",{className:"input-wrap-with-icon"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"cust_no",value:e.cust_no||""})))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Customer Name"),n.createElement("div",{className:"col-3"},n.createElement("div",{className:"input-wrap-with-icon"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"cust_name",value:e.cust_name||"",autoComplete:"off"}))),n.createElement("label",{className:"col-3 col-form-label-sm"},"Customer Country"),n.createElement("div",{className:"col-3"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"cust_country",value:e.cust_country||"",autoComplete:"off"}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Value"),n.createElement("div",{className:"col-3"},n.createElement("input",{type:"text",className:"form-control form-control-sm text-right",name:"value",value:CvUtil.toDecimalFormat(e.value||""),autoComplete:"off"})))))}(t),a=!t.order_inv_no,i=bs4pop.dialog({title:a?"Create Order Invoice":"Update Order Invoice",content:o,backdrop:"static",width:650,onShowEnd:function(){var e=i.$el;e.find('input[name="order_inv_no"]').focus(),init_datepicker(e.find(".d-picker")),init_autocomplete(e.find('input[name="cust_no"]'),{class:"CustomerAction",action:"get_ac_customers_full",exactName:""},t.cust_no&&t.cust_no>1,null,null,(function(t){var r=t.data.split("^");r&&4==r.length&&(e.find('[name="cust_no"]').val(r[1]),e.find('[name="cust_name"]').val(r[2]),e.find('[name="cust_country"]').val(r[3]))})),init_autocomplete(e.find('input[name="cust_name"]'),{class:"CustomerAction",action:"get_ac_customers_full",exactName:""},t.cust_no&&t.cust_no>1,null,null,(function(t){var r=t.data.split("^");r&&4==r.length&&(e.find('[name="cust_no"]').val(r[1]),e.find('[name="cust_name"]').val(r[2]),e.find('[name="cust_country"]').val(r[3]))}))},btns:[{label:a?"Create":"Update",className:"btn-info btn-sm",onClick:function(e){var n=i.$el,o={old_order_inv_no:t.order_inv_no,data:{order_inv_no:n.find('[name="order_inv_no"]').val(),date:CvUtil.dtValidYMDValue(n.find('[name="date"]').val()),cust_no:n.find('[name="cust_no"]').val(),cust_name:n.find('[name="cust_name"]').val(),cust_country:n.find('[name="cust_country"]').val(),value:CvUtil.parseNumber(n.find('[name="value"]').val())}};return r(i,o),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})}),p=function(e,t,r){var o=function(e){return l().renderToStaticMarkup(n.createElement(n.Fragment,null,n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Description"),n.createElement("div",{className:"col-9"},n.createElement("textarea",{type:"text",className:"form-control form-control-sm",name:"description",value:e.description||"",autoComplete:"off"}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"HSCode"),n.createElement("div",{className:"col-9"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"hs_code",value:e.hs_code||"",autoComplete:"off"}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"GW"),n.createElement("div",{className:"col-9"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"gw",value:e.gw||""}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"NW"),n.createElement("div",{className:"col-9"},n.createElement("input",{type:"text",className:"form-control form-control-sm",name:"nw",value:e.nw||""}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Value"),n.createElement("div",{className:"col-9"},n.createElement("input",{type:"text",className:"form-control form-control-sm text-right",name:"value",value:e.value||"",autoComplete:"off"}))),n.createElement("div",{className:"form-group row"},n.createElement("label",{className:"col-3 col-form-label-sm"},"Quantity"),n.createElement("div",{className:"col-9"},n.createElement("input",{type:"text",className:"form-control form-control-sm text-right",name:"quantity",value:CvUtil.toIntFormat(e.quantity||""),autoComplete:"off"})))))}(t),a=!t.id,i=bs4pop.dialog({title:(a?"Create invoice HS Code":"Update invoice HS Code")+'<span class="badge badge-success ml-2">'.concat(t.order_inv_no,"</span>"),content:o,backdrop:"static",width:400,className2:"hs-code-popup",onShowEnd:function(){var e=i.$el;e.find('[name="description"]').focus(),init_autocomplete(e.find('[name="description"]'),{class:"OrderInvoiceHSCodeAction",action:"get_ac_description",exactName:""},t.description&&t.description>1,null,null,(function(t){var r=t.data.split("^");r&&2==r.length&&e.find('[name="hs_code"]').val(r[0])}))},btns:[{label:a?"Create":"Update",className:"btn-info btn-sm",onClick:function(e){var n=i.$el,o={return_invoice:1,data:{id:t.id||"",order_inv_no:t.order_inv_no,hs_code:n.find('[name="hs_code"]').val(),gw:n.find('[name="gw"]').val(),nw:n.find('[name="nw"]').val(),value:CvUtil.parseNumber(n.find('[name="value"]').val()),description:n.find('[name="description"]').val(),quantity:CvUtil.parseNumber(n.find('[name="quantity"]').val())}};return r(i,o),!1}},{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})};function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function v(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=b(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==b(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,l,i=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return g(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var h=function(e){var t=y(n.useState(!1),2),r=t[0],o=(t[1],y(n.useState(e.item),2)),a=o[0],l=o[1];n.useEffect((function(){l(e.item)}),[e.item]);var i=function(e){d(0,a,(function(e,t){m(t,(function(t){t.error||(l(t.data.row),e&&e.hide())}),{blockEle:e.$el})}))},c=function(e){var t={order_inv_no:a.order_inv_no};p(0,t,(function(e,t){f(t,(function(t){t.error||(l(t.data.invoice),e&&e.hide())}),{blockEle:e.$el})}))},b=function(e,t){p(0,t,(function(e,t){f(t,(function(t){t.error||(l(t.data.invoice),e&&e.hide())}),{blockEle:e.$el})}))},g=function(){var e=bs4pop.dialog({title:"Invoice "+a.order_inv_no+"'s HS Codes",width:600,content:"",onShowEnd:function(){var t,r,n,o;t={order_inv_no:a.order_inv_no,return_html:!0},r=function(t){t.error||e.$el.find(".modal-body").html(t.data.html)},n={blockEle:e.$el.find(".modal-body")},u("OrderInvoiceHSCodeAction","get_list",t,r,n,o)},btns:[{label:"Close",className:"btn-secondary btn-sm",onClick:function(e){e.hide()}}]})};return n.createElement("tr",{id:"tr-"+a.order_inv_no,className:""},s.map((function(t){return function(t){var o=t.accessor,l=(t.editable,t.width),s=t.className,u=a[o]||"",m="",f=o+" "+s;switch(o){case"act_buttons":m=n.createElement(n.Fragment,null,n.createElement("span",{className:"badge badge-success cursor-p",onClick:c},"+HS"),n.createElement("button",v({className:"btn btn-sm btn-sm-td btn-light ml-2",disabled:r,onClick:function(t){return e.handleRowDelete(a)}},"disabled",r),"Delete"));break;case"date":m=CvUtil.dtNiceDMY(u);break;case"hs_codes":m=n.createElement("div",{className:"d-flex align-items-center justify-content-between"},n.createElement("div",{style:{width:t.width-20}},a.hs_codes.map((function(e){return n.createElement("div",{key:e.id,className:"form-row fs-z6 no-gutters"},n.createElement("div",{className:"col-4 tt",title:e.description},n.createElement("a",{href:"#",onClick:function(t){return b(0,e)}},e.description)),n.createElement("div",{className:"col-2"},n.createElement("a",{href:"#",onClick:function(t){return b(0,e)}},e.hs_code)),n.createElement("div",{className:"col-2"},e.gw),n.createElement("div",{className:"col-2"},e.nw),n.createElement("div",{className:"col-1 text-right"},CvUtil.toDecimalFormat(e.value)),n.createElement("div",{className:"col-1 text-right"},CvUtil.toIntFormat(e.quantity)))}))),a.hs_codes.length>0&&n.createElement("i",{className:"oi oi-info",title:"View all",onClick:g}));break;case"order_inv_no":m=n.createElement(n.Fragment,null,n.createElement("span",{className:"tt",title:""}," ",u),n.createElement("i",{className:"oi m-0 float-right oi-pencil oi-edit",onClick:i,title:"Please click to edit invoice."}));break;case"hs_code_value_sum":case"value":m=CvUtil.toDecimalFormat(u);break;default:m=u}return n.createElement("td",{key:o,className:f},n.createElement("div",{style:{width:l||"auto"}},m))}(t)})))};h.propTypes={loading:c().bool,isTotal:c().bool,item:c().object.isRequired,form:c().object.isRequired,handleRowDelete:c().func.isRequired};const E=h;function w(e){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach((function(t){O(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function O(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=w(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=w(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==w(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function j(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,l,i=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(s)throw o}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return C(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?C(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function k(e){var t=j(n.useState(!1),2),r=(t[0],t[1]),o=j(n.useState(e.form),2),a=o[0],l=o[1];(0,n.useEffect)((function(){r(!0)}),[]),(0,n.useEffect)((function(){l(e.form)}),[e.form]);var i=function(){return{from_date:$('#form-search [name="from_date"]').val(),to_date:$('#form-search [name="to_date"]').val()}},c=function(e){var t=e.target,r=t.name,n=t.value,o=t.checked;l("noCustomer"==r||"complaints"==r||"complaints_resolved"==r?S(S({},a),{},O({},r,o?1:0)):S(S({},a),{},O({},r,n),i()))};return n.createElement("div",{className:"card border-0 bg-transparent",id:"form-search"},n.createElement("div",{className:"card-body p-0 pt-1"},n.createElement("div",{className:"form-row"},n.createElement("div",{className:"col-auto"},n.createElement("label",{className:"mr-1",htmlFor:"filter_dr"},"Date:"),n.createElement("input",{type:"text",name:"from_date",id:"from-date",className:"d-picker form-control form-control-sm d-inline-block fw-100",disabled:e.loading,defaultValue:a.from_date,autoComplete:"off",onChange:c})," ~ ",n.createElement("input",{type:"text",name:"to_date",id:"to-date",className:"d-picker form-control form-control-sm d-inline-block fw-100",disabled:e.loading,defaultValue:a.to_date,autoComplete:"off","data-datepicker":"",onChange:c})),n.createElement("div",{className:"col-auto"},n.createElement("button",{className:"btn btn-sm btn-info",onClick:function(t){e.setForm(S(S({},a),i()))},disabled:e.loading},"Search")))))}k.propTypes={loading:c().bool,form:c().object.isRequired,setForm:c().func.isRequired,searchData:c().func.isRequired};const P=k;var A=r(2827),x=r(2280);function I(e){return I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},I(e)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach((function(t){D(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function D(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=I(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=I(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==I(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var U=function(e){var t=e.loading,r=e.form,o=e.setForm,a=function(e){var t=e.target,n=t.name,a=t.value;o(F(F({},r),{},D({},n,a)))};return n.createElement("tr",{id:"row-search",className:"row-search"+(t?" loading":"")},s.map((function(e,t){return function(e){var t=e.accessor,r=e.searchable,o=e.width,l="",i=t;if(r){var c="order_inv_no"==t?"order_inv_no_alias":t;l=n.createElement(x.A,{placeholder:"",name:c,onChange:a,className:"form-control form-control-sm d-inline-block"})}return n.createElement("td",{key:t,className:i},n.createElement("div",{style:{width:o||"auto"}},l))}(e)})))};U.propTypes={loading:c().bool,form:c().object.isRequired,setForm:c().func.isRequired};const T=U;r(8239);function H(e){return H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},H(e)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach((function(t){V(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function V(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=H(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=H(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==H(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M(e){return function(e){if(Array.isArray(e))return B(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Q(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,l,i=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(s)throw o}}return i}}(e,t)||Q(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(e,t){if(e){if("string"==typeof e)return B(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?B(e,t):void 0}}function B(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var G=n.createContext(null),K="table-order-invoice",z={page:0,pageCount:0},Y={date:CvUtil.dtToday()};function Z(e){var t=e.defaultSearchForm,r=e.defaultOrder,o=e.settings,a=W(n.useState(!0),2),l=a[0],i=a[1],c=W(n.useState(!0),2),f=c[0],p=c[1],b=W(n.useState(!1),2),v=b[0],y=b[1],g=W(n.useState(o),2),h=g[0],w=g[1],N=W(n.useState(t),2),S=N[0],O=N[1],j=W(n.useState(r.field),2),C=j[0],k=j[1],x=W(n.useState(r.dir),2),I=x[0],R=x[1],F=W(n.useState(z),2),D=F[0],U=F[1],H=W(n.useState(Y),2),q=(H[0],H[1],W(n.useState([]),2)),V=q[0],Q=q[1],B=W(n.useState(""),2),Z=B[0],J=B[1];n.useEffect((function(){ee({with:"form"})}),[]),n.useEffect((function(){v&&$("#"+K).floatThead({autoReflow:!0})}),[l]),n.useEffect((function(){v&&X(null)}),[C,I]),n.useEffect((function(){v&&X(null)}),[S]),n.useEffect((function(){v&&X(null)}),[D.page]);var X=function(e){ee({})},ee=function(e){e=Object.assign({},S,e||{}),te(e)},te=function(e){var t,r={pager:D};"order_field"in(r=Object.assign({},r,e))||(r.order_field=C),"order_dir"in r||(r.order_dir=I),u("OrderInvoiceAction","get_list",r,(function(e){e.error||re(e.data),i(!1)}),{blockEle:null,setLoading:p},t)},re=function(e){y(!0),Q(M(e.rows)),U(_.get(e,"pager",z)),J(e.sql),init_tooltip()},ne=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.order_inv_no,"'?"),(function(t){var r,n,o,a;t&&(r={id:e.order_inv_no},n=function(t){t.error||Q(V.filter((function(t){return t.order_inv_no!=e.order_inv_no})))},o={blockEle:v?$("#"+K):null,setLoading:p},u("OrderInvoiceAction","delete",r,n,o,a))}),{title:"Delete entry"})},oe=function(e){var t=e.name,r=e.accessor,o=e.width,a=e.sortable,l=e.className;return n.createElement("th",{key:r,className:r+(a?" icon-order-wrap":"")+" "+(l||""),onClick:function(t){return function(e){e.sortable&&(e.accessor==C?R((function(e){return"desc"==e?"asc":"desc"})):(k(e.accessor),R("asc")))}(e)}},n.createElement("div",{className:a?"sortable":"",style:{width:o||"auto"}},n.createElement("span",null,t||""),a&&C==r&&n.createElement("a",{className:"float-right icon-order "+I,href:"#"})))};return n.createElement(n.Fragment,null,n.createElement(G.Provider,{value:L(L({},h),{},{setStateSettings:w})},n.createElement(P,{form:S,setForm:O,loading:f,searchData:ee}),n.createElement("h6",null,V&&V.length>0?"Results (".concat((0,A.w)(D),")"):"No Results",n.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),n.createElement("div",{className:"sql-log-wrap"},n.createElement("pre",null,Z)),n.createElement("div",{className:"table-wrap position-relative"},n.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},n.createElement("button",{className:"btn btn-sm btn-success",onClick:function(e,t){d(0,t||Y,(function(e,t){m(t,(function(t){t.error||(Q([t.data.row].concat(M(V))),e&&e.hide())}),{blockEle:e.$el})}))},disabled:f},"Create an Invoice"),n.createElement("span",{className:"ml-5"},f?"Loading ...":"")),n.createElement("table",{id:K,className:"data-table editable border-0",style:{minWidth:500}},n.createElement("thead",null,n.createElement("tr",null,s.map((function(e,t){return oe(e)})))),n.createElement("tbody",null,n.createElement(T,{loading:f,form:S,setForm:O,onRefresh:X}),V.map((function(e,t){return n.createElement(E,{key:e.order_inv_no,isTotal:!1,item:e,form:S,loading:f,handleRowDelete:ne})})))),n.createElement(A.A,{pager:D,onPageChange:function(e){var t=L(L({},D),{},{page:e.selected});U(t)}}))))}Z.propTypes={defaultSearchForm:c().object.isRequired,defaultOrder:c().object.isRequired,settings:c().object.isRequired};r(4508);var J="undefined"!=typeof OrderInvoiceProps?OrderInvoiceProps:"undefined"!=typeof App?App.get_params(window.location):{};o.render(n.createElement(Z,J),document.getElementById("root"))},4114:(e,t,r)=>{"use strict";r.d(t,{A:()=>w});var n=r(6540),o=(r(961),r(5848),r(5556)),a=r.n(o),l=(r(6151),r(2303)),i=r(7406),c=r(5184),s=r(6234),u=r(9216),m=(r(8540),r(8239)),f=r(2827);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach((function(t){v(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e){return function(e){if(Array.isArray(e))return E(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,l,i=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){s=!0,o=e}finally{try{if(!c&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(s)throw o}}return i}}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){if(e){if("string"==typeof e)return E(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(e,t):void 0}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var w=n.createContext(null);function N(e){var t=e.defaultSearchForm,r=e.defaultOrder,o=e.settings,a=g(n.useState(!0),2),d=a[0],p=a[1],v=g(n.useState(!0),2),h=v[0],E=v[1],N=g(n.useState(!1),2),S=N[0],O=N[1],j=g(n.useState(o),2),C=j[0],k=j[1],P=g(n.useState(r.field),2),A=P[0],x=P[1],I=g(n.useState(r.dir),2),R=I[0],F=I[1],D={page:0,pageCount:0},U=g(n.useState(D),2),T=U[0],H=U[1],q={order_id:"",name:"",customer_id:"",customerName:"",offer_id:"",offerName:""},L=g(n.useState(q),2),V=(L[0],L[1]),M=g(n.useState([]),2),W=M[0],Q=M[1],B=g(n.useState(""),2),G=B[0],K=B[1],z=g(n.useState(t),2),Y=z[0],Z=z[1];n.useEffect((function(){ee({with:"form"})}),[]),n.useEffect((function(){S&&$("#"+l.RI).floatThead({autoReflow:!0})}),[d]),n.useEffect((function(){S&&X(null)}),[A,R]),n.useEffect((function(){S&&X(null)}),[Y]),n.useEffect((function(){S&&X(null)}),[T.page]);var J=Y.mode||"status",X=function(e){ee({})},ee=function(e){e=Object.assign({},Y,e),te(e)},te=function(e){E(!0);var t={pager:T};"order_field"in(t=Object.assign({},t,e))||(t.order_field=A),"order_dir"in t||(t.order_dir=R),App.ajax_post(get_ajax_url("OrderAction","get_list"),t,(function(e){O(!0),E(!1),e.error||re(e.data),p(!1)}),(function(e){O(!0)}))},re=function(e){Q(y(e.rows)),H(_.get(e,"pager",D)),K(e.sql),init_tooltip()},ne=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.order_id," / ").concat(e.name,"'?"),(function(t){t&&App.ajax_post(get_ajax_url("OrderAction","delete"),{id:e.order_id},(function(t){0==t.error&&Q(W.filter((function(t){return t.order_id!=e.order_id})))}))}),{title:"Delete entry"})},oe=function(e){return"0"===e?" oi-x red":"1"===e?" oi-circle-check":"2"===e?" oi-ban c-lightgrey-d":null===e||"x"===e?" oi-minus c-lightgrey-d":""},ae=function(e,t){if(!_.isUndefined(t)){var r="!"+e.id,o=_.get(Y,r,"u");return n.createElement("i",{className:"oi "+oe(o)})}var a=Y.commentStatusFilterId||"",l=Y.commentStatusFilterValue||null;return a==e.id?n.createElement("i",{className:"oi position-absolute"+oe(l),style:{top:0,right:5}}):""},le=function(e,t,r,n){if(_.isUndefined(r)){var o=["u","0",null,"2","1"];Z((function(e){var r=b(b({},e),{},{commentStatusFilterId:t});if(e.commentStatusFilterId!=t)r.commentStatusFilterValue="1";else{var n=e.commentStatusFilterValue||null;r.commentStatusFilterValue=App.getNextValue(n,o)}return r}))}else{1==n&&e.preventDefault();var a=n?["x","u","0","2","1"]:["x","u","0","1","2"];Z((function(e){var r=b({},e),n="!"+t;if(n in e){var o=e[n]||null;r[n]=App.getNextValue(o,a)}else r[n]="1";return r}))}},ie=function(e){switch(e){case"supplier_comments_statuses":return"invoice"==J?null:o.supplierStatuses.map((function(t,r){var o="";return 1==t.ui_border&&(o=n.createElement("th",{key:t.id+"g",className:"gap"},n.createElement("div",null))),n.createElement(n.Fragment,null,o,n.createElement("th",{key:t.id,className:"cs-col"+(RHelper.isSortable(e,l.UQ)?" icon-order-wrap":1==t.ui_border||0==r||500==t.order||1e3==t.order?" sep-l":""),onClick:function(e){return le(e,t.id)}},n.createElement("div",{key:t.id,className:"tt vt","data-placement":"right",title:t.name,style:{width:20}},t.name),ae(t)))}));case"customer_comments_statuses":return"invoice"==J?null:o.customerStatuses.map((function(t,r){var o="";return 1==t.ui_border&&(o=n.createElement("th",{key:t.id+"g",className:"gap"},n.createElement("div",null))),n.createElement(n.Fragment,null,o,n.createElement("th",{key:t.id,className:"cs-col"+(RHelper.isSortable(e,l.UQ)?" icon-order-wrap":1==t.ui_border||0==r?" sep-l":""),onClick:function(e){return le(e,t.id)}},n.createElement("div",{key:t.id,className:"tt vt","data-placement":"right",title:t.name,style:{width:20}},t.name),ae(t)))}));case"act_invoices":if("invoice"!=J)return null;break;case"invoices":return"invoice"!=J?null:_.map(Object.values(m.HB),(function(t,r){return n.createElement("th",{key:t},n.createElement("div",{style:{width:RHelper.getColWidth(e,l.WH)}},m.by[t]))}))}return n.createElement("th",{key:e,className:e+(RHelper.isSortable(e,l.UQ)?" icon-order-wrap":""),onClick:function(t){return function(e){RHelper.isSortable(e,l.UQ)&&(e==A?F((function(e){return"desc"==e?"asc":"desc"})):(x(e),F("asc")))}(e)}},n.createElement("div",{className:RHelper.isSortable(e,l.UQ)?"sortable":"",style:{width:_.get(l.WH,e,"")}},n.createElement("span",null,RHelper.getColName(e,l.MK)),RHelper.isSortable(e,l.UQ)&&A==e&&n.createElement("a",{className:"icon-order "+R,href:"#"})))},ce=Object.keys(l.MK);return n.createElement(w.Provider,{value:b(b({},C),{},{setStateSettings:k})},n.createElement(i.A,{form:Y,setForm:Z,loading:h}),n.createElement("h6",null,W&&W.length>0?"Results (".concat((0,f.w)(T),")"):"No Results",n.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),n.createElement("div",{className:"sql-log-wrap"},n.createElement("pre",null,G)),n.createElement("div",{className:"table-wrap position-relative"},n.createElement("div",{className:"table-btn-wrap position-absolute",style:{top:-40,left:450}},n.createElement("button",{className:"btn btn-sm btn-info mr-4",onClick:X,disabled:h},"Refresh"),n.createElement("button",{className:"btn btn-sm btn-success",onClick:function(e,t){(0,u.d)(e,t,(function(e,t){!function(e,t){E(!0);var r=b({},e);delete r.customerName,delete r.offerName,App.ajax_post(get_ajax_url("Order","save"),{data:r,isNew:1},(function(e){E(!1),e.error||(Q([e.data.row].concat(y(W))),V(b({},q)),_.isUndefined(t)||t.hide())}),(function(e){E(!1)}))}(e,t)}))},disabled:h},"Create an Order"),n.createElement("span",{className:"ml-5"},h?"Loading ...":"")),n.createElement("table",{id:l.RI,className:"data-table editable border-0",style:{minWidth:500}},n.createElement("thead",null,n.createElement("tr",null,ce.map((function(e,t){var r=ie(e);if(r)return r})))),n.createElement("tbody",null,n.createElement(c.A,{form:Y,setForm:Z,loading:h,commentStatusFilterChange:le,commentStatusFilter:ae}),W.map((function(e,t){return n.createElement(s.A,{key:e.order_id,isTotal:!1,item:e,cols:ce,form:Y,loading:h,handleRowDelete:ne})})))),n.createElement(f.A,{pager:T,onPageChange:function(e){var t=b(b({},T),{},{page:e.selected});H(t)}})))}N.propTypes={defaultSearchForm:a().object.isRequired,defaultOrder:a().object.isRequired,settings:a().object.isRequired}},4508:(e,t,r)=>{var n=r(2420);"string"==typeof n&&(n=[[e.id,n,""]]);var o={hmr:!0,transform:undefined,insertInto:void 0};r(3027)(n,o);n.locals&&(e.exports=n.locals)},8239:(e,t,r)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,{AI:()=>i,HB:()=>a,by:()=>l,qh:()=>c});var a={FINAL:"final",PROFORMA:"proforma",FREIGHT:"freight",OTHER:"other",PALLETS:"pallets"},l=o(o(o(o(o({},a.FINAL,"Final"),a.PROFORMA,"Proforma"),a.FREIGHT,"Freight"),a.OTHER,"Other"),a.PALLETS,"Pallets"),i={PAID:"paid",UNPAID:"unpaid"},c=o(o({},i.PAID,"Paid"),i.UNPAID,"Unpaid")}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.m=t,e=[],n.O=(t,r,o,a)=>{if(!r){var l=1/0;for(u=0;u<e.length;u++){for(var[r,o,a]=e[u],i=!0,c=0;c<r.length;c++)(!1&a||l>=a)&&Object.keys(n.O).every((e=>n.O[e](r[c])))?r.splice(c--,1):(i=!1,a<l&&(l=a));if(i){e.splice(u--,1);var s=o();void 0!==s&&(t=s)}}return t}a=a||0;for(var u=e.length;u>0&&e[u-1][2]>a;u--)e[u]=e[u-1];e[u]=[r,o,a]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),n.j=480,(()=>{var e={480:0};n.O.j=t=>0===e[t];var t=(t,r)=>{var o,a,[l,i,c]=r,s=0;if(l.some((t=>0!==e[t]))){for(o in i)n.o(i,o)&&(n.m[o]=i[o]);if(c)var u=c(n)}for(t&&t(r);s<l.length;s++)a=l[s],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(u)},r=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),n.nc=void 0;var o=n.O(void 0,[96],(()=>n(2701)));o=n.O(o)})();