<?php

/**
 * Class ConfigTaskCategoryAction For tasks
 */
class ConfigTaskCategoryAction extends BaseAction
{
    const CATEGORIES = [
        SysConfigModel::CONFIG_TYPE_TASK_CATEGORY,
    ];

    /**
     * @var SysConfigModel
     */
    protected $config_model = null;

    function __construct()
    {
        parent::__construct();
        $this->config_model =& Loader::get_instance()->load_model('SysConfigModel');
    }

    /**
     * Save Config row record.
     */
    public function save()
    {
        $this->post_restrict();

        $row = $_POST['data'];
        $isNew = ($row['id'] ?? '') ? false : true;

        // validation
        if (strlen($row['name'] ?? '') < 1) {
            $this->return(true, "Please fill category name.");
        }

        // validation for duplicated category name.
        /*$where = '1';
        $where .= $this->db->where_in('`type`', self::CATEGORIES);
        $where .= $this->db->where_equal('`name`', $row['name']);
        if (!$isNew) {
            $where .= $this->db->where_not_equal('id', $row['id']);
        }
        $x = $this->config_model->get_by_where($where, 1);
        if ($x) {
            $this->return(true, 'Duplicated category name. Please choose another one.');
        }*/

        $row_old = null;
        $id = $row['id'] ?? '';
        $parent_id = $row['parent_id'] ?? '';
        if (!$parent_id) $row['parent_id'] = NULL;
        if ($isNew) {
            unset($row['id']);
            $row['code'] = 'C' . time();
        } else {
            $row_old = $this->config_model->get($id);
            if (!$row_old) {
                $this->return(true, 'Invalid request. Entry does not exist.');
            }
        }

        // Saving
        $row['type'] = SysConfigModel::CONFIG_TYPE_TASK_CATEGORY;

        if ($isNew) {
            $success = false;
            $id = $this->config_model->insert($row);
            if ($id) {
                $row['id'] = $id;
                $success = true;
            }
        } else {
            $success = $this->config_model->update($row, $id);
            // if this was parent category and is updated to a sub category, we need to update all chidren.
            if (!$row_old['parent_id'] && $parent_id) {
                $this->config_model->update_where(['parent_id' => NULL], $this->db->where_equal('parent_id', $row_old['id'], ''));
            }
        }

        if ($success) {
            $this->data['row'] = $this->config_model->get($id);
            $this->data['row']['level'] = $row['parent_id'] ? 2 : 1;
            $settings = $this->config_model->get_task_categories_settings();
            $this->data['settings'] = $settings;
            $this->msg = $isNew ? 'Created successfully.' : 'Updated Successfully.';
        } else {
            $this->error = true;
            $this->msg = 'Failed to save status info.';
        }

        $this->return();
    }

    public function delete()
    {
        $this->post_restrict();
        $this->msg = '';
        $id = $_POST['id'];
        $row = $this->config_model->get($id);
        if (!empty($row)) {
            $this->config_model->delete($id);
            $this->data['settings'] = $this->config_model->get_task_categories_settings();
            $this->msg = "Deleted successfully.";
        } else {
            $this->return(true, 'Invalid request! Data does not exist.');
        }
        $this->return();
    }

    /**
     * Get a table of list
     */
    public function get_list()
    {
        $this->post_restrict();
        $this->msg = '';

        $this->data['rows'] = $this->config_model->get_task_categories($_POST);
        $this->data['sql'] = $this->db->show_query_history(false);

        $format = $_POST['format'] ?? 'json';
        if ($format == 'json') {
            $this->return();
        }
        $this->return();
    }


    /**
     * ---------------------------------------------------------------------------------------------------------------------
     * Category management for js Tree
     * ---------------------------------------------------------------------------------------------------------------------
     */
    public function delete_node()
    {
        $this->login_restrict();
        $this->msg = '';
        $id = $_GET['id'] ?? '';
        if ($id) {
            // Check if it is removable?
            $where = $this->db->where_by_array([
                'task_category_id' => $id,
                'user_id' => Auth::current_user_id()
            ]);
            $x = $this->config_model->get_by_where($where, '1', BaseModel::TBL_TASK_USER_CATEGORY_MAP);
            if ($x) {
                $this->return(true, 'Please remove the task assignments to remove the category.');
            }

            // remove children
            $this->config_model->delete_node($id);

            $where = $this->db->where_by_array([
                'id' => $id,
                '`type`' => SysConfigModel::CONFIG_TYPE_TASK_CATEGORY
            ]);
            $this->error != $this->config_model->delete_by_where($where);
        } else {
            $this->ajax_return(true, 'Invalid request. No category id provided or category does not exist.');
        }
        $this->return();
    }

    public function create_node()
    {
        $this->login_restrict();

        $this->msg = '';
        $parent_id = $_GET['id'] ?? '';
        $text = $_GET['text'] ?? '';
        $position = $_GET['position'] ?? '';

        if ($parent_id) {
            $x = $this->db->query_scalar("SELECT (MAX(`order`) + 1 ) AS new_order FROM sys_config WHERE " . $this->db->where_by_array([
                    'parent_id' => $parent_id,
                    'type' => SysConfigModel::CONFIG_TYPE_TASK_CATEGORY
                ]));

            $new_order = $x ? $x : 0;

            $pk = $this->config_model->gen_pk();
            $new_node = [
                'id' => $pk,
                'parent_id' => $parent_id,
                'name' => $text,
                'order' => $new_order,
                'type' => SysConfigModel::CONFIG_TYPE_TASK_CATEGORY,
                'code' => $pk
            ];
            $new_id = $this->config_model->insert($new_node);
            if ($new_id) {
                $new_node['text'] = $new_node['name'];
                $this->data['row'] = $new_node;
            } else {
                $this->ajax_return(true, 'Failed to create a new category.');
            }
        } else {
            $this->ajax_return(true, 'Invalid request. No category id provided or category does not exist.');
        }
        $this->return();
    }

    public function rename_node()
    {
        $this->login_restrict();

        $this->msg = '';
        $id = $_GET['id'] ?? '';
        $parent_id = $_GET['parent_id'] ?? null;
        $text = $_GET['text'] ?? '';
        $position = $_GET['position'] ?? '';
        $is_new = false;
        if (strlen($id) != 15) {
            $is_new = true;
            $id = '';
        }

        if ($is_new) {
            $new_order = 0;
            if ($parent_id) {
                $x = $this->db->query_scalar("SELECT (MAX(`order`) + 1 ) AS new_order FROM sys_config WHERE " . $this->db->where_by_array([
                        'parent_id' => $parent_id,
                        'type' => SysConfigModel::CONFIG_TYPE_TASK_CATEGORY
                    ]));
                $new_order = $x ? $x : 0;
            }
            $pk = $this->config_model->gen_pk();
            $new_node = [
                'id' => $pk,
                'parent_id' => $parent_id,
                'name' => $text,
                'order' => $new_order,
                'type' => SysConfigModel::CONFIG_TYPE_TASK_CATEGORY,
                'code' => $pk
            ];
            $new_id = $this->config_model->insert($new_node);
            if ($new_id) {
                $new_node['text'] = $new_node['name'];
                $this->data['row'] = $new_node;
            } else {
                $this->ajax_return(true, 'Failed to create a new category.');
            }
        } else {
            $x = $this->config_model->get($id);
            if ($x) {
                $this->error != $this->config_model->update(['name' => $text], $id);
                if ($this->error) {
                    $this->ajax_return(true, 'Failed to update category name. Please try again.');
                } else {
                    $x['name'] = $text;
                }
                $this->data['row'] = $x;
            } else {
                $this->ajax_return(true, 'Category does not exist.');
            }
        }
        $this->return();
    }

    /**
     * Move node in a tree.
     */
    public function move_node()
    {
        $this->login_restrict();
        $this->msg = '';

        $id = $_GET['id'] ?? '';
        $parent_id = $_GET['parent_id'] ?? null;
        $text = $_GET['text'] ?? '';
        $position = intval($_GET['position'] ?? '');

        $node = $this->config_model->get($id);
        $p_node = $this->config_model->get($parent_id);
        if ($node) {
            // get children of p_node
            $where = '';
            $where .= $this->db->where_equal('`type`', SysConfigModel::CONFIG_TYPE_TASK_CATEGORY);
            $where .= $this->db->where_equal('parent_id', $parent_id);

            // Update the children order
            $children = $this->db->query("
                SELECT id, `order` 
                FROM sys_config c1 
                WHERE 1 $where
                ORDER BY `order`, name    
            ;");
            if ($children) {
                $order = 0;
                foreach ($children as $n) {
                    if ($order == $position) $order ++;
                    $this->config_model->update(['order' => $order+1], $n['id']);
                    $order ++;
                }
            }
            $this->error = !$this->config_model->update(['parent_id' => $parent_id, 'order' => ($position + 1)], $id);
        } else {
            $this->return(true, 'Category does not exist. Please reload a page and try again.');
        }

        $this->return();
    }

    /**
     * Get the Task-User-Category map data.
     *
     */
    public function get_task_user_category_map()
    {
        $this->login_restrict();
        $this->msg = '';

        $category_id = $_REQUEST['category_id'] ?? '';
        $user_id = Auth::current_user_id();

        $where = $this->db->where_by_array([
            'task_category_id' => $category_id,
            'user_id' => $user_id,
        ]);
        $rows = $this->config_model->get_list_by_where($where,'*', BaseModel::TBL_TASK_USER_CATEGORY_MAP);
        $this->data['tuc_list'] = $rows ? $rows : [];
        $this->return();
    }

    public function get_categories()
    {
        $this->login_restrict();
        $this->msg = '';
        $this->data['category_tree'] = SysConfigModel::get_instance()->get_task_categories_jstree([]);
        $this->return();
    }

}