import React, {Fragment} from 'react';
import ReactDOMServer from 'react-dom/server';

import {colsDefs, colsWidth, get_customer_ele, get_offer_ele} from "src/pages/order/TableDef";
import HtmlHelper from "src/shared/utils/HtmlHelper";
import {OrderInfoFormHandler} from "src/pages/order/OrderInfoForm";
import OrderStatusCell from "src/pages/order/cells/OrderStatusCell";
import OrderInvoiceCell from "src/pages/order/cells/OrderInvoiceCell";
import {OrderContext} from './Order';
import {updateOrderComments, updateOrderInfo} from './OrderBiz';
import {OrderCategoryTypes, OrderCategoryTypesDropdown} from "src/shared/constants/order";
import {InvoiceTypes, InvoiceTypesNames} from "src/shared/constants/invoice";
import {OrderInvoicesCommentFormHandler} from "src/pages/order/OrderInvoicesCommentForm";
import {InvoiceFormHandler} from "src/pages/order/invoice/InvoiceForm";
import * as OrderBiz from "src/pages/order/OrderBiz";

const TableRow = (props) => {
    const [loading, setLoading] = React.useState(false);
    const [item, setItem] = React.useState(props.item);
    const [isEditing, setIsEditing] = React.useState(false);
    const [domReady, setDomReady] = React.useState(false);
    const [isEditingCells, setIsEditingCells] = React.useState({
        'est_loading_date': false,
        'est_loading_date2': false,
        'loading_date': false,
        'expected_volume': false,
    });

    React.useEffect(() => {
        setDomReady(true);
        if (_.isEmpty(item.customer_id)) {
            init_ac_customer();
        }
    }, []);

    React.useEffect(() => {
        setItem(props.item);
    }, [props.item]);

    React.useEffect(() => {
        if (domReady)
            updateRowUI();
    }, [item]);

    React.useEffect(() => {
        if (domReady) {
            if (_.isEmpty(item.customer_id)) {
                init_ac_customer();
            }
        }
    }, [item.customer_id]);

    React.useEffect(() => {
        if (domReady) {
            //init_tooltip($(`tr#tr-${item.order_id} td .d-picker`));
        }
    }, [item.order_invoices]);

    React.useEffect(() => {
        if (domReady && isEditing) {
            init_ac_customer();
            init_ac_offer_id();
            init_datepicker($(`tr#tr-${item.order_id} td .d-picker`));
        }
    }, [isEditing]);

    const {supplierStatuses, customerStatuses} = React.useContext(OrderContext);
    const mode = props.form.mode || 'status';

    const updateRowUI = () => {
        dispose_and_init_tooltip($(`tr#tr-${item.order_id} .tt, tr#tr-${item.order_id} .oi.oi-info`));
    };

    const cb_ac_customer = suggestion => {
        const $cust_ele = get_customer_ele(item.order_id);
        if ($cust_ele.length > 0 && $cust_ele.closest('tr').find('input.order_id ').length < 1) {
            // we save here.
            let merged = getMergedItem();
            let post_data = {
                isNew: 0,
                old_order_id: item.old_order_id,
                data: {
                    order_id: merged.order_id,
                    name: merged.name,
                    customer_id: merged.customer_id,
                }
            };
            updateOrderInfo(post_data, res => {
                if (!res.error) {
                    setItem(res.data.row);
                }
            }, {blockEle: $cust_ele.closest('td')});
        }
    };

    const init_ac_customer = () => {
        const value = item['customer_id'] || '';
        init_autocomplete(get_customer_ele(item.order_id), {
            class: 'CustomerAction',
            action: 'get_ac_customers',
            exactName: ''
        }, value != null && value.length > 1, null, null, cb_ac_customer);
    };

    const init_ac_offer_id = () => {
        const value = item['offer_id'] || '';
        init_autocomplete(get_offer_ele(item.order_id), {
            class: 'OfferAction',
            action: 'get_ac_offers',
            exactName: ''
        }, value != null && value.length > 1);
    };

    /**
     * Edit/Save action
     *
     * @param e
     */
    const handleEditButton = (e) => {
        e.preventDefault();

        if (isEditing) {
            setLoading(true);
            let merged = getMergedItem();
            setItem(merged);
            App.ajax_post(get_ajax_url('OrderAction', 'save'), {
                isNew: 0,
                old_order_id: item.old_order_id,
                data: {
                    order_id: item.order_id,
                    name: item.name,
                    offer_id: merged.offer_id,
                    customer_id: merged.customer_id,
                    au_master: merged.au_master,
                    loading_date: merged.loading_date,
                    est_loading_date: merged.est_loading_date,
                    est_loading_date2: merged.est_loading_date2,
                    category: merged.category,
                    expected_volume: merged.expected_volume,
                },
            }, function (res) {
                setLoading(false);
                if (!res.error) {
                    setIsEditing(!isEditing);
                    setItem(res.data.row);
                }
            }, function (res) {
                setLoading(false);
            });
        } else {
            setIsEditing(!isEditing);
        }
    };

    const handleCellChange = (colName, value) => {
        setItem(getMergedItem(colName, value));
    };

    const getMergedItem = (colName, value) => {
        let merged = {...item};
        if (!_.isUndefined(colName)) {
            merged[colName] = value;
        }

        const ele_cust = get_customer_ele(item.order_id);
        if (ele_cust.length > 0) {
            merged.customerName = ele_cust.val();
            merged.customer_id = merged.customerName ? ele_cust.attr('selected-val') : '';
        }
        const ele_offer = get_offer_ele(item.order_id);
        if (ele_offer.length > 0) {
            merged.offerName = ele_offer.val();
            merged.offer_id = merged.offerName ? ele_offer.attr('selected-val') : '';
        }
        // date-picker
        $(`tr#tr-${item.order_id} td input.d-picker`).each((ind, ele) => {
            merged[ele.name] = ele.value;
        });
        return merged;
    };

    const viewCustomerComments = () => {
        // Open a modal dialog
        setDlgTitle($g_dlg, item.name + "'s Comments");
        setDlgBody($g_dlg, no_result);
        $g_dlg.modal({
            'backdrop': 'static',
            'show': true
        });

        App.ajax_post_ok(get_ajax_url('CustomerAction', 'view_comments'), {
            customer_id: item.customer_id,
            order_id: item.order_id,
            //type: type || ''
        }, function (res) {
            if (!res.error) {
                setDlgBody($g_dlg, res.data.html);
            }
        }, $g_dlg);
    }

    const createCustomerComment = () => {
        let url = base_url + '/customer.php';
        if (item.customer_id) {
            url += `?customer_id=${item.customer_id}`;
        }
        window.open(url, '_blank');
    };

    const createSupplierComment = () => {
        const comments = item.supplier_comments || [];
        let url = base_url + '/supplier.php';
        if (comments && comments.length) {
            // select the customer
            const c_list = comments.filter(x => (x.offer_id || '').length == 15);
            if (c_list.length > 0) {
                url += `?customer_id=${c_list[0].customer_id}&offer_id=${c_list[0].offer_id}`;
            }
        }
        window.open(url, '_blank');
    };

    const updateInfo = () => {
        OrderInfoFormHandler(null, item, (dlg, res) => {
            if (res.error == false) {
                setItem(res.data.row);
                dlg.hide();
            }
        });
    };

    const changeCommentStatus = (e, s, key, rightClick) => {
        let rightClicked = false;
        if (!_.isUndefined(rightClick) && rightClick == 1) {
            e.preventDefault();
            rightClicked = true;
            // We don't use mouse double right click
            /*RightClick.active();
            if (RightClick.count == 2) {
                rightClicked = true;
            } else {
                return;
            }*/
        }

        const comment = _.first(_.filter(item[key], {sc_id: s.id}));
        const new_status = App.getNextValue(comment ? comment.status : null,
            1 == rightClicked ? ['0', '2', '1'] : ['0', '1', '2']);
        updateOrderComments({
            order_id: item.order_id,
            comments: [
                {
                    id: comment ? comment.id : null,
                    order_id: item.order_id,
                    sc_id: s.id,
                    status: new_status
                }
            ]
        }, res => {
            if (!res.error) {
                setItem(res.data.row);
            }
        }, {blockEle: $(e.currentTarget).closest('td')});

    };

    const onClickCreateComment = e => {
        OrderInvoicesCommentFormHandler(e, {
            order_id: item.order_id,
            comment: item.invoices_comment
        }, (dlg, res) => {
            if (!res.error) {
                setItem(prev => {
                    let newItem = {...prev};
                    newItem.invoices_comment = res.data.order_comment.comment;
                    return newItem;
                });
            }
        });
    };

    const onClickCreateInvoice = (e, invoice) => {
        InvoiceFormHandler(e, _.isUndefined(invoice) ? {order_id: item.order_id} : invoice, (dlgUpdateInfo, res) => {
            if (!res.error) {
                setItem(prev => {
                    let newItem = {...prev};
                    if (!_.isUndefined(invoice)) {
                        _.remove(newItem.order_invoices, el => el.id == invoice.id);
                    }
                    newItem.order_invoices = [...newItem.order_invoices, res.data.row];
                    return newItem;
                });
            }
        });
    };

    const onDoubleClickTD = (e, colName) => {
        let updateNeed = false;
        let data = {
            old_order_id: item.order_id,
            data: {
                order_id: item.order_id,
            }
        };
        if (colName == 'warehouse') {
            updateNeed = true;
            data.data[colName] = item[colName] == 1 ? 0 : 1;
        } else if (colName == 'category') {
            updateNeed = true;
            data.data[colName] = App.getNextValue(item.category, [
                OrderCategoryTypes.FD,
                OrderCategoryTypes.ND,
                OrderCategoryTypes.TK,
                OrderCategoryTypes.TCM,
                OrderCategoryTypes.NONE,
            ]);
        }
        if (updateNeed) {
            OrderBiz.updateOrderInfo(data, res => {
                if (!res.error) {
                    setItem(res.data.row);
                }
            }, {blockEle: $(e.target).closest('td')});
        }
    };

    const getIconCls = status => {
        if (status == '0') return ' oi-x red';
        else if (status == '1') return ' oi-circle-check';
        else if (status == '2') return ' oi-ban c-lightgrey-d';
        else return '';
    };

    const getStatusBgCls = (s, type) => {
        const status = getCommentValuesById(s, type);
        if (s.order == 1000) return '';
        if (status == '0') return '';
        else if (status == '1') return ' light-grey-status';
        else if (status == '2') return ' light-grey-status';
        else return '';
    };

    const getCommentValuesById = (s, type) => {
        const comments = type == 'supplier' ? item.order_supplier_comments : item.order_customer_comments;
        const x = _.first(_.filter(comments, {sc_id: s.id}));
        if (x) {
            return x.status;
        }
        return null;
    };

    const getSupplierCommentsById = (s) => {
        return _.map(_.filter(item.order_supplier_comments, {sc_id: s.id}), (x, ind) => {
            const statusList = _.split(x.status, ',');
            return _.map(statusList, (n, ind) => <div key={ind} style={{width: 15}}>
                <i className={"tt oi" + getIconCls(n)}
                   style={{cursor: 'pointer'}}
                   title={x.comment}></i>
            </div>);
        });
    };

    const getCustomerCommentsById = (s) => {
        return _.map(_.filter(item.order_customer_comments, {sc_id: s.id}), (x, ind) => {
            const statusList = _.split(x.status, ',');
            return _.map(statusList, (n, ind) => <div key={ind} style={{width: 15}}>
                <i className={"tt oi" + getIconCls(n)}
                   style={{cursor: 'pointer'}}
                   title={x.comment}></i>
            </div>);
        });
    };

    const isWarehouseGrey = () => {
        // we make grey background by 50 + 60 status.
        let flag = true;
        let statusId50 = _.get(_.first(_.filter(customerStatuses, {order: "50"})), 'id');
        let statusId60 = _.get(_.first(_.filter(customerStatuses, {order: "60"})), 'id');
        flag &= _.findIndex(item.order_customer_comments, x => x.sc_id == statusId50 && x.status == '1') >= 0;
        flag &= _.findIndex(item.order_customer_comments, x => x.sc_id == statusId60 && x.status == '1') >= 0;
        if (flag) return true;

        flag = true;
        statusId50 = _.get(_.first(_.filter(supplierStatuses, {order: "50"})), 'id');
        statusId60 = _.get(_.first(_.filter(supplierStatuses, {order: "60"})), 'id');
        flag &= _.findIndex(item.order_supplier_comments, x => x.sc_id == statusId50 && x.status == '1') >= 0;
        flag &= _.findIndex(item.order_supplier_comments, x => x.sc_id == statusId60 && x.status == '1') >= 0;
        return flag;
    };

    const getTDHtml = (colName) => {
        const value = item[colName] || '';
        const colType = RHelper.getColType(colName, colsDefs);
        const isEditableCol = RHelper.isEditable(colName, colsDefs);
        const isNumericCol = RHelper.isNumeric(colName, colsDefs);

        let html = '';
        let td_cls = colName;
        switch (colName) {
            case 'org_a':
            case 'status':
            case 'we':
            case 'warehouse':
            case 'customer_id':
            case 'loading_date':
            case 'est_loading_date':
            case 'est_loading_date2':
            case 'supplier_comments_statuses':
            case 'customer_comments_statuses':
                td_cls += ' text-center';
                break;
        }

        if (isEditableCol && (isEditing || (colName == 'customer_id' && _.isEmpty(item.customer_id)))) {
            if (mode == 'invoice') {
                if (colName == 'supplier_comments_statuses' || colName == 'customer_comments_statuses') return <></>;
            } else {
                if (colName == 'invoices' || colName == 'act_invoices') return <></>;
            }

            let cls = "form-control form-control-sm" + ' ' + colName;
            if (isNumericCol) cls += ' text-right';
            if (colType == 'dt') cls += ' d-picker';

            switch (colName) {
                case 'status':
                    td_cls += ' text-center';
                    break;
                case 'customer_id':
                    td_cls += ' text-left';
                    break;
                case 'offer_id':
                    td_cls += ' text-left';
                    break;
                case 'est_loading_date':
                case 'est_loading_date2':
                case 'loading_date':
                    td_cls += 'text-center';
                    break;
            }

            switch (colName) {
                case 'status':
                    html = (<select className={cls}
                                    value={value || '0'}
                                    name={colName}
                                    onChange={e => handleCellChange(colName, e.target.value)}
                    >
                        <option value={1}>Yes</option>
                        <option value={0}>No</option>
                    </select>);
                    break;
                case 'category':
                    html = HtmlHelper.dropdown(
                        colName,
                        OrderCategoryTypesDropdown,
                        value,
                        e => handleCellChange(colName, e.target.value),
                        {}
                    );
                    break;
                case 'customer_id':
                case 'offer_id':
                    html = (<div className="input-wrap-with-icon">
                        <input className={cls}
                               type="text"
                               disabled={loading}
                               name={colName}
                               value={_.get(item, colName == 'customer_id' ? 'customerName' : 'offerName', '') || ''}
                               selected-val={value}
                               onChange={e => handleCellChange(colName, e.target.value)}
                        />
                    </div>);
                    break;
                case 'order_id':
                    html = <>
                        <input className={cls + " d-inline-block"} type="text"
                               disabled={loading}
                               name={colName}
                               value={value}
                               onChange={e => handleCellChange(colName, e.target.value)}
                               style={{width: RHelper.getColWidth(colName, colsWidth) - 15}}
                        />
                        <i className={"oi oi-check float-right ml-0 mt-2"}
                           onClick={handleEditButton} title={"Please click to save order."}></i>
                    </>;
                    break;
                case 'est_loading_date':
                    html = <>
                        <input className={cls} type="text"
                               disabled={loading}
                               name={colName}
                               value={value}
                               onChange={e => handleCellChange(colName, e.target.value)}
                               autoComplete={'off'}
                        />
                        <input className={cls} type="text"
                               disabled={loading}
                               name={"est_loading_date2"}
                               value={item.est_loading_date2 || ''}
                               onChange={e => handleCellChange('est_loading_date2', e.target.value)}
                               autoComplete={'off'}
                        />
                    </>;
                    break;
                default:
                    html = (
                        <input className={cls} type="text"
                               disabled={loading}
                               name={colName}
                               value={value}
                               onChange={e => handleCellChange(colName, e.target.value)}
                               maxLength={colName == 'au_master' ? 10 : ''}
                               autoComplete={colType == 'dt' ? 'off' : 'on'}
                        />);
                    break;
            }
        } else {
            let val = value || '';
            if (val != null && colType == 'dt') val = CvUtil.dtNiceDMY(val);

            if (colName == 'customer_id') {
                val = item.customerName || '';
            } else if (colName == 'offer_id') {
                val = <span className="fs-z6 tt" title={item.offerName}>{item.offer_sid || ''}</span>;
            } else if (colName == 'est_loading_date') {
                const loadingDate = CvUtil.momentWithTz(item.loading_date);
                // If Loading Date is not set?
                if (loadingDate) {
                    td_cls += ' bg-none';
                } else {
                    const estDate = item.est_loading_date || item.est_loading_date2 || null;
                    const m = CvUtil.momentWithTz(estDate);
                    let mToday = moment();
                    const mTodayYMD = CvUtil.dtValidYMD(mToday);
                    mToday = moment(mTodayYMD);
                    if (m) {
                        const diffDays = m.diff(mToday, 'days');
                        if (m.isSame(mToday, 'day')) {
                            td_cls += ' orange';
                        } else if (diffDays < 0) {
                            td_cls += ' light-grey';
                        } else {
                            if (diffDays == 1) {
                                td_cls += ' yellow';
                            } else {
                                if (m.isSame(mToday, 'isoWeek')) {
                                    td_cls += ' light-blue';
                                }
                            }
                        }
                    }
                }
                val = <>
                    <div>{val}</div>
                    <div>{CvUtil.dtNiceDMY(item.est_loading_date2)}</div>
                </>;
            /*} else if (colName == 'customer_comments') {
                val = <React.Fragment>
                    <div className="position-relative pr-3">
                        {value.map((c, ind) => <div key={ind} className="form-row">
                            <div className="col-3">{CvUtil.dtNiceDMY(c.created_on)}</div>
                            <div className="col-9">
                                {c.comment}
                            </div>
                        </div>)}
                        {<div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                            <i className={"oi oi-info m-0" + (value && value.length > 0 ? " blue-f" : "")}
                               onClick={viewCustomerComments} title="Please click to view customer comments."></i>
                            {<i className={"oi oi-comment-square ml-1 tt"} onClick={createCustomerComment}
                                title="Please click to create a comment."></i>}
                        </div>}
                    </div>
                </React.Fragment>;
            } else if (colName == 'supplier_comments') {
                val = <div className="position-relative pr-3">
                    {value.map((c, ind) => <div key={ind} className="form-row">
                        <div className="col-3 ">{CvUtil.dtNiceDMY(c.created_on)}</div>
                        <div className="col-9">
                            {c.comment}
                        </div>
                    </div>)}
                    <div className="position-absolute" style={{top: '50%', right: 0, marginTop: -7}}>
                        {/!*<i className={"oi oi-info m-0" + (value && value.length > 0 ? " blue-f" : "")} onClick={createSupplierComments} title="Please click to view all comments."></i>*!/}
                        {<i className={"oi oi-comment-square tt"} onClick={createSupplierComment}
                            title="Please click to create a comment."></i>}
                    </div>
                </div>;*/

            } else if (colName == 'supplier_comments_statuses') {
                if (mode == 'invoice') return '';
                return supplierStatuses.map((s, ind) => {
                    let gap = '';
                    if (s.ui_border == 1) {
                        gap = <td key={s.id + 'g'}   className="gap"><div></div></td>;
                    }
                    return <>{gap}
                        <td key={s.id}
                            className={td_cls + (s.ui_border == 1 || ind == 0 || s.order == 500 || s.order == 1000 ? ' sep-l' : '') + getStatusBgCls(s, 'supplier') + (s.order>500 ? ' yellow-wh' : '')}
                            onClick={e => changeCommentStatus(e, s, 'order_supplier_comments')}
                            onContextMenu={e => changeCommentStatus(e, s, 'order_supplier_comments', 1)}
                        >
                            {getSupplierCommentsById(s)}
                        </td>
                    </>
                });
            } else if (colName == 'customer_comments_statuses') {
                if (mode == 'invoice') return '';
                return customerStatuses.map((s, ind) => {
                    let gap = '';
                    if (s.ui_border == 1) {
                        gap = <td key={s.id + 'g'}   className="gap"><div></div></td>;
                    }
                    return <>{gap}
                        <td key={s.id} className={td_cls + (s.ui_border == 1 || ind == 0 ? ' sep-l' : '') + getStatusBgCls(s, 'customer') + ' yellow-wh'}
                            onClick={e => changeCommentStatus(e, s, 'order_customer_comments')}
                            onContextMenu={e => changeCommentStatus(e, s, 'order_customer_comments', 1)}
                        >
                            {getCustomerCommentsById(s)}
                        </td>
                    </>
                });

            } else if (colName == 'name') {
                const customer_comments = _.get(item, 'customer_comments', []);
                val = <React.Fragment>
                    <span className="supplier-info"> {item.name || ''}</span>
                    {customer_comments && customer_comments.length > 0 &&
                    <i className={"oi oi-info m-0 float-right" + (customer_comments.length > 0 ? " blue-f" : "")}
                       onClick={viewCustomerComments} title="Please click to view customer comments."></i>}
                </React.Fragment>;
            } else if (colName == 'order_id') {
                const comments = _.get(item, 'supplier_comments', []);
                const createdTitle = CvUtil.dtValidYMD(item.created_on) ? ('Created on ' + CvUtil.dtNiceDMY(item.created_on)) : '';
                const title = (createdTitle || item.info_comment) ? "<div>" + createdTitle + "</div><div>" + (item.info_comment || '') + "</div>" : '';
                val = <React.Fragment>
                    <span className="tt"
                          title={title}> {value}</span>
                    <i className={"oi m-0 float-right oi-pencil oi-edit"}
                       onClick={handleEditButton} title={"Please click to edit order."}></i>
                    {/*{item.cust_complaint && item.cust_complaint_solved == 1 && (<i className={"oi oi-circle-check float-right mr-1 tt"} style={{color: 'orange'}} title="Customer complaint exists and solved."></i>)}*/}
                </React.Fragment>;
            } else if (colName == 'action_update_info') {
                val = <i className={"oi oi-info m-0 tt float-right" + (item.info_comment ? " blue-f" : "")}
                         onClick={updateInfo} title={item.info_comment || ''}></i>;
            } else if (colName == 'action_btn') {
                td_cls += ' text-center';
                val = <React.Fragment>
                    <button className="btn btn-sm btn-sm-td btn-outline-success" onClick={handleEditButton}
                            disabled={loading}>{isEditing ? 'Save' : 'Edit'}</button>
                </React.Fragment>;
            } else if (colName == 'cust_complaint_solved') {
                val = item.cust_complaint &&
                    <i className={"oi tt" + (item.cust_complaint_solved == '1' ? ' oi-circle-check grey' : ' oi-circle-x red')}
                       title={item.cust_complaint || ''/*item.cust_complaint_solved == '1' ? 'Complaint solved' : 'Customer complaint needs to solve.'*/}></i>;
            } else if (colName == 'supplier_status' || colName == 'customer_status') {
                val = <OrderStatusCell
                    item={item}
                    setItem={setItem}
                    type={colName}
                    statuses={colName == 'supplier_status' ? supplierStatuses : customerStatuses}
                />;
            } else if (colName == 'act_invoices') {
                if (mode != 'invoice') return '';
                val = (
                    <div className="fs-z6 text-center" style={{cursor: 'pointer'}}>
                        <span className="badge badge-success tt" title="Create an invoice"
                              onClick={onClickCreateInvoice}>IV</span>
                        <br/>
                        <span className="badge badge-success tt" title="Create a comment"
                              onClick={onClickCreateComment}>C</span>
                    </div>
                );
            } else if (colName == 'invoices') {
                if (mode != 'invoice') return '';

                return _.map(Object.values(InvoiceTypes), (s, ind) => (
                    <td key={s}>
                        <div style={{width: RHelper.getColWidth(colName, colsWidth)}}>
                            <OrderInvoiceCell
                                order_id={item.order_id}
                                invoices={_.filter(item.order_invoices, {type: s})}
                                invoicesComment={item.invoices_comment || ''}
                                onClickCreateComment={onClickCreateComment}
                                onClickCreateInvoice={onClickCreateInvoice}
                            />
                        </div>
                    </td>
                ));
            } else if (colName == 'category') {
                td_cls += ' cursor-p';
            } else if (colName == 'warehouse') {
                td_cls += ' cursor-p';
                if (isWarehouseGrey()) td_cls += ' light-grey';
                val = (item.warehouse == 1 && <i className="oi oi-circle-check"></i>);
            } else if (colName == 'expected_volume') {

            }
            html = val;
        }
        return <td
            key={colName}
            className={td_cls}
            onClick={e => onDoubleClickTD(e, colName)}
        >
            <div style={{width: _.get(colsWidth, colName, '')}} onDoubleClick={e => {
                if (!isEditing) {
                    if (colName == 'est_loading_date'
                        || colName == 'est_loading_date2'
                        || colName == 'loading_date'
                        || colName == 'expected_volume'
                    ) {

                    }
                }
            }}>{html}</div>
        </td>;
    };

    let tr_cls = '';
    if (item.warehouse == 1) {
        tr_cls = ' light-yellow-wh';
    }
    return (
        <tr id={"tr-" + item.order_id} className={tr_cls}>
            {props.cols.map(col => getTDHtml(col))}
        </tr>
    );
};

export default TableRow;