<?php

/**
 * Class OfferModel
 */
class OfferModel extends BaseModel
{
    const STATUS_TYPE_SUPPLIER = 'supplier';
    const STATUS_TYPE_INTERNAL = 'internal';
    const STATUS_TYPE_CUSTOMER = 'customer';

    const STATUS_TYPES = [self::STATUS_TYPE_SUPPLIER, self::STATUS_TYPE_INTERNAL, self::STATUS_TYPE_CUSTOMER];

    protected function initialize()
    {
        $this->table = BaseModel::TBL_OFFERS;
    }

    /**
     * Get offer list.
     *
     * @param $params
     * @param array $with
     * @param null $assoc_field
     * @return array
     */
    public function get_list($params, $with = [], $assoc_field = null)
    {
        $select_from = ' offers o';
        $select_with = ', s.name AS supplierName,s.org_a';

        $where = $this->_where_list($params, $with);

        if ($this->calc_count()) {
            $sql = "
                SELECT COUNT(*)
                FROM $select_from
                    LEFT JOIN suppliers s ON s.id=o.supplier_id
                    LEFT JOIN offer_info info ON info.offer_id = o.id     
                WHERE 1 $where
                ;";
            $this->db->append_query_history($sql);
            return $this->db->query_scalar($sql);
        }

        $order_by = $this->_order_by_list($params, $with);
        $limit_str = $this->limit_by_pager();
        $sql = "
            SELECT 
                o.* $select_with      
                , info.comment AS info_comment          
            FROM $select_from
                LEFT JOIN suppliers s ON s.id=o.supplier_id
                LEFT JOIN offer_info info ON info.offer_id = o.id     
            WHERE 1 $where
            $order_by
            $limit_str
        ";
        $this->db->append_query_history($sql);
        $rows = $assoc_field ? $this->db->query_select_manualkey($sql, $assoc_field) : $this->db->query_select($sql);

        $offer_ids = $rows ?
            array_map(function ($n) {
                return $n['id'];
            }, $rows) : [];
        // Offers
        // ---------------------------------------------------------------------------
        /*$cc_model =& Loader::get_instance()->load_model('CustomerCommentModel');
        foreach ($rows as $ind => $row) {
            $offer_id = $row['id'];
            if ($offer_id) {
                $rows[$ind]['customer_comments'] = $cc_model->get_list([
                    'offer_id' => $offer_id,
                    'limit' => 5,
                ]);
            } else {
                $rows[$ind]['customer_comments'] = [];
            }
        }

        // Orders
        // ---------------------------------------------------------------------------
        $sc_model =& Loader::get_instance()->load_model('SupplierCommentModel');
        foreach ($rows as $ind => $row) {
            $offer_id = $row['id'];
            if ($offer_id) {
                $rows[$ind]['supplier_comments'] = $sc_model->get_list([
                    'offer_id' => $offer_id,
                    'limit' => 5,
                ]);
            } else {
                $rows[$ind]['supplier_comments'] = [];
            }
        }*/

        // Offer comments
        // ---------------------------------------------------------------------------
        /*if ($offer_ids) {
            $offer_comment_model =& Loader::get_instance()->load_model('OfferCommentModel');
            $comments = $offer_comment_model->get_list(['offer_ids' => $offer_ids, 'limit' => 3], ['top3_comments'], 'offer_id');
            foreach ($rows as $ind => $row) {
                $offer_id = $row['id'];
                $rows[$ind]['top3_comments'] = $comments[$offer_id] ?? [];
            }
        }*/

        if ($offer_ids) {
            $offer_comment_model =& Loader::get_instance()->load_model('OfferCommentModel');
            $comments = $offer_comment_model->get_list(['offer_ids' => $offer_ids, 'limit' => 3], ['top3_comments_by_types'], 'offer_id');
            foreach ($rows as $ind => $row) {
                $offer_id = $row['id'];
                foreach (self::STATUS_TYPES as $st) {
                    $rows[$ind]['top3_comments_' . $st] = $comments[$offer_id][$st] ?? [];
                }
            }
        }
        return $rows;
    }

    /**
     * Select a single row by ID.
     *
     * @param $id
     * @return mixed|null
     */
    public function get_row($id)
    {
        $rows = $this->get_list(['id' => $id]);
        if ($rows) {
            return current($rows);
        }
        return null;
    }

    private function _where_list(&$params, &$with)
    {
        $where = '';
        $id = $params['id'] ?? '';
        $no_supplier = $params['noSupplier'] ?? 0;
        $orgAList = $params['orgAList'] ?? [];

        if ($id) {
            $where .= $this->db->where_equal('o.id', $id);
        } else {
            $where .= $this->db->where_equal('o.supplier_id', $params['supplier_id'] ?? '');
            $where .= $this->db->where_like('o.offer_sid', $params['offer_sid'] ?? '', true);
            $where .= $this->db->where_like('o.offer', $params['offer'] ?? '', true);
            $where .= $this->db->where_equal('o.status', $params['status'] ?? '');
            $where .= $this->db->where_equal('o.warehouse', $params['warehouse'] ?? '');
            $where .= $this->db->where_like('o.we', $params['we'] ?? '', true);
            $where .= $this->db->where_like('LEFT(o.created_on, 10)', $params['created_on'] ?? '', true);
            $where .= $this->db->where_like('LEFT(o.updated_on, 10)', $params['updated_on'] ?? '', true);

            $where .= $this->db->where_like('s.name', $params['name'] ?? '');
            $where .= $this->db->where_like('s.name', $params['supplier_name'] ?? '');
            $where .= $this->db->where_like('s.org_a', $params['org_a'] ?? '');

            if (!$no_supplier) {
                if ($orgAList !== -1)
                    $where .= $this->db->where_in("IFNULL(s.org_a, '')", $orgAList, 'AND', true);
            }

            $top3_comments = $params['top3_comments'] ?? null;
            if ($top3_comments) {
                $where .= " AND EXISTS (SELECT 1 FROM offer_comments WHERE offer_id=o.id " . $this->db->where_like('comment', $top3_comments, TRUE) . " LIMIT 1)";
            }

            foreach (self::STATUS_TYPES as $st) {
                $st_param = $params['top3_comments_' . $st] ?? null;
                if ($st_param) {
                    $where .= " AND EXISTS (SELECT 1 FROM offer_comments, sys_config sc WHERE offer_id=o.id AND sc.id=sc_id AND sc.off_status_type='$st' " . $this->db->where_like('comment', $st_param, TRUE) . " LIMIT 1)";
                }
            }
        }
        $where .= $this->db->where_equal('o.supplier_id', $params['supplier_id'] ?? '');
        if ($no_supplier) {
            $where .= " AND s.id IS NULL";
        }

        if (($params['spread_out'] ?? '') !== '') {
            $where .= " AND o.spread_out=" . intval($params['spread_out']);
        }
        if (($params['brand'] ?? '') !== '') {
            $where .= " AND o.brand=" . intval($params['brand']);
        }

        return $where;
    }

    private function _order_by_list(&$params, &$with)
    {
        $order_field = $params['order_field'] ?? 'id';
        $order_dir = $params['order_dir'] ?? 'desc';

        switch ($order_field) {
            case 'supplier_id':
                $order_by_str = 's.name';
                break;
            default:
                $order_by_str = 'o.' . $order_field;
                break;
        }

        if ($order_by_str && $order_dir) $order_by_str .= ' ' . $order_dir;
        return $order_by_str ? ' ORDER BY ' . $order_by_str : '';
    }
}