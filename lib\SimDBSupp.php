<?php


use app\SysMsg\SysMsg;

/**
 * Class SimDBSupp
 *
 * Simple Database library for WHC_Supplier database
 *
 * Implemented the singleton design pattern.
 */
class SimDBSupp
{
    private static $_instance = NULL;
    public $last_error_msg = NULL;
    protected $queries = array();
    private $trigger_enabled = true;

    private function __construct()
    {
    }

    /**
     * Get the singleton object
     */
    public static function &get_instance()
    {
        if (self::$_instance == NULL) {
            self::$_instance = new SimDBSupp();
            // WHC_Supplier ENV
            $DB_HOST_SUPPLIER = $_ENV['DB_HOST_SUPPLIER'];
            $DB_USER_SUPPLIER = $_ENV['DB_USER_SUPPLIER'];
            $DB_PASSWORD_SUPPLIER = $_ENV['DB_PASSWORD_SUPPLIER'];
            $DB_NAME_SUPPLIER = $_ENV['DB_NAME_SUPPLIER'];
            $DB_PORT_SUPPLIER = $_ENV['DB_PORT_SUPPLIER'];

            self::$_instance->initialize(
                $_ENV['DB_HOST_SUPPLIER'],
                $_ENV['DB_USER_SUPPLIER'],
                $_ENV['DB_PASSWORD_SUPPLIER'],
                $_ENV['DB_NAME_SUPPLIER'],
                $_ENV['DB_PORT_SUPPLIER']
            );
        }

        return self::$_instance;
    }

    /** Relative path to error logs folder
     * When found a database error, it will store the log file to this folder
     */
    public $LOGS_PATH = "./errorlogs";

    public function init_queries()
    {
        $this->queries = array();
    }

    public function append_query_history($sql = '')
    {
        if ($sql) {
            $this->queries[] = $sql;
        } else {
            $this->queries[] = $this->last_query;
        }
    }

    public function get_query_history()
    {
        return $this->queries;
    }

    public function show_query_history($print = true, $raw = true)
    {
        $sql = implode("\n-- " . str_repeat("=", 50) . "\n", $this->get_query_history());
        $str = '';
        if (!$raw)
            $str = "<div class='sql-log-wrap'><pre>";
        $str .= $sql;
        if (!$raw)
            $str .= "</pre></div>";
        if ($print)
            echo $str;
        return $str;
    }

    /** Server information */
    private $HOST;
    private $USERNAME;
    private $PASSWORD;
    private $DATABASE;
    private $PORT;

    /** Slashes handling, default is TRUE */
    public $handleSlashes = TRUE;

    /** Connection link */
    private $conn = "";

    /** Last database link has been made */
    private $last_link = "";

    /** Last query made, for error logging purpose */
    private $last_query = "";

    /**
     * When you want to insert an expression to the query, to avoid SimDB
     * auto wrapping quotation to the string, use this function.
     *
     * E.g.:
     *
     * $student = array(
     *    'name'    => "Johny",
     *    'created' => SimDBSupp::createExpression('NOW()')
     * );
     * $db->insert("students", $student);
     *
     */
    public static function createExpression($sql_expression)
    {
        return new SimDBSuppExpressionType($sql_expression);
    }

    public static function E($sql_expression)
    {
        return new SimDBSuppExpressionType($sql_expression);
    }

    public function initialize($host = "", $user = "", $pw = "", $db = "", $port = 3306)
    {
        $this->HOST = $host;
        $this->USERNAME = $user;
        $this->PASSWORD = $pw;
        $this->DATABASE = $db;
        $this->PORT = $port;
        $this->connect();
    }

    public function &get_last_link()
    {
        return $this->last_link;
    }

    /**
     * Connect to database.
     * Also try to select database if possible.
     *
     */
    public function connect()
    {
        $this->conn = @mysqli_connect($this->HOST, $this->USERNAME, $this->PASSWORD, $this->DATABASE, $this->PORT);
        if (!$this->conn) {
            die('Error connecting to mysql server.');
        }

        // Change character set to utf8
        mysqli_set_charset($this->conn, "utf8");

        return $this->conn;
    }

    public function get_connection()
    {
        return $this->conn;
    }

    public function get_escape_string($value)
    {
        return mysqli_real_escape_string($this->conn, $value);
    }

    public function begin_transaction($link = null)
    {
        if ($this->conn) {
            mysqli_autocommit($this->conn, FALSE);
            mysqli_begin_transaction($this->conn);
        }
    }

    public function commit($link = null)
    {
        if ($this->conn) {
            mysqli_commit($this->conn);
            mysqli_autocommit($this->conn, TRUE);
        }
    }

    public function rollback($link = null)
    {
        if ($this->conn) {
            mysqli_rollback($this->conn);
            mysqli_autocommit($this->conn, TRUE);
        }
    }


    /**
     * Change current database.
     * Assumption: connection already made.
     *
     */
    public function select_db($database = "")
    {
        if ($database == "") {
            $database = $this->DATABASE;
        }

        if ($database != "") {
            if (!@mysqli_select_db($database, $this->conn)) {
                $this->log_error(@mysqli_error($this->conn));
                die('Error selecting database ' . $database);
            } else {
                // Successful
            }
        }
    }

    /**
     * Return a list of databases.
     * Assumption: connection already made.
     *
     * @return array
     */
    public function get_databases()
    {
        $dbs = $this->query_select("SHOW DATABASES;");
        if (!function_exists('__tmp_huy_db_databases')) {
            function __tmp_huy_db_databases($val)
            {
                return $val['Database'];
            }
        }
        return array_map('__tmp_huy_db_databases', $dbs);
    }

    /**
     * Get list of tables available in current database.
     *
     * @return array
     */
    public function get_tables()
    {
        $tables = $this->query_select("SHOW TABLES;", "num");
        if (!function_exists('__tmp_huy_db_tables')) {
            function __tmp_huy_db_tables($val)
            {
                return $val[0];
            }
        }
        return array_map('__tmp_huy_db_tables', $tables);
    }

    /**
     * General function, do a query
     *
     */
    public function query($sql)
    {
        if (!$this->conn) {
            $this->connect();
        }
        $this->last_link = mysqli_query($this->conn, $sql);
        $this->last_query = $sql;

        if (!$this->last_link) {
            $error_msg = mysqli_error($this->conn) . ' SQL:' . $sql;
            $this->log_error($error_msg);
            $this->last_error_msg = $error_msg;
        } else {
            /**
             * Hook to monitor db changes.
             *
             * success, so we need to log a db changes
             */
            if ($this->trigger_enabled) {
                list($type, $table_name) = $this->_sql_parser($sql);
                if ($type != 'SELECT' && $table_name && !in_array($table_name, BaseModel::EXCLUDED_IN_SYNC)) {
                    $this->_trigger_transaction($type, $table_name, $sql);
                }
            }
        }

        return $this->last_link;
    }

    public function _sql_parser($sql)
    {
        $sql = trim($sql);

        $token = " \n\t\r";
        $result = [];
        $result[0] = strtok($sql, $token);
        $result[0] = strtoupper($result[0]);
        if ($result[0] == 'SELECT') {
            return [$result[0], null];
        }

        $result[1] = strtok($token);
        $result[2] = strtok($token);
        $result[3] = strtok($token);

        $type = '';
        $table_name = '';
        if ($result[0] == 'SELECT') {
            return [$result[0], $table_name];
        } else {
            if ($result[0] == 'INSERT' || $result[0] == 'REPLACE') {
                if (strtoupper($result[1]) == 'INTO') {
                    $type = $result[0];
                    $table_name = $result[2];
                }
            } else if ($result[0] == 'UPDATE') {
                $type = $result[0];
                $table_name = $result[1];
            } else if ($result[0] == 'DELETE') {
                if (strtoupper($result[1]) == 'FROM') {
                    $type = $result[0];
                    $table_name = $result[2];
                }
            }
        }
        $table_name = trim($table_name, " `\t\n\r\0\x0B");
        return [$type, $table_name];
        //preg_match("/php/i");
    }

    /**
     * Do a SELECT query and return the resultset.
     *
     * @param mixed $sql sql string
     */
    public function query_select($sql, $type = "assoc")
    {
        $this->query($sql);
        $ans = array();
        while ($r = $this->fetch_array($this->last_link, $type)) {
            $ans[] = $r;
        }
        $this->free_result();

        return $ans;
    }

    /*
     * Same as query_select but returns an array with keys as specified by the
     * parameter $manualkey (must be a valid column from the database)
     *
     * @param
     * @return
     */
    public function query_select_manualkey($sql, $manualkey, $type = "assoc")
    {
        $this->query($sql);
        $ans = array();
        while ($r = $this->fetch_array($this->last_link, $type)) {
            $ans[$r[$manualkey]] = $r;
        }
        $this->free_result();

        return $ans;
    }

    /**
     * @param $sql
     * @param $manualkey
     * @param string $type
     * @return array
     */
    public function query_select_manualkey_deep($sql, $manualkey, $type = "assoc")
    {
        $this->query($sql);
        $ans = array();
        while ($r = $this->fetch_array($this->last_link, $type)) {
            if (!isset($ans[$r[$manualkey]]))
                $ans[$r[$manualkey]] = [];
            $ans[$r[$manualkey]][] = $r;
        }
        $this->free_result();

        return $ans;
    }

    /**
     * e.g. ['manualKey' => [ ['manualKey2'] => row ] ]
     *
     * @param $sql
     * @param $manualkey
     * @param $manualkey2
     * @param string $type
     * @return array
     */
    public function query_select_manualkey_deep2($sql, $manualkey, $manualkey2, $type = "assoc")
    {
        $this->query($sql);
        $ans = [];
        while ($r = $this->fetch_array($this->last_link, $type)) {
            if (!isset($ans[$r[$manualkey]]))
                $ans[$r[$manualkey]][$r[$manualkey2]] = [];
            $ans[$r[$manualkey]][$r[$manualkey2]][] = $r;
        }
        $this->free_result();

        return $ans;
    }

    /**
     * @param $sql
     * @param $manualkey
     * @param string $type
     * @return array
     */
    public function query_select_manualkey2($sql, $manualkey, $manualkey2, $type = "assoc")
    {
        $this->query($sql);
        $ans = array();
        while ($r = $this->fetch_array($this->last_link, $type)) {
            $ans[$r[$manualkey]][$r[$manualkey2]] = $r;
        }
        $this->free_result();

        return $ans;
    }

    public function query_select_manualkey3($sql, $manualkey, $manualkey2, $manualkey3, $type = "assoc")
    {
        $this->query($sql);
        $ans = array();
        while ($r = $this->fetch_array($this->last_link, $type)) {
            $ans[$r[$manualkey]][$r[$manualkey2]][$r[$manualkey3]] = $r;
        }
        $this->free_result();

        return $ans;
    }

    public function query_select_dropdown($sql, $key_field, $value_field)
    {
        $this->query($sql);
        $ans = array();
        while ($r = $this->fetch_array($this->last_link, 'assoc')) {
            $ans[$r[$key_field]] = $r[$value_field];
        }
        $this->free_result();

        return $ans;
    }

    /**
     * Return only the first row
     *
     * @param mixed $sql
     * @param $type either 'assoc' or 'num' or 'both'. Default is 'assoc'
     * @deprecated
     * @see query_row
     */
    public function query_single($sql, $type = "assoc")
    {
        $this->query($sql);
        if ($r = $this->fetch_array($this->last_link, $type)) {
            $this->free_result();
            return $r;
        }
        return FALSE;
    }

    /**
     * Return only the first row of a SELECT statement
     */
    public function query_row($sql, $type = "assoc")
    {
        return $this->query_single($sql, $type);
    }

    /**
     * Query single result.
     * Usually used when doing SELECT COUNT.
     *
     * @param mixed $sql
     * @param $type either 'assoc' or 'num' or 'both'. Default is 'assoc'
     *
     * @return FALSE if NO RECORD
     */
    public function query_scalar($sql)
    {
        $this->query($sql);
        if ($ans = $this->fetch_array($this->last_link, "num")) {
            $this->free_result();
            return $ans[0];
        }
        return FALSE;
    }

    /**
     * Query and return array of 1 element each
     *
     * @deprecated
     * @see query_col
     * For eg: SELECT userid FROM users, will return array(uid,uid,..)
     */
    public function query_select_single($sql)
    {
        $tmp = $this->query_select($sql, "num");
        $ans = array();
        foreach ($tmp as $item) {
            $ans[] = $item[0];
        }
        return $ans;
    }

    /**
     * Query and return array of 1 element each
     * To replace query_select_single
     */
    public function query_col($sql)
    {
        return $this->query_select_single($sql);
    }

    /**
     * Fetch row by row (ASSOC)
     * Will return array with fieldname as key
     *
     * @param $type either 'assoc' or 'num' or 'both'. Default is 'assoc'
     */
    public function fetch_array($query_id = "", $type = "assoc")
    {
        if ($query_id == "") {
            $query_id = $this->last_link;
        }
        if ($type == "assoc") {
            $const = MYSQLI_ASSOC;
        } elseif ($type == "num") {
            $const = MYSQLI_NUM;
        } else {
            $const = MYSQLI_BOTH;
        }
        if ($query_id) {
            $row = mysqli_fetch_array($query_id, $const);
            return $row;
        } else {
            return array();
        }
    }

    /**
     * Fetch row by row (NUM)
     * Will return array with index as key
     *
     */
    public function fetch_num($query_id = "")
    {
        return $this->fetch_array($query_id, "num");
    }

    /**
     * Fetch row by row (ASSOC)
     * Will return array with index as key
     *
     */
    public function fetch_row($query_id = "")
    {
        return $this->fetch_array($query_id, "assoc");
    }

    /**
     * Get number of rows in resultset
     *
     */
    public function get_num_rows($query_id = "")
    {
        if ($query_id == "") {
            $query_id = $this->last_link;
        }
        return mysqli_num_rows($query_id);
    }

    /**
     * Get number of affected rows
     *
     */
    public function get_affected_rows()
    {
        return mysqli_affected_rows();
    }

    /**
     * Generate and execute an UPDATE statement
     *
     * @param mixed $table_name
     * @param mixed $updates
     * @param mixed $where
     * @return bool|mysqli_result|string
     */
    public function update($table, $data = array(), $where = "")
    {
        $statements = array();
        foreach ($data as $key => $value) {
            $statements[] = '`' . $key . '` = ' . $this->get_field_value($value);
        }
        $statements_str = implode(",", $statements);
        if ($where != "") {
            $where = " WHERE " . $where;
        }
        $query = "UPDATE `$table` SET $statements_str $where";
        return $this->query($query);
    }

    /**
     * Build and execute an insert query
     *
     * @param mixed $table
     * @param mixed $data
     * @return bool|int
     */
    public function insert($table, $data = array())
    {
        $fields = array();
        $values = array();

        foreach ($data as $key => $value) {
            $fields[] = $key;
            $values[] = $this->get_field_value($value);
        }

        $fields_str = implode("`,`", $fields);
        $values_str = implode(",", $values);

        $query = "INSERT INTO `$table` (`$fields_str`) VALUES ($values_str)";

        if ($res = $this->query($query)) {
            $lid = $this->get_insert_id();
            if (!$lid)
                return true;
            else
                return $lid;
        } else {
            return false;
        }
    }

    /**
     * Build and execute an insert query
     *
     * @param mixed $table
     * @param mixed $data
     * @return int
     */
    public function replace($table, $data = array())
    {
        $fields = array();
        $values = array();

        foreach ($data as $key => $value) {
            $fields[] = $key;
            $values[] = $this->get_field_value($value);
        }

        $fields_str = implode("`,`", $fields);
        $values_str = implode(",", $values);

        $query = "REPLACE INTO `$table` (`$fields_str`) VALUES ($values_str)";

        if ($res = $this->query($query)) {
            $lid = $this->get_insert_id();
            if (!$lid)
                return true;
            else
                return $lid;
        } else {
            return false;
        }
    }

    /**
     * Build and execute a batch-INSERT statement
     *
     * @param $entries
     * @return 1 if success, 0 if fail.
     *
     */
    public function insert_batch($table, $entries, $extra_suffix_sql = "")
    {
        if (sizeof($entries) == 0) {
            return -1;
        }

        $fields = array();
        $values = array(); // overall values


        /** First build the field names */
        $idx = 0;
        foreach ($entries[0] as $key => $value) {
            $fields[] = $key;
        }

        /** Now loop thru the entries, build the VALUES string. */
        foreach ($entries as $entry) {
            $entry_values = array();
            $sanity_idx = 0; // for sanity check
            foreach ($entry as $key => $value) {
                if ($fields[$sanity_idx++] != $key) {
                    die('Bad input data in $db->insert_batch.');
                }
                $entry_values[] = $this->get_field_value($value);
            }
            $values[] = '(' . implode(',', $entry_values) . ')';
        }

        $fields_str = implode("`,`", $fields);
        $values_str = implode(",", $values);

        $query = "INSERT INTO `$table` (`$fields_str`) VALUES $values_str $extra_suffix_sql";

        if ($res = $this->query($query)) {
            $lid = $this->get_insert_id();
            if (!$lid)
                return true;
            else
                return $lid;
        } else {
            return false;
        }

        return 1;
    }

    /**
     * release resource
     *
     * @param mixed $query_id
     */
    public function free_result($query_id = "")
    {
        if ($query_id == "") {
            $query_id = $this->last_link;
        }
        @mysqli_free_result($query_id);
    }

    /**
     * Get lastest AUTO_INCREMENT id
     *
     * @return int
     */
    public function get_insert_id()
    {
        return mysqli_insert_id($this->conn);
    }

    /**
     * Close the connection
     */
    public function close()
    {
        if (!$this->conn) {
            die('Connection not established. How to close?');
        }
        mysqli_close($this->conn);
    }

    /*#############################################
     * PRIVATE FUNCTIONS
     *############################################/
    /**
     * Return MYSQL value string.
     */
    public function get_field_value($value)
    {
        if ($value instanceof SimDBSuppExpressionType) {
            return $value->getExpression();
        } else if (is_array($value)) {
            return "'" . $this->get_escape_string(serialize($value)) . "'";
        } else {
            if (is_null($value)) {
                return "NULL";
            } else {
                return "'" . $this->get_escape_string($value) . "'";
            }
        }
    }

    /**
     * @param $field    DB column name
     * @param $value    Value to be compared on DB.
     * @param string $select    SQL select clause
     * @param $table    Table Name
     * @return mixed
     */
    public function get_by_field($field, $value, $select, $table)
    {
        return $this->query_row(sprintf('SELECT %s FROM `%s` WHERE `%s`=%s', $select, $table, $field, $this->safe_value($value)));
    }

    /**
     * @param $where    SQL Where clause
     * @param string    $select SQL Select clause
     * @param $table    Table Name
     * @return mixed
     */
    public function get_by_where($where, $select, $table)
    {
        return $this->query_row(sprintf('SELECT %s FROM `%s` WHERE %s', $select, $table, $where));
    }

    public function safe_value($val)
    {
        return $this->get_field_value($val);
    }

    public function where_like($col, $value, $full_search = false, $operator = 'AND')
    {
        if ($value !== '') {
            $value = $value . '%';
            if ($full_search)
                $value = '%' . $value;
            return sprintf(' %s %s LIKE %s', $operator, $col, $this->get_field_value($value));
        }
        return '';
    }

    /**
     * Where condition: LIKE / REGEXP
     *
     * @param $col
     * @param $value
     * @param bool $full_search
     * @param string $operator
     * @return string
     */
    public function where_like_regexp($col, $value, $full_search = false, $operator = 'AND')
    {
        $filter = $value;
        $filter_len = strlen($filter);
        if ($filter_len > 0) {
            $is_regexp_search = strpos($filter, "?") !== false || strpos($filter, "*") !== false;
            $filter = $this->get_escape_string($filter);
            if ($is_regexp_search === false) {
                $temp_where = "$col LIKE '" . ($full_search ? '%' : '') . "$filter%'";
            } else {
                $new_filter = str_replace("?", ".", $filter);
                $new_filter = str_replace("*", ".*", $new_filter);
                $new_filter = $new_filter . "$";
                $temp_where = "$col REGEXP '$new_filter'";
            }
            return ' ' . $operator . ' ' . $temp_where;
        }
        return '';
    }

    public function where_equal($col, $value, $operator = 'AND', $apply_empty_param = false)
    {
        if ($value === null)
            return sprintf(' %s %s IS NULL', $operator, $col);
        else
            if ($apply_empty_param || $value !== '')
                return sprintf(' %s %s = %s', $operator, $col, $this->safe_value($value));
    }

    /**
     *
     * @param $col
     * @param $value
     * @param string $operator
     * @param bool $apply_empty_param
     * @return string
     */
    public function where_opt($col, $value, $operator = 'AND', $apply_empty_param = false)
    {
        if ($value === null)
            return sprintf(' %s %s IS NULL', $operator, $col);
        else
            if ($apply_empty_param || $value !== '')
                return sprintf(' %s %s %s', $operator, $col, $this->safe_value($value));
    }

    public function where_in($col, $fields_array, $operator = 'AND', $apply_empty_param = false)
    {
        if (!empty($fields_array)) {
            $new = [];
            foreach ($fields_array as $value)
                $new[] = $this->safe_value($value);
            return sprintf(' %s %s IN (%s)', $operator, $col, implode(",", $new));
        } else {
            if ($apply_empty_param)
                return $operator . ' FALSE';
            return '';
        }
    }

    public function where_not_equal($col, $value, $operator = 'AND', $apply_empty_param = false)
    {
        if ($value === null)
            return sprintf(' %s %s IS NOT NULL', $operator, $col);
        else
            if ($apply_empty_param || $value !== '')
                return sprintf(' %s %s != %s', $operator, $col, $this->get_field_value($value));
    }

    public function where_by_array($data, $operator = 'AND')
    {
        if (empty($data))
            return '';

        $where = '';
        $prefix = '';
        foreach ($data as $field => $value) {
            $where .= $prefix;
            $safe_value = $this->safe_value($value);
            $where .= sprintf(' %s %s %s', $field, $safe_value == 'NULL' ? 'IS' : '=', $safe_value);
            $prefix .= ' ' . $operator;
        }
        if ($where)
            $where = '(' . $where . ')';
        return $where;
    }

    public function limit($limit_start = null, $limit = 50)
    {
        $limit_sql = "";
        if (is_null($limit) === false && is_null($limit_start) === false) {
            $limit_sql = "LIMIT $limit_start, $limit";
        } else {
            if (is_null($limit) === false) {
                $limit_sql = "LIMIT $limit";
            }
        }
        return $limit_sql;
    }

    public function set_group_concat_limit($bytes = 1024)
    {
        $sql = "SET SESSION group_concat_max_len = $bytes;";
        $this->append_query_history($sql);
        $this->query($sql);
    }

    /**
     * Log down errors found in the folder logs
     * @param $error
     */
    private function log_error($error)
    {
        $flash_msg_enabled = class_exists('\app\SysMsg\SysMsg');
        if ($flash_msg_enabled) {
            SysMsg::get_instance()->error("SQL error: {$error}");
        }

        $time = time();
        // eg: 2009_01_13_GMT_08_dberr.log
        $filename = "db_" . date("Y_m_d", $time) . '.log';
        $h = @fopen($this->LOGS_PATH . DIRECTORY_SEPARATOR . $filename, "a");
        if (!$h) {
            if (!$flash_msg_enabled) {
                print '<span style="color: red; font-weight: bold; font-size: 15px;">Database error. Log cannot be written.</span>';
            }
            return;
        }
        $CRLF = "\r\n";
        fwrite($h, date('Y-m-d H:i:s', $time) . CRLF);
        fwrite($h, $error . CRLF);
        fwrite($h, $this->last_query . CRLF);
        fwrite($h, CRLF . CRLF);

        fclose($h);
    }

    /**
     * Before insert to database add the slashes.
     */
    public function add_slashes($data)
    {
        return addslashes($data);
    }

    /**
     * Return current GMT unix timestamp
     * @return unix timestamp
     */
    function gmt_time()
    {
        $now = time();
        $tz = $this->gmt_timezone();
        $seconds = 3600 * $tz;
        return $now - $seconds;
    }

    /**
     * Return server timezone
     * @return int
     */
    function gmt_timezone()
    {
        $tz = substr(date("O", time()), 1, 2);
        return $tz;
    }

    public function get_last_query()
    {
        return $this->last_query;
    }

    /**
     * Generate the PK of the specified table.
     *
     * @param $table_name
     * @return FALSE|null
     */
    public function gen_pk($table_name)
    {
        $id = null;
        if ($table_name) {
            $id = $this->query_scalar('SELECT get_pk(' . $this->safe_value($table_name) . ')');
        }
        return $id;
    }

    public function enable_trigger()
    {
        $this->trigger_enabled = true;
    }

    public function disable_trigger()
    {
        $this->trigger_enabled = false;
    }

    public function _trigger_transaction($type, $table_name, $sql, $ref_id = null)
    {
        if (!$this->trigger_enabled)
            return;
        if (in_array($table_name, BaseModel::EXCLUDED_IN_SYNC))
            return;

        $id = $this->gen_pk(BaseModel::TBL_SYS_SYNC_TRANSACTION);
        $location_id = BaseModel::get_location_id();
        $data = [
            'id' => $id,
            'trans_type' => $type,
            'table_name' => $table_name,
            'sql' => $sql,
            'location' => $location_id,
            'created_on' => date(DATE_FORMAT_YMD_HIS),
            'created_by' => Auth::current_user_id()
        ];
        if ($ref_id)
            $data['ref_id'] = $ref_id;

        return $this->insert(BaseModel::TBL_SYS_SYNC_TRANSACTION, $data);
    }
}

class SimDBSuppExpressionType
{
    protected $exp = null;

    public function getExpression()
    {
        return $this->exp;
    }

    public function __construct($exp)
    {
        $this->exp = $exp;
    }

    public function __toString()
    {
        return $this->exp;
    }

}
