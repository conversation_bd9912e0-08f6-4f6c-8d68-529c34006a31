(()=>{"use strict";var e,t={5651:(e,t,n)=>{var r=n(6540),a=n(961);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function l(e){return function(e){if(Array.isArray(e))return f(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||d(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,l,i=[],c=!0,u=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);c=!0);}catch(e){u=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw a}}return i}}(e,t)||d(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var m={type:["Type"],date:["Date"]},p=function(e){var t=s(r.useState(e.cellData),2),n=t[0],a=t[1],o=e.isEditing,l=n.colName;r.useEffect((function(){a(e.cellData)}),[e]);var i=function(t){var n=t.target.value;a({colName:l,value:n}),e.onCellChange(l,n)},c=function(){RHelper.getColType(n.colName,m);var e=n.value;return null!=e&&"created_on"==n.colName&&(e=e.substring(0,10)),e},u=RHelper.getColType(n.colName,m),d="form-control form-control-sm fw-150"+("d"==u||"i"==u?" text-right":"")+" "+l;return r.createElement("td",null,o?r.createElement("input",{className:d,type:"text",disabled:e.loading,value:c(),onChange:i,onInput:i}):r.createElement("div",{className:n.colName+("d"==u||"i"==u?" text-right":"")},c()))},b=function(e){var t=s(r.useState(!1),2),n=t[0],a=t[1],o=s(r.useState(e.item),2),l=o[0],i=o[1],d=s(r.useState(!1),2),f=d[0],m=d[1];r.useEffect((function(){i(e.item)}),[e.item]);var b=function(e,t){var n=c(c({},l),{},u({},e,t));i(n)},y=e.rowIndex;return r.createElement("tr",{id:"tr-"+l.id},r.createElement("td",null,isNaN(y)?"":y+1),e.cols.map((function(e,t){return r.createElement(p,{key:e,cellData:{colName:e,value:e in l?l[e]:""},isEditing:f&&"created_on"!=e,loading:n,onCellChange:b})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-outline-success",onClick:function(e){e.preventDefault(),f?(a(!0),App.ajax_post(get_ajax_url("OrderDateAction","save"),{isNew:0,data:{id:l.id,type:l.type,date:l.date}},(function(e){m(!f),a(!1),i(e.data.row)}),(function(e){a(!1)}))):m(!f)},disabled:n},f?"Save":"Edit"),r.createElement("button",{className:"btn btn-sm btn-sm-td btn-light ml-2",onClick:function(t){return e.handleRowDelete(l)},disabled:n},"Delete")))},y=function(e){var t=s(r.useState(!1),2),n=t[0],a=t[1],o=s(r.useState(e.item),2),l=o[0],i=o[1];r.useEffect((function(){}),[l]),r.useEffect((function(){i(e.item),a(e.loading)}),[e]);var d=function(e,t){i(c(c({},l),{},u({},e,t)))};return r.createElement("tr",{id:"tr-"+l.id},r.createElement("td",null),e.cols.map((function(e,t){return r.createElement(p,{key:e,cellData:{colName:e,value:e in l?l[e]:""},isEditing:"created_on"!=e,loading:n,onCellChange:d})})),r.createElement("td",{style:{width:120}},r.createElement("button",{className:"btn btn-sm btn-sm-td btn-info",disabled:n,onClick:function(){var t=c({},l);e.handleRowCreate(t),i(t)}},"Create")))};function v(e){var t=e.loading,n=e.form,a=e.searchData,o=s(r.useState(n.type),2),l=o[0],i=o[1],c=s(r.useState(n.date),2),u=c[0],d=c[1],f=function(e){a({type:l,date:u})};return r.createElement("div",{className:"card border-0 bg-transparent"},r.createElement("div",{className:"card-body p-0 pt-1"},r.createElement("div",{className:"form-row"},r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"type"},"Type:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"type",id:"type",value:l,onChange:function(e){return i(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&f()}})),r.createElement("div",{className:"col-auto"},r.createElement("label",{className:"mr-1",htmlFor:"date"},"Date:"),r.createElement("input",{type:"text",className:"form-control form-control-sm w-auto d-inline-block fw-100",disabled:t,name:"date",id:"date",value:u,onChange:function(e){return d(e.target.value)},onKeyDown:function(e){is_enter_pressed(e)&&f()}})),r.createElement("div",{className:"col-auto"},r.createElement("button",{className:"btn btn-sm btn-info",onClick:f,disabled:t},"Search")))))}function g(e){var t=e.initialFormStates,n={id:0,type:"",date:""},a=s(r.useState(!0),2),o=a[0],i=a[1],u=s(r.useState(!0),2),d=u[0],f=u[1],p=s(r.useState(!1),2),g=(p[0],p[1]),h=s(r.useState(n),2),E=h[0],w=h[1],O=s(r.useState({}),2),S=O[0],j=O[1],N=s(r.useState([]),2),D=N[0],C=N[1],_=s(r.useState(""),2),x=_[0],A=_[1],k=s(r.useState(t),2),P=k[0],R=k[1];r.useEffect((function(){I(c(c({},P),{},{with:"form"}))}),[]),r.useEffect((function(){T()}),[D]),r.useEffect((function(){$("#table-order-date").floatThead({autoReflow:!0})}),[o]);var T=function(){var e={};D.map((function(t,n){e[t.id]=n})),j(e)},I=function(e){var t={class:"OrderDateAction",action:"get_list"};t=Object.assign({},t,e),App.ajax_post(get_ajax_url(),t,(function(e){g(!0),f(!1),F(e.data),i(!1)}),(function(e){g(!0)}))},F=function(e){C(l(e.rows)),A(e.sql),init_tooltip()},H=function(e){bs4pop.confirm("Are you sure you want to delete '".concat(e.type," / ").concat(e.date,"'?"),(function(t){t&&App.ajax_post(get_ajax_url(),{class:"OrderDateAction",action:"delete",id:e.id},(function(t){0==t.error&&C(D.filter((function(t){return t.id!=e.id})))}),(function(e){}))}),{title:"Delete entry"})},q=Object.keys(m);return r.createElement(r.Fragment,null,r.createElement(v,{form:P,setForm:R,loading:d,setLoading:f,searchData:function(e){f(!0),I(e)}}),r.createElement("h4",null,D?"Results (".concat(D.length," records)"):"No Results",r.createElement("button",{className:"btn-sql-view btn btn-light btn-sm"},"Show/Hide SQL")),r.createElement("div",{className:"sql-log-wrap"},r.createElement("pre",null,x)),r.createElement("div",{className:"table-wrap"},r.createElement("table",{className:"data-table editable border-0",id:"table-order-date",style:{minWidth:500}},r.createElement("thead",null,r.createElement("tr",null,r.createElement("th",null,"No"),q.map((function(e,t){return r.createElement("th",{key:e},RHelper.getColName(e,m))})),r.createElement("th",null))),r.createElement("tbody",null,r.createElement(y,{key:"new-row",item:E,cols:q,loading:d,handleRowCreate:function(e){f(!0),App.ajax_post(get_ajax_url(),{class:"OrderDateAction",action:"save",data:c({},e)},(function(e){f(!1),e.error||(C([e.data.row].concat(l(D))),w(c({},n)))}),(function(e){f(!1)}))}}),D.map((function(e,t){return r.createElement(b,{key:e.id,isTotal:!1,item:e,cols:q,rowIndex:S[e.id],loading:d,handleRowDelete:H})}))))))}var h="undefined"!=typeof ROrderDateProps?ROrderDateProps:"undefined"!=typeof App?App.get_params(window.location):{};a.render(r.createElement(g,h),document.getElementById("root"))}},n={};function r(e){var a=n[e];if(void 0!==a)return a.exports;var o=n[e]={id:e,loaded:!1,exports:{}};return t[e].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=t,e=[],r.O=(t,n,a,o)=>{if(!n){var l=1/0;for(s=0;s<e.length;s++){for(var[n,a,o]=e[s],i=!0,c=0;c<n.length;c++)(!1&o||l>=o)&&Object.keys(r.O).every((e=>r.O[e](n[c])))?n.splice(c--,1):(i=!1,o<l&&(l=o));if(i){e.splice(s--,1);var u=a();void 0!==u&&(t=u)}}return t}o=o||0;for(var s=e.length;s>0&&e[s-1][2]>o;s--)e[s]=e[s-1];e[s]=[n,a,o]},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=686,(()=>{var e={686:0};r.O.j=t=>0===e[t];var t=(t,n)=>{var a,o,[l,i,c]=n,u=0;if(l.some((t=>0!==e[t]))){for(a in i)r.o(i,a)&&(r.m[a]=i[a]);if(c)var s=c(r)}for(t&&t(n);u<l.length;u++)o=l[u],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(s)},n=self.webpackChunkWHCOrg=self.webpackChunkWHCOrg||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r.nc=void 0;var a=r.O(void 0,[96],(()=>r(5651)));a=r.O(a)})();