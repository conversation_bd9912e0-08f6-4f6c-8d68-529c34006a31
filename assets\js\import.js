var objImport = null;
$(document).ready(function (e) {
    objImport = new Import({});
    objImport.init();
});

Dropzone.autoDiscover = false;
Dropzone.myAwesomeDropzone = false;

function Import(settings) {
    const default_settings = {};
    this.settings = Object.assign({}, default_settings, settings || {});
    this.init = () => {
        $('#act-save-location').click(e => {
            const $input = $('#location_id');
            App.ajax_post_ok(get_ajax_url('BaseAction', 'save_location'), {
                location_id: $input.val()
            }, res => {
            }, $input);
        });

        if ($('#btn-backup, .backup-db').length > 0) {
            $('#btn-backup, .backup-db').click((e) => {
                e.preventDefault();
                this.backup(e);
            });
        }

        if ($('#btn-backup-transactions').length > 0) {
            $('#btn-backup-transactions').click((e) => {
                e.preventDefault();
                this.backup_transactions(e);
            });
        }
        if ($('#btn-import-transactions').length > 0) {
            $('#btn-import-transactions').click((e) => {
                e.preventDefault();
                this.import_transactions(e);
            });
        }
        if ($('#btn-import-transactions-alt').length > 0) {
            $('#btn-import-transactions-alt').click((e) => {
                e.preventDefault();
                this.import_transactions_alt(e);
            });
        }

        if ($('#btn-search-files').length > 0) {
            $('#btn-search-files').click((e) => {
                e.preventDefault();
                this.search_files(e);
            });
        }

        this.DZ = new Dropzone("#dropzone", {
            autoProcessQueue: true,
            previewTemplate: document.querySelector('#dz-tpl').innerHTML,
            parallelUploads: 1,
            timeout: 0,
            maxFiles: 1,
            maxFilesize: 100, // MB
            url: get_ajax_url('UploadAction', 'upload_sync_file'),
            addRemoveLinks: true,
            dictRemoveFile: 'Remove',
            dictInvalidFileType: 'Invalid file type.',
            removedfile: (file) => {
                var _ref;
                _ref = file.previewElement;
                if (_ref != null) {
                    if (file.accepted) {
                        App.ajax_post_ok(get_ajax_url(), {
                            class: 'UploadAction',
                            action: 'remove_sync_file',
                            fileName: file.upload.filename
                        }, (res) => {
                            _ref.parentNode.removeChild(file.previewElement);
                        }, $body);
                    } else {
                        _ref.parentNode.removeChild(file.previewElement);
                    }
                }
                return 0;
            },
            success: function (file, res, data) {
                if (!res.error) {
                    file.upload.filename = res.data.file_name;
                } else {
                    App.error(res.msg);
                }
            },
            error: function (file, response) {
                file.previewElement.classList.add("dz-error");
            }
        });
    };
    this.backup = (e) => {
        bs4pop.confirm(`Are you sure you want to backup database to download?`, function (sure) {
            if (sure) {
                App.ajax_post_ok(get_ajax_url(), {
                    class: 'BackupAction',
                    action: 'backup',
                }, function (res) {
                    if (res.error == false) {
                        window.location.href = res.data.download_url;
                    }
                }, $body);
            }
        }, {title: 'Backup Data?'});
    };
    this.backup_transactions = (e) => {
        bs4pop.confirm(`Are you sure you want to export a data for sync?`, function (sure) {
            if (sure) {
                const use_export_path = $('#use-export-path')[0].checked ? 1:0;
                App.ajax_post_ok(get_ajax_url('BackupAction', 'backup_transactions'), {
                    export_path: $('#export-path').val(),
                    use_export_path: use_export_path,
                }, function (res) {
                    if (res.error == false) {
                        if (!use_export_path)
                            window.location.href = res.data.download_url;
                        else {
                            App.success('Exported successfully.');
                            $('#export-result').html('Exported to ' + res.data.file_path);
                        }
                    }
                }, $body);
            }
        }, {title: 'Backup Data?'});
    };

    this.search_files = (e) => {
        $('#import-file-path').html('');
        App.ajax_post_ok(get_ajax_url('BackupAction', 'search_files'), {path: $('#import-path').val()}, (res) => {
            if (!res.error) {
                res.data.forEach((file) => {
                    $('#import-file-path').append(`<option value="${file}">${file}</option>`);
                });
            }
            $('#files-wrap').removeClass('d-none');
        }, $(e.target).closest('.form-group'));
    };
    this.import_transactions = (e) => {
        const acceptedFiles = this.DZ.getAcceptedFiles();
        const file_name = acceptedFiles.length > 0 ? acceptedFiles[0].upload.filename : null;

        bs4pop.confirm(`Are you sure you want to import a data for sync?`,  (sure) => {
            if (sure) {
                App.ajax_post_ok(get_ajax_url('BackupAction', 'import_transactions'), {file_name: file_name}, (res) => {
                    if (!res.error) {
                        acceptedFiles.forEach((file) => {
                            this.DZ.removeFile(file);
                        });
                    }
                }, $body);
            }
        }, {title: 'Sync Data?'});
    };
    this.import_transactions_alt = (e) => {
        const file_name = $('#import-file-path').val();

        bs4pop.confirm(`Are you sure you want to sync data from selected file?`,  (sure) => {
            if (sure) {
                App.ajax_post_ok(get_ajax_url('BackupAction', 'import_transactions'), {file_name: file_name, use_import_path: 1}, (res) => {
                    if (!res.error) {
                    }
                }, $body);
            }
        }, {title: 'Sync Data?'});
    };
}
