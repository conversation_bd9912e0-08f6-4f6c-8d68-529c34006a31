body {
    min-height: 100vh;
    padding-bottom: 20px;
    position: relative;
}

h1 {
    font-size: 1.85rem;
}

/** Data table
===================================================================================  */
table.data-table {
    border-collapse: collapse;
    margin-bottom: 20px;
}

.modal-body table.data-table {
    margin-bottom: 0px;
}

table.data-table th {
    padding: 3px 5px;
    border: 1px solid #ddd;
    font-size: 13px;
    background: #eaeaea !important;
}

table.data-table td {
    padding: 3px 5px;
    border: 1px solid #ddd;
    font-size: 12px;
}

table.data-table th {
    font-weight: bold;
    background: #fefefe;
    font-size: 13px;
}

table.inner-table {
    border-collapse: collapse !important;
}

table.inner-table td, table.inner-table th {
    padding: 5px 5px;
    border: 1px solid #eee;
}

table.inner-table th {
    font-weight: bold;
    background: #fefefe;
}

table.inner-table2 th {
    padding: 5px;
}

table.inner-table2 th {
    font-size: 0.8rem;
}

table.inner-table2 tr td:last-child, table.inner-table2 tr th:last-child {
    border-right: 0;
}

table.inner-table2 tr td:first-child, table.inner-table2 tr th:first-child {
    border-left: 0;
}

table.inner-table2 tr:last-child td {
    border-bottom: 0;
}

table.inner-table2 tr:first-child th {
    border-top: 0;
}

table.data-table td .input-edit, .btn-sm-td {
    padding: 1px 5px;
    font-size: 0.8rem;
    line-height: 0.8rem;
    border-radius: .2rem;
    width: initial;
}

table.data-table td .input-edit {
    display: inline-block;
    margin: 0;
    font-size: .675rem;
    border-radius: .2rem;
    color: #495057;
    border: 1px solid #ced4da;
}

table.data-table > tbody > tr.tr-total {
    background: #eafde4;
}

fieldset {
    padding: 0px 3px 5px;
    margin-bottom: 30px;
}

ul.result-type {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    justify-content: space-around;
}

#message {
    color: #11b311;
    font-size: 20px;
    margin-left: 250px;
    font-weight: bold;
}

#message.error {
    color: #cc0000;
}

/** Colors definition for values
===================================================================================  */
.red {
    color: #cc2200;
}

.red-f {
    color: #cc2200 !important;
}


.black {
    color: #222222;
}

.blue {
    color: #1065ff;
}

.blue-f {
    color: #1065ff !important;
}

.red-light {
    color: lightcoral;
}

.green-light {
    color: #63d863;
}

.c-lightyellow {
    color: lightyellow;
}

.c-lightgrey {
    color: lightgrey;
}

.c-lightgrey-d {
    color: #f1eded;
}

.c-yellow {
    color: #baba09;
}

.c-orange {
    color: #f9a409;
}

/** Background colors
===================================================================================  */
.light-yellow {
    background: lightyellow !important;
}

.orange {
    background: orange !important;
}

.purple {
    background: mediumpurple !important;
}

.yellow {
    background: yellow !important;
}

.light-red {
    background: #ff8686 !important;
}

.light-grey {
    background: lightgrey !important;
}

.light-blue {
    background: #d0e8f9 !important;
}

.bg-none {
    background: none !important;
}


.light-green {
    background: #bae6ba !important;
}

.light-blue2 {
    background: #edf5fa !important;
}

.light-pink {
    background: lightpink !important;
}

table.stock-buffer tr.bg-white {
    background: transparent !important;
}

label.separate {
    margin-left: 20px;
}

fieldset label {
    margin-right: 10px;
}

/** Sql log viewer
    ===================================================================================  */
.btn-sql-view {
    margin-left: 20px;
    position: relative;
}

.btn-sql-view span.badge {
    position: absolute;
    top: -2px;
}

.sql-log-wrap {
    display: none;
    height: 400px;
    margin-bottom: 20px;
    overflow-y: scroll;
    overflow-x: auto;
    border: 1px solid #aaa;
}

.sql-log-wrap pre {
    padding: 20px;
    font-size: 12px;
    height: 100%;
    overflow: initial;
}

/** Form Fieldset
    ===================================================================================  */
fieldset {
    padding: 0px 3px 5px;
    margin-bottom: 30px;
}

fieldset label {
    margin-right: 10px;
}

form fieldset {
    border: 1px solid #cccccc;
    margin-bottom: 0px;
}

fieldset input[type=radio], fieldset input[type=checkbox] {
    margin-top: 0.4rem;
}

fieldset legend {
    width: max-content;
    padding: 0px 10px;
    font-size: 0.9rem;
    margin: 0;
}

/** Footer Layout
    ===================================================================================  */
footer {
    position: absolute;
    bottom: 0px;
    width: 100%;
}

/**
Icon information
 */
i.oi-delete {
    /*vertical-align: middle;*/
    /*color: #dc3545;*/
    color: #777777;
}

i.oi-delete:hover {
    /*color: #c82333;*/
    color: #333333;
}

.oi.oi-circle-check, .oi.oi-check {
    color: green;
}

.oi.oi-circle-x, .oi.oi-x {
    color: #777;
}

.oi.oi-info {
    color: #bbbbbb;
    font-size: 0.6rem !important;
    border: 1px solid #bbbbbb;
    border-radius: 10px;
    padding: 1px 3px 1px 2px;
    margin-left: 8px;
    cursor: pointer;
}

.oi.oi-info.blue-f {
    border-color: #1065ff !important;
}

.oi.oi-info:hover {
    color: #555555;
    border: 1px solid #555555;
}

.oi.oi-file {
    font-size: 0.8rem !important;
    color: #bbbbbb;
    vertical-align: middle;
    cursor: pointer;
}

i.oi-comment-square {
    vertical-align: middle;
    color: #28a745;
}

i.oi-comment-square:hover {
    color: #1e7e34;
}

/** Icon-Edit
    ===================================================================================  */
h1 i.oi {

}

.oi.oi-edit {
    color: #bbbbbb;
    font-size: 0.7rem !important;
    cursor: pointer;
    margin-left: 8px;
}

.oi.oi-edit:hover {
    color: #555555;
}

.oi-edit-save {
    color: #bbbbbb;
    font-size: 0.8rem !important;
    cursor: pointer;
    margin-left: 5px;
}

.oi-edit-save:hover {
    color: #555555;
}

.oi-edit-cancel {
    color: #bbbbbb;
    font-size: 0.7rem !important;
    cursor: pointer;
    margin-left: 5px;
}

.edit-wrap .oi-edit-cancel:hover {
    color: #555555;
}

i.oi.green {
    color: green;
}

i.oi.grey {
    color: lightgrey;
}

i.oi.red {
    color: #dc3545;
}

/** Dropdown menu fixing for Table float header.
    ===================================================================================  */
.dropdown-menu {
    z-index: 1002;
}

/** Fixed width styling
    ===================================================================================  */
.fw-100 {
    width: 100px !important;
}

.fw-150 {
    width: 150px !important;
}

.fw-200 {
    width: 200px !important;
}

.fw-250 {
    width: 250px !important;
}

.fw-350 {
    width: 350px !important;
}

.fw-80 {
    width: 80px !important;
}

.fw-60 {
    width: 60px !important;
}

.fw-50 {
    max-width: 50px !important;
}

.fw-30 {
    max-width: 30px !important;
}


/** Auto completiion
    =============================================================  */
.autocomplete-suggestions {
    border: 1px solid #ddd;
    background: #FFF;
    overflow: auto;
    border-radius: 5px;
    width: initial !important;
    font-size: 0.9rem;
}

.autocomplete-suggestion {
    padding: 2px 5px;
    white-space: nowrap;
    overflow: hidden;
}

.autocomplete-selected {
    background: #F0F0F0;
}

.autocomplete-suggestions strong {
    font-weight: normal;
    color: #3399FF;
}

.autocomplete-group {
    padding: 2px 5px;
}

.autocomplete-group strong {
    display: block;
    border-bottom: 1px solid #000;
}


.required:after {
    content: ' *';
    color: #cc2200;
}

.fs-z8 {
    font-size: 0.8rem;
}

.fs-z6 {
    font-size: 0.6rem;
}

.cursor-p {
    cursor: pointer;
}

/** Input wrap with status icon. Used in auto completion input field
    =============================================================  */

/** Table header ordering icon
    =============================================================  */
a.icon-order {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-left: 10px;
    margin-top: 5px;
    background: url("../img/up_down.png") 20px 20px no-repeat;
}

a.icon-order.asc {
    background-position: 0 0;
}

a.icon-order.desc {
    background-position: 0 -10px;
}

th.icon-order-wrap {
    cursor: pointer;
}

/** Datepicker fixing on modal dialog
    =============================================================  */
.datepicker {
    z-index: 1051;
}

/** Vertical text mode.
    =============================================================  */
.vt {
    text-orientation: mixed;
    writing-mode: vertical-rl;
    transform: rotate(-180deg);
}

table.data-table th.sep-l, table.data-table td.sep-l {
    border-left: 2px solid #555;
}

/** Pagination.
    =============================================================  */
.pagination-wrap {
    display: flex;
    align-items: center;
}

.pagination-desc {
    margin-bottom: 1rem;
    margin-right: 1rem;
    font-size: 0.8rem;
}

.pagination .break-numbers {
    margin: 0 15px;
}

.pagination .page-item {
    cursor: pointer;
}

/** Sortable TH
 =============================================================  */
table thead th div.sortable {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}
table thead th div.sortable > span {
    /*word-break: break-all;*/
}
table thead th div.sortable > a {
    margin-left: 0;
    min-width: 10px;
}