<?php

function table_order_invoice_hs_codes($rows)
{
    $head = '<tr>';
    $head .= "<th class='text-center' width='50'>No</th>";
    $head .= "<th class='text-center' width='200'>Description</th>";
    $head .= "<th class='text-center' width='100'>HS Code</th>";
    $head .= "<th>GW</th>";
    $head .= "<th>NW</th>";
    $head .= "<th class='text-right'>Value</th>";
    $head .= "<th class='text-right'>Quantity</th>";
    $head .= "<th class='text-center'width='100'>Created on</th>";
    $head .= "</tr>";

    $totals = [
        'value' => 0,
        'quantity' => 0,
    ];
    $ind = 1;
    $body = '';
    if (!empty($rows)) {
        foreach ($rows as $row) {
            $body .= '<tr>';
            $body .= "<td class='text-center'>" . $ind++ . "</td>";
            $body .= "<td class=''>" . $row['description'] . "</td>";
            $body .= "<td class='text-center'>" . $row['hs_code'] . "</td>";
            $body .= "<td>" . $row['gw'] . "</td>";
            $body .= "<td>" . $row['nw'] . "</td>";
            $body .= "<td class='text-right'>" . nf_ze($row['value']) . "</td>";
            $body .= "<td class='text-right'>" . nf_i_ze($row['quantity']) . "</td>";
            $body .= "<td class='text-center'>" . dt_nice_dmy($row['created_on']) . "</td>";
            $body .= '</tr>';
            $totals['value'] += $row['value'];
            $totals['quantity'] += $row['quantity'];
        }
    }

    $body_total = '';
    $body_total .= '<tr class="tr-total">';
    $body_total .= "<td colspan='3'>Total</td>";
    $body_total .= "<td></td>";
    $body_total .= "<td></td>";
    $body_total .= "<td class='text-right'>" . nf_ze($totals['value']) . "</td>";
    $body_total .= "<td class='text-right'>" . nf_i_ze($totals['quantity']) . "</td>";
    $body_total .= "<td></td>";
    $body_total .= '</tr>';

    $html_tpl = '
        <table id="hs-codes" class="data-table border-0 w-100">
            <thead>%s</thead>
            <tbody>%s</tbody>    
        </table>';

    return sprintf($html_tpl, $head, $body_total . $body);
}
