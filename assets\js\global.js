const AJAX_ENDPOINT = 'action_ajax.php';
const loading_body = "<h6 class='my-5 text-center'>Loading... Please do not close dialog.</h6>";
const no_result = "<h6 class='my-5 text-center'>No result.</h6>";
const EDIT_SAVE_CANCEL = "<i class='oi oi-check oi-edit-save'></i><i class='oi oi-x oi-edit-cancel'></i>";
const editor_option_toolbar = 'styleselect | forecolor | backcolor bold | outdent indent | bullist numlist | link unlink image | code preview | fullscreen';
const editor_option_plugins = 'lists table fullscreen code advlist preview image link';

const MSG_RELOAD_PAGE = 'Invalid a request. Please reload a page and try again.';


const BTN_LABEL_SAVE = 'Save';
const BTN_LABEL_EDIT = 'Edit';
const D_PICKER_OPTIONS = {
    dateFormat: 'yyyy-mm-dd',
    language: 'de',
    autoClose: true,
    todayButton: new Date()
};

const CLS_CIRCLE_X = 'oi-circle-x';
const CLS_CIRCLE_X_RED = 'oi-circle-x red-f';
const CLS_CIRCLE_CHECK = 'oi-circle-check';

const THOUSAND_SEP = '.';
const DECIMAL_SEP = ',';

// global variables: jQuery objects
var $body = null;
var $g_dlg = null;
var $confirm_dlg = null;
var tzLocalOffset = (new Date().getTimezoneOffset()) / 60;
var tzServerOffset = 0;

$(document).ready(function () {
    var tz_html = " Local Time: " + tzLocalOffset;
    tz_html += ", " + new Date();
    $('#tzLocal').html(tz_html);

    $body = $("body");
    $g_dlg = $("#dlg");
    $confirm_dlg = $("#dlg-confirm");

    $body.on('click', ".btn-sql-view", function () {
        $(".sql-log-wrap").toggle();
        /* Get the text field */
        if ($(".sql-log-wrap").length > 0) {
            var copyText = $(".sql-log-wrap pre");
            copyToClipboard(copyText[0]);
            if ($(this).children('span').length == 0) {
                $(this).append("<span class='badge badge-warning'>SQL copied</span>");
            }
            $(this).children('span').show();
            setTimeout(function () {
                $(".btn-sql-view").children('span').slideUp('slow');
            }, 1200);
        }

        // table float-header reflow.
        const tbl = $('table.data-table');
        if (tbl.length > 0) {
            if (tbl.hasClass('floatThead-table')) {
                tbl.floatThead('reflow');
            }
        }
    });

    // Live tooltip.
    $('body').tooltip({
        selector: '[data-toggle="tooltip"], .oi.oi-edit, .oi.oi-info, .tt',
        html: true,
        sanitize: false,
    });

    init_datepicker();
    init_tooltip();
});

function init_tooltip(selector) {
    if (typeof selector == 'undefined') {
        selector = '[data-toggle="tooltip"], .oi.oi-edit, .oi.oi-info, .tt';
    }
    const obj = typeof selector == 'string' ? $(selector) : selector;
    obj.tooltip({html: true, sanitize: false});
}

function dispose_and_init_tooltip(selector) {
    if (typeof selector == 'undefined') {
        selector = '[data-toggle="tooltip"], .oi.oi-edit, .oi.oi-info, .tt';
    }
    const obj = typeof selector == 'string' ? $(selector) : selector;
    dispose_tooltip(obj);
    obj.tooltip({html: true, sanitize: false});
}

function dispose_tooltip(selector) {
    if (typeof selector == 'undefined') {
        selector = '[data-toggle="tooltip"], .oi.oi-edit, .oi.oi-info, .tt';
    }
    const obj = typeof selector == 'string' ? $(selector) : selector;
    obj.tooltip('dispose');
}

function init_datepicker(selector) {
    if (typeof selector == 'undefined') {
        selector = 'input.d-picker';
    }
    let obj = typeof selector == 'string' ? $(selector) : selector;

    if (obj.length > 0) {
        obj.datepicker({
            dateFormat: 'yyyy-mm-dd',
            language: 'de',
            autoClose: true,
            todayButton: new Date(),
            /*onSelect: function (formattedDate, date, inst) {
                $(this).change();
                $(this).hide();
            },*/
            /*onSelect: function(f,d,i){
                if(d !== i.lastVal){
                    $(this).trigger("change");
                }
            }*/
        });
        obj.each(function (ind, x) {
            const def_value = $(x).val();
            if (def_value.length == 10) {
                const d = def_value.match(/^(\d{4})-(\d{2})-(\d{2})$/);
                $(x).datepicker().data('datepicker').selectDate(new Date(d[1], d[2] - 1, d[3]));
            }
        });
    }
}

/**
 * Initialize the auto completion object.
 *
 * @param $input    Input element
 * @param data      Object: used for Ajax call.
 *
 * Selected value will be set into "selected-val" attr in $input.
 *
 */
function init_autocomplete($input, data, is_selected, cb_extra_param, cb_start_lookup, cb_select, lookup) {
    if ($input.length > 0) {
        if ($input.next('i.ac-desc').length < 1) {
            $(`<i class="oi ac-desc ml-1 ${is_selected ? CLS_CIRCLE_CHECK : CLS_CIRCLE_X_RED}" title="selection status."></i>`).insertAfter($input);
            $input.width($input.width() - 20);
            $input.addClass('d-inline-block');
            init_tooltip($input.next('i.ac-desc'));
        }

        $input.autocomplete({
            autoSelectFirst: true,
            lookup: function (query, done) {
                if (typeof cb_start_lookup === 'function') {
                    cb_start_lookup();
                }
                
                $input.attr('selected-val', '');
                $input.attr('sel', '');
                const $input_desc = $input.next('.ac-desc');
                if ($input_desc.length > 0) {
                    $input_desc.removeClass(CLS_CIRCLE_CHECK).addClass(CLS_CIRCLE_X_RED);
                }
                if (typeof lookup != 'undefined') {
                    done({suggestions: lookup.filter(x => x.value.toLowerCase().search(query.toLowerCase()) != -1)});
                    return;
                }

                // preparing ajax call.
                let post_data = Object.assign({}, data);
                post_data['keyword'] = query;
                if (typeof cb_extra_param === 'function') {
                    post_data = Object.assign({}, post_data, cb_extra_param());
                }
                App.ajax_post(get_ajax_url(), post_data, function (res) {
                    if (!res.error)
                        done({suggestions: res.data});
                    else
                        done({suggestions: []});
                }, function (res) {
                    done({suggestions: []});
                });
            },
            onSelect: function (suggestion) {
                $input.attr('selected-val', suggestion.data);
                const $input_desc = $input.next('.ac-desc');
                $input.attr('sel', '1');
                if ($input_desc.length > 0) {
                    $input_desc.addClass(CLS_CIRCLE_CHECK).removeClass(CLS_CIRCLE_X_RED);
                }
                if (typeof cb_select === 'function') {
                    cb_select(suggestion);
                }
            },
            formatResult: (suggestion, currentValue) => {
                let str = suggestion.value;
                if (str == null) return '';
                str = str.split(currentValue).join("<strong>" + currentValue + "</strong>");
                return str;
            }
        });
    }
}

/**
 * copy function.
 *
 * @param elem : Input element
 * @returns {boolean}
 */
function copyToClipboard(elem) {
    // create hidden text element, if it doesn't already exist
    var targetId = "_hiddenCopyText_";
    var isInput = elem.tagName === "INPUT" || elem.tagName === "TEXTAREA";
    var origSelectionStart, origSelectionEnd;
    if (isInput) {
        // can just use the original source element for the selection and copy
        target = elem;
        origSelectionStart = elem.selectionStart;
        origSelectionEnd = elem.selectionEnd;
    } else {
        // must use a temporary form element for the selection and copy
        target = document.getElementById(targetId);
        if (!target) {
            var target = document.createElement("textarea");
            target.style.position = "absolute";
            target.style.left = "-9999px";
            target.style.top = "0";
            target.id = targetId;
            document.body.appendChild(target);
        }
        target.textContent = elem.textContent;
    }
    // select the content
    var currentFocus = document.activeElement;
    target.focus();
    target.setSelectionRange(0, target.value.length);

    // copy the selection
    var succeed;
    try {
        succeed = document.execCommand("copy");
    } catch (e) {
        succeed = false;
    }
    // restore original focus
    if (currentFocus && typeof currentFocus.focus === "function") {
        currentFocus.focus();
    }

    if (isInput) {
        // restore prior selection
        elem.setSelectionRange(origSelectionStart, origSelectionEnd);
    } else {
        // clear temporary content
        target.textContent = "";
    }
    return succeed;
}

var App = function () {
    return {
        success: function (msg, auto_close) {
            $.toast({
                heading: 'Success',
                text: msg,
                showHideTransition: 'slide',
                position: 'top-center',
                icon: 'success',
                allowToastClose: true,
                hideAfter: (auto_close == undefined || true) ? 5000 : auto_close,
            });
        },
        error: function (msg) {
            $.toast({
                heading: 'Error',
                text: msg,
                showHideTransition: 'slide',
                position: 'top-center',
                allowToastClose: true,
                hideAfter: 15000,
                icon: 'error'
            });
        },
        info: function (msg) {
            $.toast({
                heading: 'Note',
                text: msg,
                showHideTransition: 'slide',
                position: 'top-center',
                hideAfter: 10000,
                icon: 'info'
            });
        },
        get_form_data(form) {
            let data = {};
            form.find('select, input[type=text], input[type=checkbox]:checked, input[type=radio]:checked, input[type=hidden]').each(function (ind, ele) {
                data[$(ele).attr('name')] = $(ele).val();
            });
            return data;
        },
        get_form_data_in_ele($e) {
            let data = {};
            $e.find('select, input[type=text], input[type=checkbox]:checked, input[type=radio]:checked, input[type=hidden]').each(function (ind, ele) {
                data[$(ele).attr('name')] = $(ele).val();
            });
            return data;
        },
        show_messages(res, auto_close) {
            // Show single message first.
            if (res.msg != null && res.msg.length > 0) {
                if (res.error) {
                    this.error(res.msg);
                } else {
                    this.success(res.msg, auto_close);
                }
            }

            // Show multiple messages, then.
            let list = {};
            if ('msg_list' in res && res.msg_list != null) {
                list = res.msg_list;
            } else if (res.data != null && typeof res.data == 'object' && 'msg_list' in res.data && res.data.msg_list != null) {
                list = res.data.msg_list;
            }
            for (let type in list) {
                let msg_list = list[type];
                msg_list.forEach((msg) => {
                    if (msg.length > 5) {
                        if (type == 's') {
                            this.success(msg, auto_close);
                        } else if (type == 'i') {
                            this.info(msg);
                        } else if (type == 'e') {
                            this.error(msg);
                        }
                    }
                });
            }
        },
        ajax_post(url, data, success, error) {
            $.ajax({
                url: url,
                type: 'post',
                data: data,
                dataType: 'json',
                success: function (res) {
                    App.show_messages(res, false);
                    if (success !== undefined)
                        success(res);
                },
                error: function (res, status, errorStr) {
                    App.error(status + " : " + errorStr + " <br />Details: " + res.responseText);
                    if (error !== undefined)
                        error(res, status, errorStr + res.responseText);
                }
            });
        },
        ajax_post_ok(url, data, success, block_ele, error) {
            if (block_ele) wait_icon(block_ele);
            this.ajax_post(url, data, function (res) {
                if (block_ele) hide_wait_icon(block_ele);
                if (success !== undefined)
                    success(res);
            }, function (res, status, errorStr) {
                if (block_ele) hide_wait_icon(block_ele);
                if (error !== undefined)
                    error(res, status, errorStr + res.responseText);
            });
        },
        show_dialog($dlg_ele, title, body, options, size) {
            setDlgTitle($dlg_ele, title);
            setDlgBody($dlg_ele, body);

            $dlg_ele.find(".modal-dialog").removeClass('modal-xxl');
            if (typeof size != 'undefined' && size == 'full') {
                $dlg_ele.find(".modal-dialog").addClass('modal-xxl');
            }

            $dlg_ele.modal(typeof options == 'undefined' ? {
                'backdrop': 'static',
                'show': true
            } : options);
        },
        py_format_result(suggestion, currentValue) {
            let str = suggestion.data;
            str = str.split(currentValue).join("<strong>" + currentValue + "</strong>");
            return str;
        },
        d_picker_init($ele_list) {
            if ($ele_list.length > 0) {
                $ele_list.datepicker(D_PICKER_OPTIONS);
                $ele_list.each(function (ind, obj) {
                    const def_value = $(obj).val();
                    if (def_value.length == 10) {
                        let d = def_value.match(/^(\d{4})-(\d{2})-(\d{2})$/);
                        $(obj).datepicker().data('datepicker').selectDate(new Date(parseInt(d[1]), parseInt(d[2]) - 1, parseInt(d[3])));
                    }
                });
            }
        },
        has_perm(perm_str) {
            if (_.isUndefined(userPerms)) return false;
            return userPerms.length < 1 || _.get(userPerms, perm_str, '') == 'data';
        },
        get_params: function (url) {
            var params = {};
            var query = url.search.substring(1);
            if (query.length <= 2) return params;
            var vars = query.split('&');
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split('=');
                params[pair[0]] = decodeURIComponent(pair[1]);
            }
            return params;
        },
        getNextValue: function(currentValue, values) {
            const currentInd = _.findIndex(values, x => x === currentValue);
            //console.log(currentValue, currentInd);
            if (currentInd == -1) {
                return _.get(values, '[0]', '');
            } else {
                const nextInd = (currentInd + 1) % values.length;
                return _.get(values, `[${nextInd}]`, '');
            }
        },
    }
}();

/**
 * Converting utils
 * @constructor
 */
var CvUtil = function () {
    return {
        /**
         *
         * @param string num_str    German format
         * @returns {number}
         */
        parseNumber: function (num_str) {
            if (typeof num_str == 'number') return num_str;

            let num = parseFloat(num_str
                .replace(new RegExp('\\' + THOUSAND_SEP, 'g'), '')
                .replace(new RegExp('\\' + DECIMAL_SEP), '.')
            );
            if (isNaN(num)) return 0;
            else return num;
        },
        toNumberFormat: function (value, zero_show) {
            if (value == 0) return typeof zero_show == "undefined" ? '' : 0;
            return Intl.NumberFormat('de-DE').format(value);
        },
        toDecimalFormat: function (value, zero_show) {
            if (value == 0 || _.isUndefined(value)) return _.isUndefined(zero_show) ? '' : 0;
            return number_format(value, 2, DECIMAL_SEP, THOUSAND_SEP);
        },
        toIntFormat: function (value, zero_show) {
            if (value == 0 || !value) return typeof zero_show == "undefined" ? '' : 0;
            return number_format(value, 0, DECIMAL_SEP, THOUSAND_SEP);
        },
        dtNiceDM: function (dtStr, include_time) {
            const m = moment(dtStr);
            if (m.isValid()) {
                return m.format(typeof include_time == 'undefined' ? 'DD.MM.' : 'DD.MM. HH:mm');
            } else {
                return '';
            }
        },
        dtNiceDMY: function (dtStr, include_time) {
            const m = moment(dtStr);
            if (m.isValid()) {
                return m.format(typeof include_time == 'undefined' ? 'DD.MM `YY' : 'DD.MM `YY HH:mm');
            } else {
                return '';
            }
        },
        dtNiceDMYShort: function (dtStr, include_time) {
            let m = moment(dtStr);
            if (m.isValid()) {
                m = CvUtil.fixTimezone(m);
                return m.format(typeof include_time == 'undefined' ? 'DD.MM `YY' : 'DD.MM. HH:mm');
            } else {
                return '';
            }
        },
        /**
         * Get valid YMD after applying Client Timezone.
         *
         * @param dtStr
         * @param include_time
         * @returns {string|*|string}
         */
        dtValidYMD: function (dtStr, include_time) {
            if (_.isUndefined(dtStr) || _.isEmpty(dtStr)) return '';
            let m = moment(dtStr);
            if (m.isValid()) {
                m = CvUtil.fixTimezone(m);
                return m.format(typeof include_time == 'undefined' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss');
            } else {
                return '';
            }
        },
        dtToday: function() {
            let m = moment();
            m = CvUtil.fixTimezone(m);
            return m.format('YYYY-MM-DD');
        },
        /**
         * Get valid YMD value.
         * todo: Later we should add Timezone Info.
         *
         * @param dtStr
         * @param include_time
         * @returns {null|*|string}
         */
        dtValidYMDValue: function (dtStr, include_time) {
            if(!dtStr) return null;
            let m = _.isString(dtStr) ? moment(dtStr) : dtStr;
            if (m.isValid()) {
                return m.format(typeof include_time == 'undefined' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss');
            } else {
                return null;
            }
        },
        dtAgo: function (dtStr) {
            let m = moment(dtStr);
            if (m.isValid()) {
                m = CvUtil.fixTimezone(m);
                return m.fromNow();
            }
            return dtStr;
        },
        fixTimezone: function (m) {
            return m.isValid()? m.add(-tzLocalOffset - tzServerOffset, 'hours') : null;
        },
        momentWithTz: function (dtstr) {
            let m = moment(dtstr);
            if (m.isValid()) {
                m = CvUtil.fixTimezone(m);
                return m;
            } else {
                return null;
            }
        },
    }
}();

var HtmlUtil = function () {
    return {
        dropdown: function (data, options, selected, extra) {
            let name = '';
            if (typeof data == 'string') {
                name = data;
            }
            return `
                <select name="${name}" ${extra || ' class="form-control form-control-sm"'}>
                    ${options.map(s => `<option value="${s.id}" ${s.id == selected ? ' selected="selected"' : ''} >${s.name}</option>`).join('')}
                </select>
            `;
        },
        dropdown_yes_no: function (data, selected, extra) {
            const list = HtmlUtil._get_yes_no_options();
            return HtmlUtil.dropdown(data, list, selected, extra);
        },
        radio: function (data, options, selected, extra, extra_option) {
            let name = '';
            if (typeof data == 'string') {
                name = data;
            }
            return options.map(s => `
                <div class="form-check d-inline-block">
                    <label class="form-check-label">
                        <input type="radio" name="${name}" id="${name}${s.id}" value="${s.id}" class="form-check-input" 
                        ${s.id == selected ? ' checked="checked"' : ''}                           
                        /> ${s.name}
                    </label>                    
                </div>
            `).join('');
        },
        radio_yes_no: function (data, selected, extra) {
            const list = HtmlUtil._get_yes_no_options();
            return HtmlUtil.radio(data, list, selected, extra);
        },
        _get_yes_no_options: function() {
            return [{id: 1, name: 'Yes'}, {id: 0, name: 'No'}];
        },
    }
}();

/**
 * Double with 2 digits formatter
 * Locale : Germany
 *
 * @param value
 * @returns {string}
 */
function nf_d(value) {
    if (value == 0) return '';
    return number_format(value, 2, ',', '.');
}

function nf_i(value, zero_show) {
    if (value == 0) return typeof zero_show == "undefined" ? '' : 0;
    return number_format(value, 0, ',', '.');
}

function get_ajax_sync_url() {
    return base_url + '/action_sync_tables.php';
}

function get_ajax_url(cls_name, action) {
    let url = base_url + '/action_ajax.php';
    if (typeof cls_name != 'undefined' && typeof action != 'undefined') {
        url += `?class=${cls_name}&action=${action}`
    }
    return url;
}

/**
 * Number formatting
 *
 * @param number
 * @param decimals
 * @param decPoint
 * @param thousandsSep
 * @returns {string}
 */
function number_format(number, decimals, decPoint, thousandsSep) {

    // eslint-disable-enter code hereline camelcase
    //  discuss at: http://locutus.io/php/number_format/
    // original by: Jonas Raoni Soares Silva (http://www.jsfromhell.com)
    // improved by: Kevin van Zonneveld (http://kvz.io)
    // improved by: davook
    // improved by: Brett Zamir (http://brett-zamir.me)
    // improved by: Brett Zamir (http://brett-zamir.me)
    // improved by: Theriault (https://github.com/Theriault)
    // improved by: Kevin van Zonneveld (http://kvz.io)
    // bugfixed by: Michael White (http://getsprink.com)
    // bugfixed by: Benjamin Lupton
    // bugfixed by: Allan Jensen (http://www.winternet.no)
    // bugfixed by: Howard Yeend
    // bugfixed by: Diogo Resende
    // bugfixed by: Rival
    // bugfixed by: Brett Zamir (http://brett-zamir.me)
    //  revised by: Jonas Raoni Soares Silva (http://www.jsfromhell.com)
    //  revised by: Luke Smith (http://lucassmith.name)
    //    input by: Kheang Hok Chin (http://www.distantia.ca/)
    //    input by: Jay Klehr
    //    input by: Amir Habibi (http://www.residence-mixte.com/)
    //    input by: Amirouche
    //   example 1: number_format(1234.56)
    //   returns 1: '1,235'
    //   example 2: number_format(1234.56, 2, ',', ' ')
    //   returns 2: '1 234,56'
    //   example 3: number_format(1234.5678, 2, '.', '')
    //   returns 3: '1234.57'
    //   example 4: number_format(67, 2, ',', '.')
    //   returns 4: '67,00'
    //   example 5: number_format(1000)
    //   returns 5: '1,000'
    //   example 6: number_format(67.311, 2)
    //   returns 6: '67.31'
    //   example 7: number_format(1000.55, 1)
    //   returns 7: '1,000.6'
    //   example 8: number_format(67000, 5, ',', '.')
    //   returns 8: '67.000,00000'
    //   example 9: number_format(0.9, 0)
    //   returns 9: '1'
    //  example 10: number_format('1.20', 2)
    //  returns 10: '1.20'
    //  example 11: number_format('1.20', 4)
    //  returns 11: '1.2000'
    //  example 12: number_format('1.2000', 3)
    //  returns 12: '1.200'
    //  example 13: number_format('1 000,50', 2, '.', ' ')
    //  returns 13: '100 050.00'
    //  example 14: number_format(1e-8, 8, '.', '')
    //  returns 14: '0.00000001'

    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number;
    var prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
    var sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep;
    var dec = (typeof decPoint === 'undefined') ? '.' : decPoint;
    var s = '';

    var toFixedFix = function (n, prec) {
        if (('' + n).indexOf('e') === -1) {
            return +(Math.round(n + 'e+' + prec) + 'e-' + prec)
        } else {
            var arr = ('' + n).split('e');
            var sig = '';
            if (+arr[1] + prec > 0) {
                sig = '+'
            }
            return (+(Math.round(+arr[0] + 'e' + sig + (+arr[1] + prec)) + 'e-' + prec)).toFixed(prec)
        }
    };

    s = (prec ? toFixedFix(n, prec).toString() : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }

    return s.join(dec);
}

function num_g2e(num) {
    if (typeof num == "number") return num;
    num = num.replace(/\./g, "");
    num = num.replace(/,/g, ".");
    return num;
}

function setDlgTitle($dlg, title) {
    $dlg.find(".modal-title").html(title);
}

function setDlgBody($dlg, body) {
    $dlg.find(".modal-body").html(body);
}

function wait_icon($ele, title) {
    if (typeof $ele === "undefined") {
        $ele = $('body');
    }
    const option = {
        effect: 'facebook',   //bounce, rotateplane, stretch, orbit, roundBounce, win8, win8_linear, ios, facebook, rotation, pulse, progressBar, bouncePulse, img
        text: title || 'Please waiting...',
        textPos: 'vertical',
        bg: 'rgba(255,255,255,0.7)',
        color: '#000',
        waitTime: -1,
    };

    if ($ele.hasClass('modal') && $ele.find('.modal-content').length > 0) {
        $ele.find('.modal-content').waitMe(option);
    } else {
        $ele.waitMe(option);
    }
}

function hide_wait_icon($ele) {
    if (typeof $ele === "undefined") {
        $ele = $('body');
    }
    if ($ele.length > 0 && $ele.hasClass('modal') && $ele.find('.modal-content').length > 0) {
        $ele.find('.modal-content').waitMe('hide');
    } else {
        $ele.waitMe('hide');
    }
}

function is_enter_pressed(e) {
    return ((e.which && e.which == 13) || (e.keyCode && e.keyCode == 13));
}

function is_tab_pressed(e) {
    return ((e.which && e.which == 9) || (e.keyCode && e.keyCode == 9));
}


function init_html_editors() {
    $(document).on('focusin', function (e) {
        if ($(e.target).closest(".tox-tinymce-aux, .moxman-window, .tam-assetmanager-root").length) {
            e.stopImmediatePropagation();
        }
    });

    if (typeof tinymce != 'undefined')
        tinymce.init({
            selector: '.html-editor',
            'toolbar': editor_option_toolbar,
            'plugins': editor_option_plugins
        });
}

var RightClick = {
    'sensitivity':350,
    'count':0,
    'timer':false,
    'active':function () {
        this.count++;
        this.timer = setTimeout(
            this.endCountdown.bind(this),
            this.sensitivity
        );
    },
    'endCountdown': function () {
        this.count = 0;
        this.timer = false;
    }
};


/**
 * -----------------------------------------------------------------
 * React Component helpers
 * -----------------------------------------------------------------
 */
const RHelper = function () {
    return {
        getColName: function (col, colsDefs) {
            const x = colsDefs[col];
            if (x != undefined) return x[0];
            return '';
        },
        getColType: (col, colsDefs) => {
            const x = colsDefs[col];
            if (x != undefined) return x.length > 1 ? x[1] : '';
            return null;
        },
        getAlign: (col, colsDefs) => {
            const colType = RHelper.getColType(col, colsDefs);
            switch (colType) {
                case 'd':
                case 'i':
                    return ' text-right';
                    break;
                case 'dt':
                    return ' text-center';
                    break;
                default:
                    return '';
            }
        },
        isNumeric: (col, colsDefs) => {
            const colType = RHelper.getColType(col, colsDefs);
            return colType == 'd' || colType == 'i';
        },
        isEditable: (col, colsDefs) => {
            const x = colsDefs[col];
            if (x != undefined) return x.length > 2 ? x[2] : false;
            return null;
        },
        isSortable: (col, arr) => {
            return arr.findIndex(v => col == v) > -1;
        },
        getColWidth: function (col, defs) {
            if (col in defs) return defs[col];
            return 'initial';
        },
    }
}();

String.prototype.ucfirst = function() {
    return this.charAt(0).toUpperCase() + this.slice(1)
}