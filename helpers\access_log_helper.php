<?php

function view_access_log_detail($row)
{
    $detail = $row['detail'];

    if ($detail) {
        $p = new Printer();
        $detail = $p->print($detail, '   ');
    }

    $html = '
        <div style="font-size: 0.8rem">
            <div class="row">
                <div class="col-3"><label class="font-weight-bold">Title</label></div>
                <div class="col-9"><label>' . $row['site'] . '</label></div>
            </div>
            <div class="row">
                <div class="col-3"><label class="font-weight-bold">URL</label></div>
                <div class="col-9"><label>' .$row['url'] . '</label></div>
            </div>
            <div class="row">
                <div class="col-3"><label class="font-weight-bold">Type / Method</label></div>
                <div class="col-9"><label>' . ucfirst($row['type']) . ' / ' . $row['request_method'] . '</label></div>
            </div>
            <div class="row">
                <div class="col-3"><label class="font-weight-bold">Detail</label></div>
                <div class="col-9"><pre>' . $detail . '</pre></div>
            </div>
            <div class="row">
                <div class="col-3"><label class="font-weight-bold">IP</label></div>
                <div class="col-9"><label>' . ($row['ip']) . '</label></div>
            </div>
            <div class="row">
                <div class="col-3"><label class="font-weight-bold">Created On</label></div>
                <div class="col-9"><label>' . ($row['created_at']) . '</label></div>
            </div>
        </div>
    ';
    return $html;
}